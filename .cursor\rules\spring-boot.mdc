---
description: 
globs: *.java
alwaysApply: false
---
后端项目开发规范
1. 包结构和命名
Controller: 放在 controller 包下，命名为 XxxController
Service: 放在 service 包下，命名为 XxxService
DAO: 放在 dao 包下，命名为 XxxDao，继承 MongoDao<T>
Entity: 放在 domain 模块的 entity 包下
2. 接口路径规范
使用 Constant.PRE_MAPPING_URL + "/xxx" 定义路径
通过网关访问：/platform/gam/community/v1/xxx
网关配置 StripPrefix=2 会去掉 /platform/gam
3. 分页处理规范
参数名: 使用 pageno 和 pagesize（不是 page 和 pageSize）
空值检查: 使用 ToolsKit.isEmpty() 方法
默认值: pageno 默认 "1"，pagesize 默认 "10"
分页方法: 优先使用 findPage() 而不是分别调用 findList() 和 count()
4. 方法命名规范
分页查询: 方法名使用 getPageList
返回类型: 分页查询直接返回 Page<T> 对象
5. 导入和类型使用
导入简化: 导入具体类型，如 import net.xplife.system.mongo.common.Page
避免全限定名: 导入后直接使用 Page<Dict> 而不是 net.xplife.system.mongo.common.Page<Dict>
6. 数据处理规范
ID 生成: 新增时手动生成 new ObjectId().toString()
基础字段: 设置 createtime、updatetime、status 等
状态值: 启用状态使用 "1"
7. 异常处理
继承 BaseController
使用 this.getValue() 获取参数
使用 this.returnSuccessJson() 返回成功结果
8. 查询条件构建
使用 Query 和 Criteria 构建查询条件
可选参数需要判空：if (param != null && !param.trim().isEmpty())

排序使用 Sort.by(Sort.Direction.DESC, "fieldName")