package net.xplife.system.account.dto.user;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

public class UserInfoDto implements Serializable {
    /**
     * 用户信息--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            userId;               // 用户ID
    private int               codeId;               // 用户codeId
    private String            nickName;             // 昵称
    private String            userPic;              // 用户头像
    private String            sex;                  // 性别
    private String            birthDay;             // 生日
    private int               age;                  // 年龄
    private double            currentWeight;        // 当前体重
    private double            targetWeight;         // 目标体重
    private int               height;               // 身高
    private int               isEdit;               // 是否编辑过信息 0否 1是
    private String            createDate;           // 创建时间
    private int               isFriend;             // 是否是好友
    private int               isBandedUdid;         // 是否已绑定UDID
    private int               isFollow;             // 是否关注
    private int               isOfficial;           // 是否官方账号-用户 0否1是
    private Map<String, Object> gradeLevelObj;      // 所在年级展示  id，name
    private Map<String, Object> roleObj;            // 身份        id，name
    private Map<String, Object> userTitleObj;       // 头衔        id，name，url
    protected Date            createtime;
    private int                 forbidden;          // 是否禁止，0：不禁止；1：禁止
    private String             machineInfo;                     // 用户使用设备环境数据
    public int getIsBandedUdid() {
        return isBandedUdid;
    }

    public void setIsBandedUdid(int isBandedUdid) { this.isBandedUdid = isBandedUdid; }

    public String getBirthDay() {
        return birthDay;
    }

    public void setBirthDay(String birthDay) {
        this.birthDay = birthDay;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public int getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(int isEdit) {
        this.isEdit = isEdit;
    }

    public int getCodeId() {
        return codeId;
    }

    public void setCodeId(int codeId) {
        this.codeId = codeId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getUserPic() {
        return userPic;
    }

    public void setUserPic(String userPic) {
        this.userPic = userPic;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public double getCurrentWeight() {
        return currentWeight;
    }

    public void setCurrentWeight(double currentWeight) {
        this.currentWeight = currentWeight;
    }

    public double getTargetWeight() {
        return targetWeight;
    }

    public void setTargetWeight(double targetWeight) {
        this.targetWeight = targetWeight;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getIsFriend() {
        return isFriend;
    }

    public void setIsFriend(int isFriend) {
        this.isFriend = isFriend;
    }

    public int getIsFollow() {
        return isFollow;
    }

    public void setIsFollow(int isFollow) {
        this.isFollow = isFollow;
    }

    public int getIsOfficial() {return isOfficial; }

    public void setIsOfficial(int isOfficial) { this.isOfficial = isOfficial; }

    public Map<String, Object> getGradeLevelObj() {
        return gradeLevelObj;
    }

    public void setGradeLevelObj(Map<String, Object> gradeLevelObj) {
        this.gradeLevelObj = gradeLevelObj;
    }

    public Map<String, Object> getRoleObj() {
        return roleObj;
    }

    public void setRoleObj(Map<String, Object> roleObj) {
        this.roleObj = roleObj;
    }

    public Map<String, Object> getUserTitleObj() {
        return userTitleObj;
    }

    public void setUserTitleObj(Map<String, Object> userTitleObj) {
        this.userTitleObj = userTitleObj;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public int getForbidden() {
        return forbidden;
    }

    public void setForbidden(int forbidden) {
        this.forbidden = forbidden;
    }
    public String getMachineInfo() {
        return machineInfo;
    }

    public void setMachineInfo(String machineInfo) {
        this.machineInfo = machineInfo;
    }
}
