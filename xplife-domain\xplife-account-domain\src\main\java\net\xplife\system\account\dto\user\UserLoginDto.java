package net.xplife.system.account.dto.user;
import java.io.Serializable;
import java.util.List;
public class UserLoginDto implements Serializable {
    /**
     * 用户登录信息--服务器返回
     */
    private static final long    serialVersionUID = 1L;
    private AccountStatusDto     accountStatusDto;     // 账号状态
    private List<AccountInfoDto> accountInfoDto;       // 账号信息
    private UserInfoDto          userInfoDto;          // 用户信息

    public UserLoginDto() {
    }

    public UserLoginDto(int loginStatus) {
        this.accountStatusDto = new AccountStatusDto(loginStatus);
    }

    public AccountStatusDto getAccountStatusDto() {
        return accountStatusDto;
    }

    public void setAccountStatusDto(AccountStatusDto accountStatusDto) {
        this.accountStatusDto = accountStatusDto;
    }

    public UserInfoDto getUserInfoDto() {
        return userInfoDto;
    }

    public void setUserInfoDto(UserInfoDto userInfoDto) {
        this.userInfoDto = userInfoDto;
    }

    public List<AccountInfoDto> getAccountInfoDto() {
        return accountInfoDto;
    }

    public void setAccountInfoDto(List<AccountInfoDto> accountInfoDto) {
        this.accountInfoDto = accountInfoDto;
    }
}
