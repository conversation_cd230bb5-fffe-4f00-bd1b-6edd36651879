package net.xplife.system.account.dto.user;
import java.io.Serializable;
import java.util.Map;
public class UserThirdPartyAuthDto implements Serializable {
    /**
     * 第三方授权信息dto
     */
    private static final long   serialVersionUID = 1L;
    /**
     * 用户id(唯一索引)
     */
    private String              userId;
    /**
     * 微信openID
     */
    private Map<String, String> wechatOpenId;
    /**
     * 微信unionID
     */
    private String              wechatUnionId;
    /**
     * qq openID
     */
    private Map<String, String> qqOpenId;
    /**
     * qq unionID
     */
    private String              qqUnionId;
    /**
     * 微博 unionID
     */
    private String              weiboUnionId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Map<String, String> getWechatOpenId() {
        return wechatOpenId;
    }

    public void setWechatOpenId(Map<String, String> wechatOpenId) {
        this.wechatOpenId = wechatOpenId;
    }

    public String getWechatUnionId() {
        return wechatUnionId;
    }

    public void setWechatUnionId(String wechatUnionId) {
        this.wechatUnionId = wechatUnionId;
    }

    public Map<String, String> getQqOpenId() {
        return qqOpenId;
    }

    public void setQqOpenId(Map<String, String> qqOpenId) {
        this.qqOpenId = qqOpenId;
    }

    public String getQqUnionId() {
        return qqUnionId;
    }

    public void setQqUnionId(String qqUnionId) {
        this.qqUnionId = qqUnionId;
    }

    public String getWeiboUnionId() {
        return weiboUnionId;
    }

    public void setWeiboUnionId(String weiboUnionId) {
        this.weiboUnionId = weiboUnionId;
    }
}
