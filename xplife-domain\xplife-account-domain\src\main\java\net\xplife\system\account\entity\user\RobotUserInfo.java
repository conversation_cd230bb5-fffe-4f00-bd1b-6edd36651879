package net.xplife.system.account.entity.user;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "V1_RobotUserInfo")
public class RobotUserInfo  extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_RobotUserInfo";
    public static final String USER_ID_FIELD    = "userId";
    @Indexed(name = "_userid_")
    private String             userId;                          // 账号ID

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
