package net.xplife.system.account.entity.user;
import java.util.Map;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 用户账号表
 */
@Document(collection = "V1_UserAccount")
public class UserAccount extends IdEntity {
    private static final long   serialVersionUID     = 1L;
    public static final String  COLL                 = "V1_UserAccount";
    public static final String  MOBILE_ACCOUNT_FIELD = "mobileAccount";
    public static final String  MAIL_ACCOUNT_FIELD   = "mailAccount";
    public static final String  SINAACCOUNT_FIELD    = "sinaaccount";
    public static final String  QQACCOUNT_FIELD      = "qqaccount";
    public static final String  WEIXINACCOUNT_FIELD  = "weixinaccount";
    public static final String  FBACCOUNT_FIELD      = "fbaccount";
    public static final String  TWACCOUNT_FIELD      = "twaccount";
    public static final String  APPLEACCOUNT_FIELD      = "appleaccount";
    @Indexed(name = "_mobileaccount_")
    private String              mobileAccount;                          // 手机号码
    @Indexed(name = "_mailaccount_")
    private String              mailAccount;                            // 邮箱地址
    private String              pwd;                                    // 密码
    private String              salt;                                   // 盐值
    @Indexed(name = "_sinaaccount_")
    private String              sinaaccount;                            // 新浪微博账号
    private String              sinapwd;                                // 新浪微博密码
    @Indexed(name = "_qqaccount_")
    private String              qqaccount;                              // qq账号
    private String              qqpwd;                                  // qq密码
    @Indexed(name = "_weixinaccount_")
    private String              weixinaccount;                          // 微信账号
    private String              weixinpwd;                              // 微信密码
    @Indexed(name = "_fbaccount_")
    private String              fbaccount;                              // 脸书账号
    private String              fbpwd;                                  // 脸书密码
    @Indexed(name = "_twaccount_")
    private String              twaccount;                              // 推特账号
    private String              twpwd;                                  // 推特密码
    @Indexed(name = "_appleaccount_")
    private String              appleaccount;                           // AppleID账号
    private Map<String, String> bindName;                               // 第三方绑定名称
    private String              moblieId;                               // 设备ID
    private String              deviceSystem;                           // 设备系统
    private String              udid;                                   // 苹果设备唯一ID
    private String              nationCode;                             // 国际区号

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getDeviceSystem() {
        return deviceSystem;
    }

    public void setDeviceSystem(String deviceSystem) {
        this.deviceSystem = deviceSystem;
    }

    public String getMoblieId() {
        return moblieId;
    }

    public void setMoblieId(String moblieId) {
        this.moblieId = moblieId;
    }

    public Map<String, String> getBindName() {
        return bindName;
    }

    public void setBindName(Map<String, String> bindName) {
        this.bindName = bindName;
    }

    public String getMobileAccount() {
        return mobileAccount;
    }

    public void setMobileAccount(String mobileAccount) {
        this.mobileAccount = mobileAccount;
    }

    public String getMailAccount() {
        return mailAccount;
    }

    public void setMailAccount(String mailAccount) {
        this.mailAccount = mailAccount;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getSinaaccount() {
        return sinaaccount;
    }

    public void setSinaaccount(String sinaaccount) {
        this.sinaaccount = sinaaccount;
    }

    public String getSinapwd() {
        return sinapwd;
    }

    public void setSinapwd(String sinapwd) {
        this.sinapwd = sinapwd;
    }

    public String getQqaccount() {
        return qqaccount;
    }

    public void setQqaccount(String qqaccount) {
        this.qqaccount = qqaccount;
    }

    public String getQqpwd() {
        return qqpwd;
    }

    public void setQqpwd(String qqpwd) {
        this.qqpwd = qqpwd;
    }

    public String getWeixinaccount() {
        return weixinaccount;
    }

    public void setWeixinaccount(String weixinaccount) {
        this.weixinaccount = weixinaccount;
    }

    public String getWeixinpwd() {
        return weixinpwd;
    }

    public void setWeixinpwd(String weixinpwd) {
        this.weixinpwd = weixinpwd;
    }

    public String getFbaccount() {
        return fbaccount;
    }

    public void setFbaccount(String fbaccount) {
        this.fbaccount = fbaccount;
    }

    public String getFbpwd() {
        return fbpwd;
    }

    public void setFbpwd(String fbpwd) {
        this.fbpwd = fbpwd;
    }

    public String getTwaccount() {
        return twaccount;
    }

    public void setTwaccount(String twaccount) {
        this.twaccount = twaccount;
    }

    public String getTwpwd() {
        return twpwd;
    }

    public void setTwpwd(String twpwd) {
        this.twpwd = twpwd;
    }

    public String getAppleaccount() {
        return appleaccount;
    }

    public void setAppleaccount(String appleaccount) {
        this.appleaccount = appleaccount;
    }

    public String getNationCode() {
        return nationCode;
    }

    public void setNationCode(String nationCode) {
        this.nationCode = nationCode;
    }
}
