package net.xplife.system.account.entity.user;
import java.util.Date;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
/**
 * 用户信息表
 */
@Document(collection = "V1_UserInfo")
public class UserInfo extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_UserInfo";
    public static final String USER_ID_FIELD    = "userId";
    public static final String CODE_ID_FIELD    = "codeId";
    public static final String NICK_NAME_FIELD  = "nickName";
    public static final String APP_ID_FIELD     = "appId";
    public static final String SEX_FIELD        = "sex";
    @Indexed(name = "_userid_")
    private String             userId;                          // 账号ID
    private String             nickName;                        // 昵称
    private String             userPic;                         // 用户头像
    private String             sex;                             // 性别
    private Date               birthday;                        // 生日
    private double             currentWeight;                   // 当前体重
    private double             targetWeight;                    // 目标体重
    private int                height;                          // 身高
    private String             channel;                         // 渠道
    @Indexed(name = "_codeid_")
    private int                codeId;                          // 用户codeId
    private int                isEdit;                          // 是否编辑过信息 0否 1是
    private int                isOfficial;                      // 是否是官方认证（3.2版本后，会弃用，由userTitleType替代）
    private int                gradeLevel;                      // 所在年级1--12对应小学一年级到高中三年级
    private int                role;                            // 身份 0：其他；1：学生；2：老师；3：家长
    private int                userTitleType;                   // 用户头衔 0:普通用户；1:官方认证；2:KOL认证；3:素材编辑
    private String             driverToken;                     // 友盟推送的接口，记录当前使用的机器码
    private int                forbidden;                       // 是否禁用，0：否； 1：是
    private String             machineInfo;                     // 用户使用设备环境数据
    public int getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(int isEdit) {
        this.isEdit = isEdit;
    }

    public int getCodeId() {
        return codeId;
    }

    public void setCodeId(int codeId) {
        this.codeId = codeId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getUserPic() {
        return userPic;
    }

    public void setUserPic(String userPic) {
        this.userPic = userPic;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public double getCurrentWeight() {
        return currentWeight;
    }

    public void setCurrentWeight(double currentWeight) {
        this.currentWeight = currentWeight;
    }

    public double getTargetWeight() {
        return targetWeight;
    }

    public void setTargetWeight(double targetWeight) {
        this.targetWeight = targetWeight;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getIsOfficial() { return isOfficial; }

    public void setIsOfficial(int isOfficial) { this.isOfficial = isOfficial; }

    public int getGradeLevel() {
        return gradeLevel;
    }

    public void setGradeLevel(int gradeLevel) {
        this.gradeLevel = gradeLevel;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public int getUserTitleType() {
        return userTitleType;
    }

    public void setUserTitleType(int userTitleType) {
        this.userTitleType = userTitleType;
    }

    public String getDriverToken() {
        return driverToken;
    }

    public void setDriverToken(String driverToken) {
        this.driverToken = driverToken;
    }

    public int getForbidden() {
        return forbidden;
    }

    public void setForbidden(int forbidden) {
        this.forbidden = forbidden;
    }

    public String getMachineInfo() {
        return machineInfo;
    }

    public void setMachineInfo(String machineInfo) {
        this.machineInfo = machineInfo;
    }
}
