package net.xplife.system.account.entity.user;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2024/3/20 9:04
 * @description：用户登录历史
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_UserLoginHistory")
public class UserLoginHistory extends IdEntity {
    private static final long  serialVersionUID = 1L;

    private String              userId;               // 用户ID
    private Date                latestLoginTime;      // 最后登录的时间
    private String              appVersion;           // 使用的app版本
    private String              usedDevices;          // 使用过的打印设备
    private String              phoneType;            // 手机类型

    public Date getLatestLoginTime() {
        return latestLoginTime;
    }

    public void setLatestLoginTime(Date latestLoginTime) {
        this.latestLoginTime = latestLoginTime;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getUsedDevices() {
        return usedDevices;
    }

    public void setUsedDevices(String usedDevices) {
        this.usedDevices = usedDevices;
    }

    public String getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(String phoneType) {
        this.phoneType = phoneType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}

