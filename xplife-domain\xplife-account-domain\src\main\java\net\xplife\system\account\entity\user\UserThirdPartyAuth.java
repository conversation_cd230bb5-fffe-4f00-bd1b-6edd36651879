package net.xplife.system.account.entity.user;
import java.util.Map;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 用户第三方授权信息
 */
@Document(collection = "V1_UserThirdPartyAuth")
public class UserThirdPartyAuth extends IdEntity {
    /**
     * 
     */
    private static final long   serialVersionUID      = 1L;
    public static final String  COLL                  = "V1_UserThirdPartyAuth";
    public static final String  USER_ID_FIELD         = "userId";
    public static final String  WECHAT_UNION_ID_FIELD = "wechatUnionId";
    /**
     * 用户id(唯一索引)
     */
    @Indexed(name = "_userid_")
    private String              userId;
    /**
     * 微信openID
     */
    private Map<String, String> wechatOpenId;
    /**
     * 微信unionID
     */
    @Indexed(name = "_wechatunionid_")
    private String              wechatUnionId;
    /**
     * qq openID
     */
    private Map<String, String> qqOpenId;
    /**
     * qq unionID
     */
    private String              qqUnionId;
    /**
     * 微博 unionID
     */
    private String              weiboUnionId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Map<String, String> getWechatOpenId() {
        return wechatOpenId;
    }

    public void setWechatOpenId(Map<String, String> wechatOpenId) {
        this.wechatOpenId = wechatOpenId;
    }

    public String getWechatUnionId() {
        return wechatUnionId;
    }

    public void setWechatUnionId(String wechatUnionId) {
        this.wechatUnionId = wechatUnionId;
    }

    public Map<String, String> getQqOpenId() {
        return qqOpenId;
    }

    public void setQqOpenId(Map<String, String> qqOpenId) {
        this.qqOpenId = qqOpenId;
    }

    public String getQqUnionId() {
        return qqUnionId;
    }

    public void setQqUnionId(String qqUnionId) {
        this.qqUnionId = qqUnionId;
    }

    public String getWeiboUnionId() {
        return weiboUnionId;
    }

    public void setWeiboUnionId(String weiboUnionId) {
        this.weiboUnionId = weiboUnionId;
    }
}