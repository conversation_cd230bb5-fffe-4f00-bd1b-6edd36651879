package net.xplife.system.account.entity.user;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/***
 * 用户头衔
 */
@Document(collection = "V1_UserTitleType")
public class UserTitleType extends IdEntity {
    private int     code;               //编号（需要唯一）
    private String  name;               //头衔名称
    private String  nameUrl;                //名称url地址
    private String  borderUrl;              //头衔url地址
    private int     isOfficial;         //0：表示官方；1：其他（用戶）这个头衔只能官方授权还是用户自己可以设置，为以后扩充用

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIsOfficial() {
        return isOfficial;
    }

    public void setIsOfficial(int isOfficial) {
        this.isOfficial = isOfficial;
    }

    public String getNameUrl() {
        return nameUrl;
    }

    public void setNameUrl(String nameUrl) {
        this.nameUrl = nameUrl;
    }

    public String getBorderUrl() {
        return borderUrl;
    }

    public void setBorderUrl(String borderUrl) {
        this.borderUrl = borderUrl;
    }
}
