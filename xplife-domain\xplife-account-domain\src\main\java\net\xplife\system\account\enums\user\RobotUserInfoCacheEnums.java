package net.xplife.system.account.enums.user;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum RobotUserInfoCacheEnums implements ICacheEnums {
    ROOT_USER_INFO_LIST_ID("account:robot:rm:list", "机器人用户的列表缓存");

    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (RobotUserInfoCacheEnums userAccountCacheEnums : RobotUserInfoCacheEnums.values()) {
            map.put(userAccountCacheEnums.getKey(), userAccountCacheEnums.getDesc());
        }
    }

    private RobotUserInfoCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
