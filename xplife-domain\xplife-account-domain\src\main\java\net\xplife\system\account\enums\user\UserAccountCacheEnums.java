package net.xplife.system.account.enums.user;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum UserAccountCacheEnums implements ICacheEnums {
    USER_ACCOUNT_BY_USER_ID("account:ua:by:id:", "用户账户信息对象"),
    USER_ACCOUNT_ID_BY_ACCOUNT_NAME("account:uaid:by:account:", "用户账户ID信息"),
    USER_CODEID_BY_RANDOM("account:uco:k{num}:codeid", "用户codeId"),
    USER_ACCOUNT_DELETED_BY_ACCOUNT("account:delete:by:account:", "用户7天内注销的账号"),
    USER_MANAGER_DEFAULT_ACCOUNT("account:manager:default:account",""),
    USER_MANAGER_DEFAULT_PASSWORD("account:manager:default:password",""),
    USER_MANAGER_DEFAULT_BANDID("account:manager:default:bindid","");
    
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserAccountCacheEnums userAccountCacheEnums : UserAccountCacheEnums.values()) {
            map.put(userAccountCacheEnums.getKey(), userAccountCacheEnums.getDesc());
        }
    }

    private UserAccountCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
