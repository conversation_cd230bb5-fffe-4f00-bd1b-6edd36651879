package net.xplife.system.account.enums.user;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum UserDeviceInfoCacheEnums implements ICacheEnums {
    USER_DEVICE_INFO_BY_USERID("account:ude:by:uid:", "用户设备信息对象");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserDeviceInfoCacheEnums userDeviceInfoCacheEnums : UserDeviceInfoCacheEnums.values()) {
            map.put(userDeviceInfoCacheEnums.getKey(), userDeviceInfoCacheEnums.getDesc());
        } 
    } 

    private UserDeviceInfoCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
