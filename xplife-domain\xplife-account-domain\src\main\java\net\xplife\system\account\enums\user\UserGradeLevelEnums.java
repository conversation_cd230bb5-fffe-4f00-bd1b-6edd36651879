package net.xplife.system.account.enums.user;

import java.util.HashMap;
import java.util.Map;

public enum UserGradeLevelEnums {
    ONE(1, "一年级"),
    TWO(2, "二年级"),
    THREE(3, "三年级"),
    FOUR(4, "四年级"),
    <PERSON>IVE(5, "五年级"),
    SIX(6, "六年级"),
    SEVEN(7, "初一"),
    EIGHT(8, "初二"),
    NINE(9, "初三"),
    TEN(10, "高一"),
    ELEVEN(11, "高二"),
    TWELVE(12, "高三");
    public Integer value;
    public String desc;

    UserGradeLevelEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据from获取微信的openId 类型
     *
     * @param from
     *            来源
     * @return 类型
     */
    public static String getByFrom(Integer from) {
        if (from != null) {
            for (UserGradeLevelEnums objEnums : UserGradeLevelEnums.values()) {
                if (objEnums.value.intValue()==from) {
                    return objEnums.getDesc();
                }
            }
        }
        return "";
    }

    public static Map<String, Object> getMapByValue(Integer id) {
//        if ("".equals(UserGradeLevelEnums.getByFrom(id))){
//            return null;
//        } else {
            Map<String, Object> map = new HashMap<>();
            map.put("id", id);
            map.put("name", UserGradeLevelEnums.getByFrom(id));
            return map;
//        }
    }
}
