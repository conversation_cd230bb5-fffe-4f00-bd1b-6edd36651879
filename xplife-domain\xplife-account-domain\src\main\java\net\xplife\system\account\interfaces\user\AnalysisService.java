package net.xplife.system.account.interfaces.user;
import net.xplife.system.web.core.FeignConfiguration;
import net.xplife.system.web.dto.statistics.StatisticsDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-account", configuration = FeignConfiguration.class)
public interface AnalysisService {
    /**
     * 用户注册数
     * 
     * @return
     */
    @RequestMapping(value = "/account/v1/analysis/getaccountcount", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public StatisticsDto<Long> getAccountCount(@RequestParam("startdate") String startDate, @RequestParam("enddate") String endDate);
}
