package net.xplife.system.account.interfaces.user;

import net.xplife.system.account.dto.user.*;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 微服务对外提供api服务层
 *
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-account", configuration = FeignConfiguration.class)
public interface UserAccountService {
    /**
     * 获取用户账号信息
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/login/getuseraccountinfo", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public UserAccountDto getUserAccountInfo(@RequestParam("account") String account, @RequestParam("loginway") String loginway);

    /**
     * 获取用户账号信息
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/login/getuseraccount", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public UserAccountDto getUserAccount(@RequestParam("codeid") String codeId);

    /**
     * 查询出用户信息记录
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/user/finduserinfopage", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<UserInfoDto> findUserInfoPage(@RequestParam("codeId") String codeId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate, @RequestParam("nickName") String nickName,
            @RequestParam("pageNo") int pageNo,
            @RequestParam("pageSize") int pageSize, @RequestParam("sort") String sort,
            @RequestParam("sortType") String sortType,
            @RequestParam("sex") String sex,
            @RequestParam(value = "role", required = false) Integer role,
            @RequestParam(value = "gradeLevel", required = false) Integer gradeLevel);

    /**
     * 查询用户信息
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/user/getuserinfo", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public UserLoginDto getUserInfo(@RequestParam("userid") String userId);

    /**
     * 查询用户信息
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/user/getuserinfobycodeid", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public UserLoginDto getUserInfoByCodeId(@RequestParam("codeid") String codeId);

    /**
     * 查询用户第三方授权信息
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/user/getuserthirdpartyauth", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public UserThirdPartyAuthDto getUserThirdPartyAuth(@RequestParam("userid") String userId);

    /**
     * 更新用户信息
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/user/updateuserinfo", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void updateUserInfo(@RequestParam("userId") String userId, @RequestParam("height") int height, @RequestParam("nickName") String nickName,
            @RequestParam("birthday") String birthday, @RequestParam("currentWeight") double currentWeight, @RequestParam("targetWeight") double targetWeight,
            @RequestParam("sex") String sex, @RequestParam("pic") String pic, @RequestParam("userTitleType") int userTitleType, @RequestParam("forbidden") String forbidden);

    /**
     * 更新用户头衔
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/user/updateusertitletype", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void updateUserInfo(@RequestParam("id") String id, @RequestParam("userTitleType") String userTitleType);
    /**
     * 解绑账号--只能解绑QQ、微信、新浪
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/login/unbindingaccount", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void unBindingAccount(@RequestParam("userid") String userId, @RequestParam("loginway") String loginway);

    /**
     * 随机获取机器人用户信息
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/robot/random", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public UserLoginDto getRobotUserInfoRandom();

    /**
     * 获取用户登录的一些信息
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/userloginhistory/gethistorybyuserid", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public UserLoginHistoryDto getUserLoginHistoryInfoByUserId(@RequestParam("otherid") String otherid);
}
