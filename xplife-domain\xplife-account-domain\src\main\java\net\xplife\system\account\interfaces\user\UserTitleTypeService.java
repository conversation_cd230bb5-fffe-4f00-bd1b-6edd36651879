package net.xplife.system.account.interfaces.user;
import net.xplife.system.web.core.FeignConfiguration;
import net.xplife.system.account.entity.user.UserTitleType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 微服务对外提供api服务层
 *
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-account", configuration = FeignConfiguration.class)
public interface UserTitleTypeService {
    /**
     * 获取头衔类型信息
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/titleType/findlist", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<UserTitleType> findList();


    /**
     * 更新头衔类型信息
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/titleType/updatetitletype", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void updateTitleType(@RequestParam("id") String id, @RequestParam("code") int code, @RequestParam("name") String name,@RequestParam("nameUrl") String nameUrl,@RequestParam("borderUrl") String borderUrl);

    /**
     * 删除头衔类型信息
     *
     * @return
     */
    @RequestMapping(value = "/account/v1/titleType/deltitletype", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void deleteTitleTypeById(@RequestParam("id") String id);
}
