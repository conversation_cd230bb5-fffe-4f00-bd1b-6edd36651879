package net.xplife.system.account.utils;

/**
 * Created by laotang on 2017/2/23.
 */

import net.xplife.system.web.utils.Encodes;
import net.xplife.system.web.utils.WebTools;

import java.security.MessageDigest;

public class MD5 {
    private static final String[] hexDigits = new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};

    public MD5() {
    }

    public static String byteArrayToHexString(byte[] b) {
        StringBuilder resultSb = new StringBuilder();
        byte[] arr$ = b;
        int len$ = b.length;

        for(int i$ = 0; i$ < len$; ++i$) {
            byte aB = arr$[i$];
            resultSb.append(byteToHexString(aB));
        }

        return resultSb.toString();
    }

    private static String byteToHexString(byte b) {
        int n = b;
        if(b < 0) {
            n = 256 + b;
        }

        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigits[d1] + hexDigits[d2];
    }

    public static String MD5Encode(String origin) {
        String resultString = null;

        try {
            MessageDigest e = MessageDigest.getInstance("MD5");
            resultString = byteArrayToHexString(e.digest(origin.getBytes()));
        } catch (Exception var3) {
            var3.printStackTrace();
        }

        return resultString;
    }

    public static void main(String[] args) {
        System.out.println("10aecf45203701b9f7f859f14a218897b3563a56");
        // d1c0b606e4ffc118905dc84a886c0415bc7aa4d8
        // d1c0b606e4ffc118905dc84a886c0415bc7aa4d8
        // 10aecf45203701b9f7f859f14a218897b3563a56
        String im = WebTools.buildEntryptPassword(MD5.MD5Encode("654321"), Encodes.decodeHex("8d5a4f5821a38bbb"));
        String im2 = WebTools.buildEntryptPassword("654321", Encodes.decodeHex("8d5a4f5821a38bbb"));
        String im3 = WebTools.buildEntryptPassword("c33367701511b4f6020ec61ded352059", Encodes.decodeHex("8d5a4f5821a38bbb"));
//        String im = WebTools.buildEntryptPassword(MD5.MD5Encode("654321"), Encodes.decodeHex("8d5a4f5821a38bbb"));
        System.out.println(im);
        System.out.println(im2);
        System.out.println(im3);
//        System.out.println(MD5.("654321"));
    }
}

