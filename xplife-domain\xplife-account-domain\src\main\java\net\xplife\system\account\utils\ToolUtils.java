package net.xplife.system.account.utils;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import net.xplife.system.account.dto.user.UserInfoDto;
import net.xplife.system.account.entity.user.UserInfo;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.net.URL;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class ToolUtils {
    private static PrivateKey global_privateKey;

    /**
     * 获取IP地址
     * 
     * @param request
     * @return
     */
    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ToolsKit.isEmpty(ip)) {
            ip = request.getHeader("X-Real-IP");
            if (ToolsKit.isEmpty(ip)) {
                ip = request.getRemoteHost();
            }
        }
        return ip.split(",")[0];
    }

    /**
     * 获取请求参数
     * 
     * @param request
     * @param key
     * @return
     */
    public static String getRequestValue(HttpServletRequest request, String key) {
        try {
            if (key.indexOf("[]") > -1) {
                String[] tmpArray = request.getParameterValues(key);
                StringBuilder sb = new StringBuilder();
                for (String str : tmpArray) {
                    sb.append(str + ",");
                }
                if (ToolsKit.isNotEmpty(sb)) sb.deleteCharAt(sb.length() - 1);
                return ToolsKit.isNotEmpty(sb) ? sb.toString() : "";
            }
            String values = request.getParameter(key);
            if (ToolsKit.isEmpty(values)) {
                values = ToolsKit.isEmpty(request.getAttribute(key)) ? "" : request.getAttribute(key).toString();
            }
            return values;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /***
     * 目标版本是否大于来源版本
     * @param source 来源版本
     * @param target 目标版本
     * @return true：大于等于
     * @throws Exception
     */
    public static boolean compareVersion(String source, String target){
        String[] sourceArr = source.split("\\.");
        String[] targetArr = target.split("\\.");
        int i=0;
        while (i<sourceArr.length && Integer.parseInt(sourceArr[i])==Integer.parseInt(targetArr[i])){
            i++;
        }
        if (i==sourceArr.length){
            return true;//属于大于等于
        } else {
            return Integer.parseInt(targetArr[i])>Integer.parseInt(sourceArr[i]);
        }
    }

    public static String getTextClearToken(UserInfoDto userInfo){
        try{
            PrivateKey privateKey = getPrivateKey();
            JwtBuilder builder= Jwts.builder()
                    .setId(userInfo.getUserId())             //设置唯一编号
                    .setIssuer(userInfo.getNickName())         //颁发者
                    .setSubject(userInfo.getId())        //设置主题  可以是JSON数据
                    .setIssuedAt(new Date())  //设置签发日期
                    .setExpiration(new Date(System.currentTimeMillis()+3600000))//令牌过期时间，这里1小时
                    .signWith(SignatureAlgorithm.RS256,privateKey);//设置签名 使用HS256算法，并设置SecretKey(字符串)

            //自定义载荷
//        Map<String,Object> userInfoMap = new HashMap<String,Object>();
//            userInfoMap.put("userId", userInfo.getUserId());
//            builder.setClaims(userInfoMap);
//            builder.addClaims();
            builder.claim("userId", userInfo.getUserId());

            //构建 并返回一个字符串
            String token = builder.compact();
            return token;
        } catch (Exception e) {
            throw new ServiceException("获取失败");
        }
    }

    private static PrivateKey getPrivateKey() throws IOException {
        if (global_privateKey == null) {
            // 读取 secret.txt 文件中的内容
            URL jsonFileUrl = Thread.currentThread().getContextClassLoader().getResource("textclear/secret.txt");
            String privateKeyPem = readSecretKeyFromFile(jsonFileUrl.getPath());
            if (privateKeyPem == null) {
                System.out.println("无法读取文件或文件为空");
                return null;
            }
            global_privateKey = loadPrivateKey(privateKeyPem);
            return global_privateKey;
        }
        return global_privateKey;
    }

    private static String readSecretKeyFromFile(String filePath) {
        try (
                BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line).append("\n");
            }
            return sb.toString().trim();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static PrivateKey loadPrivateKey(String pem) throws IOException {
        try {
            String privateKeyPEM = pem
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\\\n", "");
//            System.out.println(privateKeyPEM);
//            System.out.println("======================================");
            byte[] encoded = Base64.getDecoder().decode(privateKeyPEM);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encoded);
            KeyFactory kf = KeyFactory.getInstance("RSA");
            return kf.generatePrivate(keySpec);
        } catch (Exception e) {
            throw new IOException("无法解析私钥", e);
        }
    }

    public static void main(String[] args) {
        System.out.println(new Date(System.currentTimeMillis()+3600000));
    }
}
