package net.xplife.system.analysis.dto.common;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
/**
 * 请求日志
 * 
 * <AUTHOR> 2018年6月24日
 */
public class RequestLogDto implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 表ID
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createtime;
    private String            requestId;            // 请求ID
    private String            method;               // 方法
    private String            host;                 // host
    private String            remoteIp;             // 客户端真实 IP
    private String            intranetIp;           // slb的内网IP
    @JSONField(format = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date              requestDate;          // 请求时间
    private String            uri;                  // 请求uri
    private String            scheme;               // scheme
    private String            contentType;          // 请求类型
    private String            head;                 // 请求头
    private String            param;                // 请求参数
    private String            result;               // 响应结果
    private String            type;                 // 类型
    private String            userId;               // 用户ID
    private String            deviceId;             // 设备ID
    private String            uaFlag;               // ua
    private String            version;              // 版本
    private String            channel;              // 渠道
    private String            deviceSystem;         // 系统版本
    private int               year;                 // 年
    private int               month;                // 月
    private int               day;                  // 日
    private int               hour;                 // 小时
    private int               partition;            // 分区
    private String            source;               // 来源

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getDeviceSystem() {
        return deviceSystem;
    }

    public void setDeviceSystem(String deviceSystem) {
        this.deviceSystem = deviceSystem;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public int getHour() {
        return hour;
    }

    public void setHour(int hour) {
        this.hour = hour;
    }

    public int getPartition() {
        return partition;
    }

    public void setPartition(int partition) {
        this.partition = partition;
    }

    public String getUaFlag() {
        return uaFlag;
    }

    public void setUaFlag(String uaFlag) {
        this.uaFlag = uaFlag;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    public String getIntranetIp() {
        return intranetIp;
    }

    public void setIntranetIp(String intranetIp) {
        this.intranetIp = intranetIp;
    }

    public Date getRequestDate() {
        return requestDate;
    }

    public void setRequestDate(Date requestDate) {
        this.requestDate = requestDate;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getScheme() {
        return scheme;
    }

    public void setScheme(String scheme) {
        this.scheme = scheme;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
}
