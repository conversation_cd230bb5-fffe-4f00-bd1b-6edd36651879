package net.xplife.system.analysis.dto.common;
import java.io.Serializable;
import java.util.Map;
public class StatisticsInfoDto implements Serializable {
    /**
     * 统计信息暂存dto
     */
    private static final long           serialVersionUID = 1L;
    private int                         partition;            // 分区字段
    private Map<String, ChannelInfoDto> version;              // 版本

    public int getPartition() {
        return partition;
    }

    public void setPartition(int partition) {
        this.partition = partition;
    }

    public Map<String, ChannelInfoDto> getVersion() {
        return version;
    }

    public void setVersion(Map<String, ChannelInfoDto> version) {
        this.version = version;
    }
}
