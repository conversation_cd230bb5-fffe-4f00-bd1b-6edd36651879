package net.xplife.system.analysis.dto.order;
import java.io.Serializable;
public class CompanyInfoDto implements Serializable {
    /**
     * 分公司信息
     */
    private static final long serialVersionUID = 1L;
    private String            name;                 // 分公司名称
    private int               companyNum;           // 公司数量
    private int               personNum;            // 店长数量
    private String            colour;               // 颜色
    private double            totalNum;             // 数额

    public CompanyInfoDto() {
        super();
    }

    public CompanyInfoDto(String name, int companyNum, int personNum) {
        super();
        this.name = name;
        this.companyNum = companyNum;
        this.personNum = personNum;
    }

    public CompanyInfoDto(String name, String colour, double totalNum) {
        super();
        this.name = name;
        this.colour = colour;
        this.totalNum = totalNum;
    }

    public double getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(double totalNum) {
        this.totalNum = totalNum;
    }

    public String getColour() {
        return colour;
    }

    public void setColour(String colour) {
        this.colour = colour;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCompanyNum() {
        return companyNum;
    }

    public void setCompanyNum(int companyNum) {
        this.companyNum = companyNum;
    }

    public int getPersonNum() {
        return personNum;
    }

    public void setPersonNum(int personNum) {
        this.personNum = personNum;
    }
}
