package net.xplife.system.analysis.dto.order;
import java.io.Serializable;
import java.util.Date;
public class GoodsSalesDto implements Serializable {
    /**
     * 商品销量dto
     */
    private static final long serialVersionUID = 1L;
    private String            appId;                // 渠道ID
    private String            consignmentId;        // 商家ID
    private String            productId;            // 商品ID
    private String            name;                 // 商品名称
    private double            salesAmount;          // 销售金额
    private Date              analysisDate;         // 统计时间

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getConsignmentId() {
        return consignmentId;
    }

    public void setConsignmentId(String consignmentId) {
        this.consignmentId = consignmentId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public double getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(double salesAmount) {
        this.salesAmount = salesAmount;
    }

    public Date getAnalysisDate() {
        return analysisDate;
    }

    public void setAnalysisDate(Date analysisDate) {
        this.analysisDate = analysisDate;
    }
}
