package net.xplife.system.analysis.dto.order;
import java.io.Serializable;
import java.util.List;
public class GoodsSalesTopDto implements Serializable {
    /**
     * 商品销售排行榜dto
     */
    private static final long serialVersionUID = 1L;
    private String            name;                 // 商品名称
    private int               count;                // 数量
    private String            company;              // 公司名称
    private String            consignmentName;      // 商家名称
    private List<String>      contacts;             // 联系人信息
    private double            costPrice;            // 成本价格

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getConsignmentName() {
        return consignmentName;
    }

    public void setConsignmentName(String consignmentName) {
        this.consignmentName = consignmentName;
    }

    public List<String> getContacts() {
        return contacts;
    }

    public void setContacts(List<String> contacts) {
        this.contacts = contacts;
    }

    public double getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(double costPrice) {
        this.costPrice = costPrice;
    }
}
