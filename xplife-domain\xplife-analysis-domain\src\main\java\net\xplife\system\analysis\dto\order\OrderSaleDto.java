package net.xplife.system.analysis.dto.order;
import java.io.Serializable;
import java.util.List;
public class OrderSaleDto implements Serializable {
    /**
     * 订单销售信息dto
     */
    private static final long    serialVersionUID = 1L;
    private double               yesterdaySale;        // 昨日销售额
    private double               bYesterdaySale;       // 前日销售额
    private double               yesterdayProfit;      // 昨日利润
    private double               bYesterdayProfit;     // 前日利润
    private int                  yesterdayOrder;       // 昨日订单数
    private int                  bYesterdayOrder;      // 前日订单数
    private List<CompanyInfoDto> companyInfo;          // 分公司店铺信息

    public OrderSaleDto() {
        super();
    }

    public OrderSaleDto(double yesterdaySale, double bYesterdaySale, double yesterdayProfit, double bYesterdayProfit, int yesterdayOrder, int bYesterdayOrder) {
        super();
        this.yesterdaySale = yesterdaySale;
        this.bYesterdaySale = bYesterdaySale;
        this.yesterdayProfit = yesterdayProfit;
        this.bYesterdayProfit = bYesterdayProfit;
        this.yesterdayOrder = yesterdayOrder;
        this.bYesterdayOrder = bYesterdayOrder;
    }

    public double getYesterdayOrder() {
        return yesterdayOrder;
    }

    public void setYesterdayOrder(int yesterdayOrder) {
        this.yesterdayOrder = yesterdayOrder;
    }

    public double getbYesterdayOrder() {
        return bYesterdayOrder;
    }

    public void setbYesterdayOrder(int bYesterdayOrder) {
        this.bYesterdayOrder = bYesterdayOrder;
    }

    public double getYesterdaySale() {
        return yesterdaySale;
    }

    public void setYesterdaySale(double yesterdaySale) {
        this.yesterdaySale = yesterdaySale;
    }

    public double getbYesterdaySale() {
        return bYesterdaySale;
    }

    public void setbYesterdaySale(double bYesterdaySale) {
        this.bYesterdaySale = bYesterdaySale;
    }

    public double getYesterdayProfit() {
        return yesterdayProfit;
    }

    public void setYesterdayProfit(double yesterdayProfit) {
        this.yesterdayProfit = yesterdayProfit;
    }

    public double getbYesterdayProfit() {
        return bYesterdayProfit;
    }

    public void setbYesterdayProfit(double bYesterdayProfit) {
        this.bYesterdayProfit = bYesterdayProfit;
    }

    public List<CompanyInfoDto> getCompanyInfo() {
        return companyInfo;
    }

    public void setCompanyInfo(List<CompanyInfoDto> companyInfo) {
        this.companyInfo = companyInfo;
    }
}
