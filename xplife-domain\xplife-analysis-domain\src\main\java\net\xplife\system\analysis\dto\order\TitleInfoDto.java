package net.xplife.system.analysis.dto.order;
import java.io.Serializable;
import java.util.List;
public class TitleInfoDto implements Serializable {
    /**
     * 标题信息
     */
    private static final long    serialVersionUID = 1L;
    private List<CompanyInfoDto> companyInfo;          // 公司信息
    private double               total;                // 总计
    private double               average;              // 平均数

    public List<CompanyInfoDto> getCompanyInfo() {
        return companyInfo;
    }

    public void setCompanyInfo(List<CompanyInfoDto> companyInfo) {
        this.companyInfo = companyInfo;
    }

    public double getTotal() {
        return total;
    }

    public void setTotal(double total) {
        this.total = total;
    }

    public double getAverage() {
        return average;
    }

    public void setAverage(double average) {
        this.average = average;
    }
}
