package net.xplife.system.analysis.entity.area;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.AnalyEntity;
/**
 * 区域日志
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "DW_AreaLog")
public class AreaLog extends AnalyEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "DW_AreaLog";
    public static final String USER_ID_FIELD    = "userId";
    private String             country;                        // 国
    private String             province;                       // 省
    private String             city;                           // 市
    @Indexed(name = "_userid_")
    private String             userId;                         // 用户ID
    private String             deviceId;                       // 设备ID

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
}
