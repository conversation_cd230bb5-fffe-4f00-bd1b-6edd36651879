package net.xplife.system.analysis.entity.area;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.AnalyEntity;
/**
 * 区域汇总
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "DM_AreaStatistics")
public class AreaStatistics extends AnalyEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "DM_AreaStatistics";
    private String             country;                               // 国
    private String             province;                              // 省
    private String             city;                                  // 市
    private int                count;                                 // 数量

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
