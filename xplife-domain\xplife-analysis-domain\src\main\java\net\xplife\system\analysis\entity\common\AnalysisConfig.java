package net.xplife.system.analysis.entity.common;
import java.util.List;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 统计配置
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "AnalysisConfig")
public class AnalysisConfig extends IdEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "AnalysisConfig";
    private String             remark;                             // 描述
    private String             uri;                                // 请求uri
    private List<String>       conditionField;                     // 条件key字段
    private List<Object>       conditionValue;                     // 条件值字段
    private String             cacheKey;                           // 缓存key
    private List<String>       queryKey;                           // 过滤key
    private List<String>       queryVal;                           // 过滤值
    private String             saveDaoName;                        // 存储dao实例名称
    private String             entityName;                         // 实体名称
    private String             queryType;                          // 查询类型

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    public List<String> getQueryVal() {
        return queryVal;
    }

    public void setQueryVal(List<String> queryVal) {
        this.queryVal = queryVal;
    }

    public String getCacheKey() {
        return cacheKey;
    }

    public void setCacheKey(String cacheKey) {
        this.cacheKey = cacheKey;
    }

    public List<String> getQueryKey() {
        return queryKey;
    }

    public void setQueryKey(List<String> queryKey) {
        this.queryKey = queryKey;
    }

    public String getSaveDaoName() {
        return saveDaoName;
    }

    public void setSaveDaoName(String saveDaoName) {
        this.saveDaoName = saveDaoName;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public List<String> getConditionField() {
        return conditionField;
    }

    public void setConditionField(List<String> conditionField) {
        this.conditionField = conditionField;
    }

    public List<Object> getConditionValue() {
        return conditionValue;
    }

    public void setConditionValue(List<Object> conditionValue) {
        this.conditionValue = conditionValue;
    }
}
