package net.xplife.system.analysis.entity.common;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 落盘数据日志
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "DisksDataLog")
public class DisksDataLog extends IdEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "DisksDataLog";
    @Indexed(name = "_scheduleid_")
    private String             scheduleId;                       // 定时任务ID
    private int                type;                             // 定时任务类型
    private String             remark;                           // 描述
    private int                year;                             // 年
    private int                month;                            // 月
    private int                day;                              // 日志
    @Indexed(name = "_partition_")
    private int                partition;                        // 分区字段

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(String scheduleId) {
        this.scheduleId = scheduleId;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public int getPartition() {
        return partition;
    }

    public void setPartition(int partition) {
        this.partition = partition;
    }
}
