package net.xplife.system.analysis.entity.common;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 清洗配置
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "EtlConfig")
public class EtlConfig extends IdEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "EtlConfig";
    private String             remark;                        // 描述
    private String             instantName;                   // 实例名称

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInstantName() {
        return instantName;
    }

    public void setInstantName(String instantName) {
        this.instantName = instantName;
    }
}
