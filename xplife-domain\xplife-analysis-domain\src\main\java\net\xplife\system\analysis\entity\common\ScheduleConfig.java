package net.xplife.system.analysis.entity.common;
import java.util.List;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 定时任务配置
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "ScheduleConfig")
public class ScheduleConfig extends IdEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "ScheduleConfig";
    private String             remark;                             // 描述
    private String             uniqueField;                        // 唯一字段
    private List<String>       conditionField;                     // 条件key字段
    private List<Object>       conditionValue;                     // 条件值字段
    private String             cacheKey;                           // 缓存key
    private String             queryDaoName;                       // 查询dao实例名称
    private List<String>       resultKey;                          // 结果key
    private String             queryType;                          // 参数查询类型
    private String             saveDaoName;                        // 存储dao实例名称
    private String             entityName;                         // 实体名称

    public String getQueryDaoName() {
        return queryDaoName;
    }

    public void setQueryDaoName(String queryDaoName) {
        this.queryDaoName = queryDaoName;
    }

    public String getSaveDaoName() {
        return saveDaoName;
    }

    public void setSaveDaoName(String saveDaoName) {
        this.saveDaoName = saveDaoName;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    public List<String> getResultKey() {
        return resultKey;
    }

    public void setResultKey(List<String> resultKey) {
        this.resultKey = resultKey;
    }

    public List<String> getConditionField() {
        return conditionField;
    }

    public void setConditionField(List<String> conditionField) {
        this.conditionField = conditionField;
    }

    public List<Object> getConditionValue() {
        return conditionValue;
    }

    public void setConditionValue(List<Object> conditionValue) {
        this.conditionValue = conditionValue;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUniqueField() {
        return uniqueField;
    }

    public void setUniqueField(String uniqueField) {
        this.uniqueField = uniqueField;
    }

    public String getCacheKey() {
        return cacheKey;
    }

    public void setCacheKey(String cacheKey) {
        this.cacheKey = cacheKey;
    }
}
