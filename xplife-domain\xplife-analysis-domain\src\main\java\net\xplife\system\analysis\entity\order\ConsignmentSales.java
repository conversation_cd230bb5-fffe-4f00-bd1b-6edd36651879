package net.xplife.system.analysis.entity.order;
import java.util.Date;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.AnalyEntity;
/**
 * 商家销量统计--每天每店铺
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "DW_ConsignmentSales")
public class ConsignmentSales extends AnalyEntity {
    /**
     * 
     */
    private static final long  serialVersionUID     = 1L;
    public static final String COLL                 = "DW_ConsignmentSales";
    public static final String CONSIGNMENT_ID_FIELD = "consignmentId";
    public static final String ANALYSIS_DATE_FIELD  = "analysisDate";
    @Indexed(name = "_consignment_id_")
    private String             consignmentId;                               // 商家ID
    private String             name;                                        // 商家名称
    private double             salesAmount;                                 // 销售金额
    @Indexed(name = "_analysisdate_")
    private Date               analysisDate;                                // 统计时间

    public String getConsignmentId() {
        return consignmentId;
    }

    public void setConsignmentId(String consignmentId) {
        this.consignmentId = consignmentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public double getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(double salesAmount) {
        this.salesAmount = salesAmount;
    }

    public Date getAnalysisDate() {
        return analysisDate;
    }

    public void setAnalysisDate(Date analysisDate) {
        this.analysisDate = analysisDate;
    }
}
