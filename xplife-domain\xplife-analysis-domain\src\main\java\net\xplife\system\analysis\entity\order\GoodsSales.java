package net.xplife.system.analysis.entity.order;
import java.util.Date;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.AnalyEntity;
/**
 * 单品销售统计--每天每商品
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "DW_GoodsSales")
public class GoodsSales extends AnalyEntity {
    /**
     * 
     */
    private static final long  serialVersionUID     = 1L;
    public static final String COLL                 = "DW_GoodsSales";
    public static final String CONSIGNMENT_ID_FIELD = "consignmentId";
    public static final String PRODUCT_ID_FIELD     = "productId";
    public static final String ANALYSIS_DATE_FIELD  = "analysisDate";
    @Indexed(name = "_consignment_id_")
    private String             consignmentId;                         // 商家ID
    private String             productId;                             // 商品ID
    private String             name;                                  // 商品名称
    private int                salesCount;                            // 销售数量
    @Indexed(name = "_analysisdate_")
    private Date               analysisDate;                          // 统计时间

    public String getConsignmentId() {
        return consignmentId;
    }

    public void setConsignmentId(String consignmentId) {
        this.consignmentId = consignmentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public int getSalesCount() {
        return salesCount;
    }

    public void setSalesCount(int salesCount) {
        this.salesCount = salesCount;
    }

    public Date getAnalysisDate() {
        return analysisDate;
    }

    public void setAnalysisDate(Date analysisDate) {
        this.analysisDate = analysisDate;
    }
}
