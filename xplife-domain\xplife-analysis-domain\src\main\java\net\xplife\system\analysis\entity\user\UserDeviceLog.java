package net.xplife.system.analysis.entity.user;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.AnalyEntity;
/**
 * 用户设备日志
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "DW_UserDeviceLog")
public class UserDeviceLog extends AnalyEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "DW_UserDeviceLog";
    public static final String USER_ID_FIELD    = "userId";
    @Indexed(name = "_userid_")
    private String             userId;                               // 用户ID
    private String             deviceId;                             // 设备ID

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
}
