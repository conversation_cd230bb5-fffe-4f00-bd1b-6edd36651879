package net.xplife.system.analysis.entity.user;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.AnalyEntity;
/**
 * 用户设备日志汇总
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "DM_UserDeviceStatistics")
public class UserDeviceStatistics extends AnalyEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "DM_UserDeviceStatistics";
    private int                count;                                       // 数量

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
