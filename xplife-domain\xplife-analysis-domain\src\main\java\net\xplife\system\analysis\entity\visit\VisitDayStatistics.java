package net.xplife.system.analysis.entity.visit;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.AnalyEntity;
/**
 * 访问汇总
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "DM_VisitDayStatistics")
public class VisitDayStatistics extends AnalyEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "DM_VisitDayStatistics";
    private int                count;                                     // 数量

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
