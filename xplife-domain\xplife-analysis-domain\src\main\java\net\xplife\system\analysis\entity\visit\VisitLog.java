package net.xplife.system.analysis.entity.visit;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.AnalyEntity;
/**
 * 访问日志
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "DW_VisitLog")
public class VisitLog extends AnalyEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "DW_VisitLog";
    private String             uri;                             // 请求uri
    @Indexed(name = "_userid_")
    private String             userId;                          // 用户ID
    private String             deviceId;                        // 设备ID
    private String             param;                           // 请求参数

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
}
