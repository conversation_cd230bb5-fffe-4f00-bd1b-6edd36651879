package net.xplife.system.analysis.entity.visit;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.AnalyEntity;
/**
 * 访问量汇总
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "DM_VisitUrlStatistics")
public class VisitUrlStatistics extends AnalyEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "DM_VisitUrlStatistics";
    private int                count;                                     // 数量
    private String             uri;                                       // 地址

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
