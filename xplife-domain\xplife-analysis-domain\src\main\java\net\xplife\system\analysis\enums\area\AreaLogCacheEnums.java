package net.xplife.system.analysis.enums.area;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum AreaLogCacheEnums implements ICacheEnums {
    AREA_LOG_BY_USER_ID("analy:al:by:uid:", "区域日志对象");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (AreaLogCacheEnums areaLogCacheEnums : AreaLogCacheEnums.values()) {
            map.put(areaLogCacheEnums.getKey(), areaLogCacheEnums.getDesc());
        }
    }

    private AreaLogCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
