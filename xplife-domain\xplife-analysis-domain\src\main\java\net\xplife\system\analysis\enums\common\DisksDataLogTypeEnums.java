package net.xplife.system.analysis.enums.common;
import java.util.LinkedHashMap;
/**
 * 落盘数据日志类型枚举
 */
public enum DisksDataLogTypeEnums {
    SCHEDULE_CONFIG(0, "定时任务"), 
    ANALYSIS(1, "统计任务");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (DisksDataLogTypeEnums disksDataLogTypeEnums : DisksDataLogTypeEnums.values()) {
            map.put(disksDataLogTypeEnums.getValue(), disksDataLogTypeEnums.getDesc());
        }
    }

    DisksDataLogTypeEnums(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
