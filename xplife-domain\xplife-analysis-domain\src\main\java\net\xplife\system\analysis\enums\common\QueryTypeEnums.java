package net.xplife.system.analysis.enums.common;
import java.util.LinkedHashMap;
/**
 * 查询类型枚举
 */
public enum QueryTypeEnums {
    QUERY("query", "query"), 
    IN("in", "query");
    private final String                               value;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<>();
        for (QueryTypeEnums queryTypeEnums : QueryTypeEnums.values()) {
            map.put(queryTypeEnums.getValue(), queryTypeEnums.getDesc());
        }
    }
 
    QueryTypeEnums(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
