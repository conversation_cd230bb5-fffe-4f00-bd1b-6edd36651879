package net.xplife.system.analysis.enums.common;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum StatisticsCacheEnums implements ICacheEnums {
    COMMON_FLAG_BY("analy:flg:by:", "公共缓存标识");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (StatisticsCacheEnums statisticsCacheEnums : StatisticsCacheEnums.values()) {
            map.put(statisticsCacheEnums.getKey(), statisticsCacheEnums.getDesc());
        }
    } 

    private StatisticsCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
