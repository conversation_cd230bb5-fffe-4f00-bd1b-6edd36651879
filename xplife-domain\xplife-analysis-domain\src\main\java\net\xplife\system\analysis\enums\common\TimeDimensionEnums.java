package net.xplife.system.analysis.enums.common;
import java.util.LinkedHashMap;
/**
 * 时间维度枚举
 */
public enum TimeDimensionEnums {
    DAY("day", "天"), 
    HOUR("hour", "小时");
    private final String                               value;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<>();
        for (TimeDimensionEnums timeDimensionEnums : TimeDimensionEnums.values()) {
            map.put(timeDimensionEnums.getValue(), timeDimensionEnums.getDesc());
        }
    }

    TimeDimensionEnums(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
