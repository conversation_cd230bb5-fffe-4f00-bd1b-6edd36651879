package net.xplife.system.analysis.enums.order;
import java.util.LinkedHashMap;
/**
 * 查询参数枚举
 * 
 * <AUTHOR> 2018年11月15日
 */
public enum SearchQueryEnum {
    CONSIGNMENT_ID("consignmentId", "商家ID"),
    START_DATE("startDate", "开始时间"),
    END_DATE("endDate", "结束时间"),
    START_DATE2("startDate2", "开始时间"),
    END_DATE2("endDate2", "结束时间"),
    TYPE("type", "类型"),
    COMPANY("company", "公司"),
    EXTENSION("extension", "推广费");
    
    
    private final String                                value;
    private final String                                desc;
    private static final LinkedHashMap<String, String>  map;
    static {
        map = new LinkedHashMap<>();
        for (SearchQueryEnum searchQueryEnum : SearchQueryEnum.values()) {
            map.put(searchQueryEnum.getValue(), searchQueryEnum.getDesc());
        }
    }

    SearchQueryEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
    
    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
