package net.xplife.system.analysis.enums.user;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum UserDeviceLogCacheEnums implements ICacheEnums {
    USER_DEVICE_LOG_BY_USER_ID("analy:udl:by:uid:", "用户设备日志对象");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserDeviceLogCacheEnums userDeviceLogCacheEnums : UserDeviceLogCacheEnums.values()) {
            map.put(userDeviceLogCacheEnums.getKey(), userDeviceLogCacheEnums.getDesc());
        }
    }

    private UserDeviceLogCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
