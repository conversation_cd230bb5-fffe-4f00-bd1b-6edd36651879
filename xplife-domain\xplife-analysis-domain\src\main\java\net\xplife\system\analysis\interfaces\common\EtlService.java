package net.xplife.system.analysis.interfaces.common;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import net.xplife.system.analysis.dto.common.RequestLogDto;
import net.xplife.system.web.core.FeignConfiguration;
/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-analysis", configuration = FeignConfiguration.class)
public interface EtlService {
    /**
     * 清洗日志
     * 
     * @return
     */
    @RequestMapping(value = "/analysis/v1/etl/etllogs", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void etlLogs(@RequestBody RequestLogDto requestLogDto);
}
