package net.xplife.system.analysis.interfaces.order;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import net.xplife.system.analysis.dto.order.AnalysisOrderDto;
import net.xplife.system.web.core.FeignConfiguration;
/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-analysis", configuration = FeignConfiguration.class)
public interface BusinessAnalysisService {
    /**
     * 订单统计分析
     * 
     * @return
     */
    @RequestMapping(value = "/analysis/v1/schedule/analysisorder", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void analysisOrder(@RequestBody AnalysisOrderDto analysisOrderDto);
}
