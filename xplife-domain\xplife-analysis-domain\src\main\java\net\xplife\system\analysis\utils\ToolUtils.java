package net.xplife.system.analysis.utils;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.mongo.common.AnalyEntity;
import net.xplife.system.tools.util.core.ToolsKit;
public class ToolUtils {
    private final static int[]    dayArr           = new int[] { 20, 19, 21, 20, 21, 22, 23, 23, 23, 24, 23, 22};
    private final static String[] constellationArr = new String[] { "摩羯座", "水瓶座", "双鱼座", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "摩羯座"};
    private final static String[] years            = new String[] { "鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"};

    /**
     * 通过生日计算星座
     * 
     * @param month
     * @param day
     * @return
     */
    public static String getConstellation(int month, int day) {
        return day < dayArr[month - 1] ? constellationArr[month - 1] : constellationArr[month];
    }

    /**
     * 通过生日计算属相
     * 
     * @param year
     * @return
     */
    public static String getYear(int year) {
        if (year < 1900) {
            return "未知";
        }
        int start = 1900;
        return years[(year - start) % years.length];
    }

    /**
     * 是否更新了基础类信息
     * 
     * @param deviceSystem
     *            设备系统
     * @param version
     *            版本
     * @param channel
     *            渠道
     * @param analyEntity
     *            基础类
     * @param isSave
     * @return
     */
    public static boolean isUpdateInfo(String deviceSystem, String version, String channel, AnalyEntity analyEntity, boolean isSave) {
        if (ToolsKit.isNotEmpty(deviceSystem) && !deviceSystem.equals(analyEntity.getDeviceSystem())) {
            analyEntity.setDeviceSystem(deviceSystem);
            isSave = true;
        } else if (ToolsKit.isEmpty(deviceSystem)) {
            analyEntity.setDeviceSystem(ToolsConst.DEVICE_SYSTEM_ANDROID);
            isSave = true;
        }
        if (ToolsKit.isNotEmpty(version) && !version.equals(analyEntity.getVersion())) {
            analyEntity.setVersion(version);
            isSave = true;
        } else if (ToolsKit.isEmpty(version)) {
            analyEntity.setVersion(ToolsConst.DEFAULT_APP_VERSION);
            isSave = true;
        }
        if (ToolsKit.isNotEmpty(channel) && !channel.equals(analyEntity.getChannel())) {
            analyEntity.setChannel(channel);
            isSave = true;
        } else if (ToolsKit.isEmpty(channel)) {
            analyEntity.setChannel(ToolsConst.DEFAULT_CHANNEL);
            isSave = true;
        }
        return isSave;
    }
}
