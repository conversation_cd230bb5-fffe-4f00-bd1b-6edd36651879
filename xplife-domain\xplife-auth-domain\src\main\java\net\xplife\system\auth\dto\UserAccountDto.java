package net.xplife.system.auth.dto;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import net.xplife.system.web.dto.RoleDto;
public class UserAccountDto implements Serializable {
    /**
     * 用户账号dto
     */
    private static final long          serialVersionUID = 1L;
    private String                     id;                   // 记录ID
    private String                     name;                 // 用户名称
    private String                     account;              // 用户账号
    private String                     assAttribute;         // 关联属性
    private Map<String, List<RoleDto>> roleList;             // 角色列表
    private String                     projectName;          // 项目名称

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAssAttribute() {
        return assAttribute;
    }

    public void setAssAttribute(String assAttribute) {
        this.assAttribute = assAttribute;
    }

    public Map<String, List<RoleDto>> getRoleList() {
        return roleList;
    }

    public void setRoleList(Map<String, List<RoleDto>> roleList) {
        this.roleList = roleList;
    }
}
