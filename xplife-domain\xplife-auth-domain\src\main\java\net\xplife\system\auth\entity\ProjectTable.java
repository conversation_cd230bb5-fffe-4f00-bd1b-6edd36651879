package net.xplife.system.auth.entity;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
@Document(collection = "V1_ProjectTable")
public class ProjectTable extends IdEntity {
    /**
     * 项目表
     */
    private static final long  serialVersionUID    = 1L;
    public static final String COLL                = "V1_ProjectTable";
    public static final String ASS_ATTRIBUTE_FIELD = "assAttribute";
    private String             projectName;                            // 项目名称
    private String             projectCode;                            // 项目编号
    private String             enterpriseCode;                         // 企业编号
    private int                isUser;                                 // 是否能创建用户
    private int                isRole;                                 // 是否能创建角色
    private int                isResource;                             // 是否能创建资源
    private String             assAttribute;                           // 关联属性

    public String getAssAttribute() {
        return assAttribute;
    }

    public void setAssAttribute(String assAttribute) {
        this.assAttribute = assAttribute;
    }

    public String getEnterpriseCode() {
        return enterpriseCode;
    }

    public void setEnterpriseCode(String enterpriseCode) {
        this.enterpriseCode = enterpriseCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public int getIsUser() {
        return isUser;
    }

    public void setIsUser(int isUser) {
        this.isUser = isUser;
    }

    public int getIsRole() {
        return isRole;
    }

    public void setIsRole(int isRole) {
        this.isRole = isRole;
    }

    public int getIsResource() {
        return isResource;
    }

    public void setIsResource(int isResource) {
        this.isResource = isResource;
    }
}
