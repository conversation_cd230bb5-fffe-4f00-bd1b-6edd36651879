package net.xplife.system.auth.entity;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
@Document(collection = "V1_ProjectUser")
public class ProjectUser extends IdEntity {
    /**
     * 项目用户表
     */
    private static final long  serialVersionUID      = 1L;
    public static final String COLL                  = "V1_ProjectUser";
    public static final String USER_ACCOUNT_ID_FIELD = "userAccountId";
    public static final String PROJECT_ID_FIELD      = "projectId";
    private String             projectId;                               // 项目ID
    private String             userAccountId;                           // 用户账户表ID

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getUserAccountId() {
        return userAccountId;
    }

    public void setUserAccountId(String userAccountId) {
        this.userAccountId = userAccountId;
    }
}
