package net.xplife.system.auth.entity;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
@Document(collection = "V1_ResourceTable")
public class ResourceTable extends IdEntity {
    /**
     * 资源表
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_ResourceTable";
    public static final String RES_NAME_FIELD   = "resName";
    public static final String LEVEL_FIELD      = "level";
    public static final String TYPE_FIELD       = "type";
    public static final String APP_ID_FIELD     = "appId";
    public static final String SORT_FIELD       = "sort";
    private String             pId;                                  // 父节点ID
    private String             resName;                              // 资源名称
    private String             resCode;                              // 资源编码
    private String             path;                                 // 访问路径
    private int                level;                                // 层级
    private int                sort;                                 // 排序
    private int                type;                                 // 资源类型 0菜单 1按钮
    private String             projectId;                            // 项目ID
    private String             icon;                                 // 菜单图标

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getpId() {
        return pId;
    }

    public void setpId(String pId) {
        this.pId = pId;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }
}
