package net.xplife.system.auth.entity;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
@Document(collection = "V1_RoleResource")
public class RoleResource extends IdEntity {
    /**
     * 角色资源
     */
    private static final long  serialVersionUID  = 1L;
    public static final String COLL              = "V1_RoleResource";
    public static final String ROLE_ID_FIELD     = "roleId";
    public static final String RESOURCE_ID_FIELD = "resourceId";
    private String             roleId;                               // 角色表ID
    private String             resourceId;                           // 资源表ID

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }
}
