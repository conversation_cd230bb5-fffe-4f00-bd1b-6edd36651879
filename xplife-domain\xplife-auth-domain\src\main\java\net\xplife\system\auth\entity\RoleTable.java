package net.xplife.system.auth.entity;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
@Document(collection = "V1_RoleTable")
public class RoleTable extends IdEntity {
    /**
     * 角色表
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_RoleTable";
    public static final String ROLE_NAME_FIELD  = "roleName";
    public static final String ROLE_CODE_FIELD  = "roleCode";
    public static final String APP_ID_FIELD     = "appId";
    private String             projectId;                        // 项目ID
    private String             roleName;                         // 角色名称
    private String             roleCode;                         // 角色编码

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }
}
