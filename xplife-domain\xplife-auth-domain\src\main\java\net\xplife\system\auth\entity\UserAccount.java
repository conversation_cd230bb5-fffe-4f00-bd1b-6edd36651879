package net.xplife.system.auth.entity;
import java.util.List;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
@Document(collection = "V1_UserAccount")
public class UserAccount extends IdEntity {
    /**
     * 用户表
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_UserAccount";
    public static final String NAME_FIELD       = "name";
    public static final String ACCOUNT_FIELD    = "account";
    public static final String APP_ID_FIELD     = "appId";
    private String             name;                               // 用户名称
    private String             account;                            // 用户账号
    private String             password;                           // 用户密码
    private String             salt;                               // 安全码
    private String             assAttribute;                       // 关联属性
    private List<String>       projectIds;                         // 项目ID集合

    public List<String> getProjectIds() {
        return projectIds;
    }

    public void setProjectIds(List<String> projectIds) {
        this.projectIds = projectIds;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getAssAttribute() {
        return assAttribute;
    }

    public void setAssAttribute(String assAttribute) {
        this.assAttribute = assAttribute;
    }
}
