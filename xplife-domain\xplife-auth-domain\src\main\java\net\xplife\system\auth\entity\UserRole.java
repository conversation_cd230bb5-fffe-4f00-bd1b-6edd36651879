package net.xplife.system.auth.entity;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
@Document(collection = "V1_UserRole")
public class UserRole extends IdEntity {
    /**
     * 用户角色表
     */
    private static final long  serialVersionUID      = 1L;
    public static final String COLL                  = "V1_UserRole";
    public static final String USER_ACCOUNT_ID_FIELD = "userAccountId";
    public static final String ROLE_ID_FIELD         = "roleId";
    public static final String PROJECT_ID_FIELD      = "projectId";
    private String             userAccountId;                          // 用户账户表ID
    private String             roleId;                                 // 角色表ID
    private String             projectId;                              // 项目ID

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getUserAccountId() {
        return userAccountId;
    }

    public void setUserAccountId(String userAccountId) {
        this.userAccountId = userAccountId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }
}
