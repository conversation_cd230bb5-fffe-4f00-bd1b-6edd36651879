package net.xplife.system.auth.enums.system;
import java.util.LinkedHashMap;
/**
 * 查询参数枚举
 * 
 * <AUTHOR> 2018年11月15日
 */
public enum SearchQueryEnum {
    NAME("name", "名称"),
    LEVEL("level", "层级"),
    TYPE("type", "类型"),;
    private final String                               value;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<>();
        for (SearchQueryEnum searchQueryEnum : SearchQueryEnum.values()) {
            map.put(searchQueryEnum.getValue(), searchQueryEnum.getDesc());
        }
    }

    SearchQueryEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
