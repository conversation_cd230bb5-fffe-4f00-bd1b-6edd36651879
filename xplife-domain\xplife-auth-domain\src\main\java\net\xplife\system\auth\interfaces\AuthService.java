package net.xplife.system.auth.interfaces;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import net.xplife.system.auth.dto.CreateAccountDto;
import net.xplife.system.auth.dto.UserAccountDto;
import net.xplife.system.web.core.FeignConfiguration;
/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-auth", configuration = FeignConfiguration.class)
public interface AuthService {
    /**
     * 新增或更新用户账号
     * 
     * @return
     */
    @RequestMapping(value = "/auth/v1/useraccount/saveorupdate", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void saveOrUpdate(@RequestBody CreateAccountDto createAccountDto);

    /**
     * 新增或更新用户账号和分配角色
     * 
     * @return
     */
    @RequestMapping(value = "/auth/v1/useraccount/updateanddisrole", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void updateAndDisrole(@RequestBody CreateAccountDto createAccountDto);

    /**
     * 删除账号
     * 
     * @return
     */
    @RequestMapping(value = "/auth/v1/useraccount/del", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void del(@RequestParam("id") String id);

    /**
     * 删除账号
     * 
     * @return
     */
    @RequestMapping(value = "/auth/v1/useraccount/delbyaccount", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void delByAccount(@RequestParam("account") String account);

    /**
     * 修改密码
     * 
     * @return
     */
    @RequestMapping(value = "/auth/v1/useraccount/changepwdbyaccount", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void changePwdByAccount(@RequestParam("account") String account, @RequestParam("newpwd") String newpwd);

    /**
     * 更新用户角色
     * 
     * @return
     */
    @RequestMapping(value = "/auth/v1/useraccount/updaterole", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void updateRole(@RequestParam("account") String account, @RequestParam("role") int role);

    /**
     * 更换登录账号
     * 
     * @return
     */
    @RequestMapping(value = "/auth/v1/useraccount/changeaccount", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void changeAccount(@RequestParam("account") String account, @RequestParam("newaccount") String newAccount);

    /**
     * 根据ID获取用户信息
     * 
     * @return
     */
    @RequestMapping(value = "/auth/v1/useraccount/getbyid", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public UserAccountDto getById(@RequestParam("id") String id);

    /**
     * 根据账号获取用户信息
     * 
     * @return
     */
    @RequestMapping(value = "/auth/v1/useraccount/getbyaccount", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public UserAccountDto getByAccount(@RequestParam("account") String account);
}
