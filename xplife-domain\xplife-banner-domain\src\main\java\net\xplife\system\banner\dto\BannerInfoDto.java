package net.xplife.system.banner.dto;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import com.alibaba.fastjson.annotation.JSONField;
public class BannerInfoDto implements Serializable {
    /**
     * banner详情dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            name;                 // 名称
    private String            pic;                  // 图片
    private String            jumpVal;              // 消息跳转值
    private int               sort;                 // 排序
    private int               jumpType;             // 跳转类型
    private String            column;               // 栏目（home 首页顶部，knowledge 干货）
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              startTime;            // Banner有效开始时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              endTime;              // Banner有效结束时间
    private String              shareTitle;                    // 分享时展示的标题
    private String              shareContent;                  // 分享时展示的内容
    private int                 shareFlag;                     // 是否分享：0表示不分享，1表示分享
    private String            version;              // 大于等于该版本好才显示
    private String            remark;               // 备注信息,如果column是knowledge,则备注表达的是年级
    private String              paramAndroid;                  // 自定义组装的json格式，用于andriod前端调用
    private String              paramIos;                      // 自定义组装的json格式，用于ios前端调用

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getJumpVal() {
        return jumpVal;
    }

    public void setJumpVal(String jumpVal) {
        this.jumpVal = jumpVal;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public String getColumn() {
        return column;
    }

    public void setColumn(String column) {
        this.column = column;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getShareTitle() {
        return shareTitle;
    }

    public void setShareTitle(String shareTitle) {
        this.shareTitle = shareTitle;
    }

    public String getShareContent() {
        return shareContent;
    }

    public void setShareContent(String shareContent) {
        this.shareContent = shareContent;
    }

    public int getShareFlag() {
        return shareFlag;
    }

    public void setShareFlag(int shareFlag) {
        this.shareFlag = shareFlag;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getParamIos() {
        return paramIos;
    }

    public String getParamAndroid() {
        return paramAndroid;
    }

    public void setParamAndroid(String paramAndroid) {
        this.paramAndroid = paramAndroid;
    }

    public void setParamIos(String paramIos) {
        this.paramIos = paramIos;
    }
}
