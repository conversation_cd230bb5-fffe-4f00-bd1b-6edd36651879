 package net.xplife.system.banner.entity;
import java.util.Date;
import java.util.Map;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 顶部广告信息表
 */
@Document(collection = "V1_Banner")
public class Banner extends IdEntity {
    private static final long   serialVersionUID = 1L;
    public static final String  COLL             = "V1_Banner";
    public static final String  SORT_FIELD       = "sort";
    private String              name;                          // 名称
    private Map<String, String> pics;                          // 图片map
    private String              jumpVal;                       // 消息跳转值
    private int                 sort;                          // 排序
    private int                 jumpType;                      // 跳转类型
    private String              column;                        // 栏目（gam 社区，mall商城）
    private Date                startTime;                     // Banner有效开始时间
    private Date                endTime;                       // Banner有效结束时间
    private String              shareTitle;                    // 分享时展示的标题
    private String              shareContent;                  // 分享时展示的内容
    private int                 shareFlag;                     // 是否分享
    private String              version;                       // 显示最小版本版本号(为了能提前查看效果)
    private String              remark;                        // 备注信息,如果column是knowledge,则备注表达的是年级
    private String              paramAndroid;                  // 自定义组装的json格式，用于andriod前端调用
    private String              paramIos;                      // 自定义组装的json格式，用于ios前端调用

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, String> getPics() {
        return pics;
    }

    public void setPics(Map<String, String> pics) {
        this.pics = pics;
    }

    public String getJumpVal() {
        return jumpVal;
    }

    public void setJumpVal(String jumpVal) {
        this.jumpVal = jumpVal;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public String getColumn() {
        return column;
    }

    public void setColumn(String column) {
        this.column = column;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getShareTitle() {
        return shareTitle;
    }

    public void setShareTitle(String shareTitle) {
        this.shareTitle = shareTitle;
    }

    public String getShareContent() {
        return shareContent;
    }

    public void setShareContent(String shareContent) {
        this.shareContent = shareContent;
    }

    public int getShareFlag() {
        return shareFlag;
    }

    public void setShareFlag(int shareFlag) {
        this.shareFlag = shareFlag;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getParamIos() {
        return paramIos;
    }

    public void setParamIos(String paramIos) {
        this.paramIos = paramIos;
    }

    public String getParamAndroid() {
        return paramAndroid;
    }

    public void setParamAndroid(String paramAndroid) {
        this.paramAndroid = paramAndroid;
    }
}
