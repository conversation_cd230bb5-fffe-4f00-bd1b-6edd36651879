package net.xplife.system.banner.interfaces;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import net.xplife.system.banner.dto.BannerInfoDto;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;

import java.util.List;

/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-banner", configuration = FeignConfiguration.class)
public interface BannerService {
    /**
     * 获取banner信息
     * 
     * @return
     */
    @RequestMapping(value = "/activity/v1/banner/getbannerinfo", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BannerInfoDto getBannerInfo(@RequestParam("id") String id);

    /**
     * 新增或更新banner信息
     * 
     * @return
     */
    @RequestMapping(value = "/activity/v1/banner/saveorupdate", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void saveOrUpdate(@RequestBody BannerInfoDto bannerInfoDto);

    /**
     * 删除banner信息
     * 
     * @return
     */
    @RequestMapping(value = "/activity/v1/banner/del", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void del(@RequestParam("id") String id);

    /**
     * 获取banner信息列表
     * 
     * @return
     */
    @RequestMapping(value = "/activity/v1/banner/findbannerpage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<BannerInfoDto> findBannerPage(@RequestParam("pageno") int pageNo, @RequestParam("pagesize") int pageSize);

    /**
     * 获取banner信息列表
     *
     * @return
     */
    @RequestMapping(value = "/activity/v1/banner/findAllList", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<BannerInfoDto> findBannerAllList();
}
