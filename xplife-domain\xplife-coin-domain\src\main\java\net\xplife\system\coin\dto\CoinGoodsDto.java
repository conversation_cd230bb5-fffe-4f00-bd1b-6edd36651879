package net.xplife.system.coin.dto;

import net.xplife.system.coin.entity.CoinPicVo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2020/12/11 17:23
 * @description：
 * @modified By：
 * @version: $
 */
public class CoinGoodsDto implements Serializable {
    private String              id;
    private String              name;//商品名称
    private String              coverUrl;//封面
    private int                 costNum;//积分价值
    private int                 stockNum;//库存
    private String              info;//商品描述
    private List<CoinPicVo>     pics;//商品的图
    private int                 type;// 0:商品；1:头像；2:字体；3：文具；4：打印机；99：其他
    private int                 isNews;//0:不是新品，1：是新品
    private int                 isExchange;//当前用户是否已兑换（针对虚拟商品） 0：未兑换；1：已兑换
    private String              code;   //头像挂件的编号
    private String              url;//如果是字体，提供字体下载地址
    private String              fontListUrl;//在列表中展示的图案
    private int                 sortNum;//排序字段，数值越大越排前
    private String              price;// 商品的金额
    private int                 canOffset;// 是否积分和商品一起抵扣模式
    private String              redirectUrl;// 商城url地址
    private int                 changeNum;// 兑换次数


    public String getFontListUrl() {
        return fontListUrl;
    }

    public void setFontListUrl(String fontListUrl) {
        this.fontListUrl = fontListUrl;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public int getCostNum() {
        return costNum;
    }

    public void setCostNum(int costNum) {
        this.costNum = costNum;
    }

    public int getStockNum() {
        return stockNum;
    }

    public void setStockNum(int stockNum) {
        this.stockNum = stockNum;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public List<CoinPicVo> getPics() {
        return pics;
    }

    public void setPics(List<CoinPicVo> pics) {
        this.pics = pics;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getIsNews() {
        return isNews;
    }

    public void setIsNews(int isNews) {
        this.isNews = isNews;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getIsExchange() {
        return isExchange;
    }

    public void setIsExchange(int isExchange) {
        this.isExchange = isExchange;
    }
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public int getCanOffset() {
        return canOffset;
    }

    public void setCanOffset(int canOffset) {
        this.canOffset = canOffset;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public int getChangeNum() {
        return changeNum;
    }

    public void setChangeNum(int changeNum) {
        this.changeNum = changeNum;
    }
}
