package net.xplife.system.coin.dto;

/**
 * <AUTHOR>
 * @date ：Created in 2021/2/22 9:52
 * @description：积分任务时间段内完成情况
 * @modified By：
 * @version: $
 */
public class CoinStatisticsMissionFinishDto {
    private String missionCode;         //任务名称
    private String missionName;         //任务名称
    private int finishTimes;            //任务完成次数
    private int fetchTimes;            //积分收取次数
    private int finishUserCount;        //任务完成人数
    private long coinCount;             // 该任务总共产生的积分
    private Double fetchAverageCoins;   //平均每人获得奖励

    public String getMissionCode() {
        return missionCode;
    }

    public void setMissionCode(String missionCode) {
        this.missionCode = missionCode;
    }

    public String getMissionName() {
        return missionName;
    }

    public void setMissionName(String missionName) {
        this.missionName = missionName;
    }

    public int getFinishTimes() {
        return finishTimes;
    }

    public void setFinishTimes(int finishTimes) {
        this.finishTimes = finishTimes;
    }

    public int getFinishUserCount() {
        return finishUserCount;
    }

    public void setFinishUserCount(int finishUserCount) {
        this.finishUserCount = finishUserCount;
    }

    public Double getFetchAverageCoins() {
        return fetchAverageCoins;
    }

    public int getFetchTimes() {
        return fetchTimes;
    }

    public void setFetchTimes(int fetchTimes) {
        this.fetchTimes = fetchTimes;
    }

    public long getCoinCount() {
        return coinCount;
    }

    public void setCoinCount(long coinCount) {
        this.coinCount = coinCount;
    }

    public void setFetchAverageCoins(Double fetchAverageCoins) {
        this.fetchAverageCoins = fetchAverageCoins;
    }
}
