package net.xplife.system.coin.dto;

import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.dto.UserInfoDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2021/2/22 10:06
 * @description：用户拥有积分余额排名
 * @modified By：
 * @version: $
 */
public class CoinStatisticsUserCoinRankDto {
    private int rankIndex;                      // 排名
    private UserInfoDto userInfoDto;            // 用户信息
    private int currCoin;                       // 当前积分余额
    private int totalCoin;                      // 累计积分
    private List<Map<String, String>> missionList;    // 奖励明细
    private String missionListStr;              // 奖励明细，转换成字符串

    public int getRankIndex() {
        return rankIndex;
    }

    public void setRankIndex(int rankIndex) {
        this.rankIndex = rankIndex;
    }

    public UserInfoDto getUserInfoDto() {
        return userInfoDto;
    }

    public void setUserInfoDto(UserInfoDto userInfoDto) {
        this.userInfoDto = userInfoDto;
    }

    public int getCurrCoin() {
        return currCoin;
    }

    public void setCurrCoin(int currCoin) {
        this.currCoin = currCoin;
    }

    public int getTotalCoin() {
        return totalCoin;
    }

    public void setTotalCoin(int totalCoin) {
        this.totalCoin = totalCoin;
    }

    public List<Map<String, String>> getMissionList() {
        return missionList;
    }

    public void setMissionList(List<Map<String, String>> missionList) {
        this.missionList = missionList;
    }

    public String getMissionListStr() {
        if (ToolsKit.isNotEmpty(this.missionList)){
            StringBuffer sb = new StringBuffer("");
            String format = "%s:%s ";
            for (Map<String, String> map:this.missionList) {
                sb.append(String.format(format, map.get("name"), map.get("num")));
            }
            return sb.toString();
        }
        return missionListStr;
    }

    public void setMissionListStr(String missionListStr) {
        this.missionListStr = missionListStr;
    }
}
