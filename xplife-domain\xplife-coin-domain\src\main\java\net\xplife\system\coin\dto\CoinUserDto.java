package net.xplife.system.coin.dto;

import net.xplife.system.coin.entity.MissionFinishInfoVo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2020/12/11 17:23
 * @description：
 * @modified By：
 * @version: $
 */
public class CoinUserDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private int coinNumCurr;                    //用户可用积分
    private int coinNumTotal;                   //用户总共积分
    private List<MissionFinishInfoVo> missionList;//每日的积分情况，包括终生一次的
    private int canFeathCount;                  // 能领取积分的任务数
//    private int level;                          //用户积分等级
//    private String levelText;                   //(如果有特殊显示要求)

    public int getCoinNumCurr() {
        return coinNumCurr;
    }

    public void setCoinNumCurr(int coinNumCurr) {
        this.coinNumCurr = coinNumCurr;
    }

    public int getCoinNumTotal() {
        return coinNumTotal;
    }

    public void setCoinNumTotal(int coinNumTotal) {
        this.coinNumTotal = coinNumTotal;
    }

//    public int getLevel() {
//        return level;
//    }
//
//    public void setLevel(int level) {
//        this.level = level;
//    }
//
//    public String getLevelText() {
//        return levelText;
//    }
//
//    public void setLevelText(String levelText) {
//        this.levelText = levelText;
//    }

    public int getCanFeathCount() {
        return canFeathCount;
    }

    public void setCanFeathCount(int canFeathCount) {
        this.canFeathCount = canFeathCount;
    }

    public List<MissionFinishInfoVo> getMissionList() {
        return missionList;
    }

    public void setMissionList(List<MissionFinishInfoVo> missionList) {
        this.missionList = missionList;
    }
}
