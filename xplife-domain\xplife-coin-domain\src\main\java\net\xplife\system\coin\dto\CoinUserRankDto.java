package net.xplife.system.coin.dto;

import net.xplife.system.web.dto.UserInfoDto;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2021/2/4 8:35
 * @description：时间范围内用户新增的积分数量
 * @modified By：
 * @version: $
 */
public class CoinUserRankDto implements Serializable {
    private Date beginDate;                 // 开始时间
    private Date endDate;                   // 结束时间
    private UserInfoDto userInfoDto;        // 用户信息
    private int coinCount;                  // 在时间范围内，一共赚取的积分数量
    private int rank;                       // 排名
    private int finishCount;                      // 完成次数

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public UserInfoDto getUserInfoDto() {
        return userInfoDto;
    }

    public void setUserInfoDto(UserInfoDto userInfoDto) {
        this.userInfoDto = userInfoDto;
    }

    public int getCoinCount() {
        return coinCount;
    }

    public void setCoinCount(int coinCount) {
        this.coinCount = coinCount;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public int getFinishCount() {
        return finishCount;
    }

    public void setFinishCount(int finishCount) {
        this.finishCount = finishCount;
    }
}
