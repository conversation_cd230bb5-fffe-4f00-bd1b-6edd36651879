package net.xplife.system.coin.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2020/12/15 10:28
 * @description：虚拟商品中用户头像
 * @modified By：
 * @version: $
 */
public class CoinUserTitleGoodsDto implements Serializable {
    private String id;              //商品id
    private int costNum;            //兑换所需积分
    private int isExchange;         //是否已兑换
    private String url;             //url
    private String name;            //头像名称
    private int type;               //头像的商品属性
    private String coverUrl;           //封面
    private String code;          //挂件编号

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getIsExchange() {
        return isExchange;
    }

    public void setIsExchange(int isExchange) {
        this.isExchange = isExchange;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public int getCostNum() {
        return costNum;
    }

    public void setCostNum(int costNum) {
        this.costNum = costNum;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
