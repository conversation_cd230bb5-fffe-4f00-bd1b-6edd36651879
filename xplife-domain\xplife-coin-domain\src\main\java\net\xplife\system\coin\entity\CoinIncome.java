package net.xplife.system.coin.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
/**
 * <AUTHOR>
 * @date       ：Created in 2020-12-11
 * @description：积分收入表
 * @version:     1.0
 */
@Document(collection = "V1_CoinIncome")
public class CoinIncome extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_CoinIncome";
    public static final String USER_ID_FIELD    = "userId";
    public static final String TYPE_FIELD    = "type";
    public static final String TRANSACTION_DATE_TYPE_FIELD    = "transactionDate";
    @Indexed(name = "_userid_")
    private String userId;// 用户id
    @Indexed(name = "_type_")
    private String type;// 类型
    private String typeStr;// 类型描述
    private int    num;//交易积分数量
    @Indexed(name = "_transactionDate_")
    private String transactionDate;//交易日期-字符串
    private String info;//交易描述

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeStr() {
        return typeStr;
    }

    public void setTypeStr(String typeStr) {
        this.typeStr = typeStr;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
