package net.xplife.system.coin.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date       ：Created in 2020-12-11
 * @description：用户积分等级表
 * @version:     1.0
 */
@Document(collection = "V1_CoinLevel")
public class CoinLevel extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_CoinLevel";
    private int level;          // 等级
    private String name;        // 名称
    private int coinNum;        // 所需积分数量
    private String remark;      // 备注

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCoinNum() {
        return coinNum;
    }

    public void setCoinNum(int coinNum) {
        this.coinNum = coinNum;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
