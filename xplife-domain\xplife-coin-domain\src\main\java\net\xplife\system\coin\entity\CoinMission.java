package net.xplife.system.coin.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date       ：Created in 2020-12-11
 * @description：用户积分获取积分的任务列表
 * @version:     1.0
 */
@Document(collection = "V1_CoinMission")
public class CoinMission extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_CoinGoods";
    public static final String SORT_NUM_FIELD = "sortNum";
    private String          category;   //任务类别
    private String          name;       //任务名称
    private String          code;       //任务编号
    private String          info;       //详情描述
    private String          sendMess;   //系统发放消息
    private String          jumpDesc;   //跳转描述
    private int             costCoin;   // 一次任务获得积分数量
    private int             dayNum;     //每天能做多少次，
    private int             dayTotal;     //每天最多获得，
    private int             autoPay;    //是否自动获取，0:手动，1:自动
    private int             type;       //0:每日；1：终生一次
    private int             sortNum;    //排序编号
    private String          paramAndroid;                  // 自定义组装的json格式，用于andriod前端调用
    private String          paramIos;                      // 自定义组装的json格式，用于ios前端调用

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getJumpDesc() {
        return jumpDesc;
    }

    public void setJumpDesc(String jumpDesc) {
        this.jumpDesc = jumpDesc;
    }

    public int getCostCoin() {
        return costCoin;
    }

    public void setCostCoin(int costCoin) {
        this.costCoin = costCoin;
    }

    public int getDayNum() {
        return dayNum;
    }

    public void setDayNum(int dayNum) {
        this.dayNum = dayNum;
    }

    public int getAutoPay() {
        return autoPay;
    }

    public void setAutoPay(int autoPay) {
        this.autoPay = autoPay;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getSendMess() {
        return sendMess;
    }

    public void setSendMess(String sendMess) {
        this.sendMess = sendMess;
    }

    public String getParamAndroid() {
        return paramAndroid;
    }

    public void setParamAndroid(String paramAndroid) {
        this.paramAndroid = paramAndroid;
    }

    public String getParamIos() {
        return paramIos;
    }

    public void setParamIos(String paramIos) {
        this.paramIos = paramIos;
    }

    public int getDayTotal() {
        return dayTotal;
    }

    public void setDayTotal(int dayTotal) {
        this.dayTotal = dayTotal;
    }
}
