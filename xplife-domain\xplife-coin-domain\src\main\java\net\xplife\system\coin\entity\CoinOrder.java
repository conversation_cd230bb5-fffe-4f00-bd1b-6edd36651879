package net.xplife.system.coin.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date       ：Created in 2020-12-11
 * @description：用户兑换记录表
 * @version:     1.0
 */
@Document(collection = "V1_CoinOrder")
public class CoinOrder extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_CoinGoods";
    public static final String USER_ID_FIELD    = "userId";
    public static final String USER_NAME_FIELD    = "userName";
    @Indexed(name = "_userid_")
    private String userId;          //当前用户id
    private String userName;        //收件人名称
    private String userPhone;       //用户联系电话
    private String userAddress;     //用户收件地址
    private String goodsId;         //商品id
    private String goodsName;       //商品名称
    private String deliveryCompany;//快递公司
    private String deliveryCode;  // 快递编号
    private String deliveryStatus;// 未发货；已发货
    private String goodsCount;       //兑换商品数量

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getUserAddress() {
        return userAddress;
    }

    public void setUserAddress(String userAddress) {
        this.userAddress = userAddress;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getDeliveryCompany() {
        return deliveryCompany;
    }

    public void setDeliveryCompany(String deliveryCompany) {
        this.deliveryCompany = deliveryCompany;
    }

    public String getDeliveryCode() {
        return deliveryCode;
    }

    public void setDeliveryCode(String deliveryCode) {
        this.deliveryCode = deliveryCode;
    }

    public String getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(String deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(String goodsCount) {
        this.goodsCount = goodsCount;
    }
}
