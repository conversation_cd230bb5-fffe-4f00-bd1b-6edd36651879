package net.xplife.system.coin.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date       ：Created in 2020-12-11
 * @description：用户每天做任务情况
 * @version:     1.0
 */
@Document(collection = "V1_CoinOutPaid")
public class CoinOutPaid extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_CoinOutPaid";
    public static final String USER_ID_FIELD    = "userId";
    public static final String GOODS_TYPE_FIELD = "goodsType";
    public static final String GOODS_ID_FIELD = "goodsId";
    public static final String TRANSACTION_DATE_TYPE_FIELD    = "transactionDate";
    @Indexed(name = "_userid_")
    private String userId;          //当前用户id
    @Indexed(name = "_goodsType_")
    private int goodsType;// 类型类型：0:商品；1:头像；2:字体；-1：其他扣除项; 3:文具；4：打印机；99：其他
    private String goodsId;// 商品id
    private int    num;//交易积分数量
    @Indexed(name = "_transactionDate_")
    private String transactionDate;//交易日期-字符串
    private String info;//交易描述

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(int goodsType) {
        this.goodsType = goodsType;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
