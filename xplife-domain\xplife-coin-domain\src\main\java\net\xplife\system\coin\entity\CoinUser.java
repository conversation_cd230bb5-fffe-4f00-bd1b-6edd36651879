package net.xplife.system.coin.entity;


import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.HashMap;
import java.util.Map;
/**
 * <AUTHOR>
 * @date       ：Created in 2020-12-11
 * @description：积分商城-用户信息
 * @version:     1.0
 */
@Document(collection = "V1_CoinUser")
public class CoinUser extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_CoinUser";
    public static final String USER_ID_FIELD    = "userId";
    @Indexed(name = "_userid_")
    private String              userId;         // 当前用户
    private int                 coinNumCurr;    // 当前可用积分
    private int                 coinNumTotal;   // 当前总积分
    private Map<String, MissionFinishInfoVo> onceMissionObj = new HashMap<>(); // 只一次性的任务领取情况

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getCoinNumCurr() {
        return coinNumCurr;
    }

    public void setCoinNumCurr(int coinNumCurr) {
        this.coinNumCurr = coinNumCurr;
    }

    public int getCoinNumTotal() {
        return coinNumTotal;
    }

    public void setCoinNumTotal(int coinNumTotal) {
        this.coinNumTotal = coinNumTotal;
    }

    public Map<String, MissionFinishInfoVo> getOnceMissionObj() {
        return onceMissionObj;
    }

    public void setOnceMissionObj(Map<String, MissionFinishInfoVo> MissionFinishInfoVo) {
        this.onceMissionObj = onceMissionObj;
    }
}
