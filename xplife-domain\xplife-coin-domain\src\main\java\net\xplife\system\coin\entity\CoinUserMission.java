package net.xplife.system.coin.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * <AUTHOR>
 * @date       ：Created in 2020-12-11
 * @description：用户每天做任务情况
 * @version:     1.0
 */
@Document(collection = "V1_CoinUserMission")
public class CoinUserMission extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_CoinUserMission";
    public static final String USER_ID_FIELD    = "userId";
    public static final String DAY_DATE_FIELD    = "dayDate";
    @Indexed(name = "_userid_")
    private String userId;
    private Map<String, MissionFinishInfoVo> missionObj;
    private int    numTotal;//今天获取积分数量
    @Indexed(name = "_dayDate_")
    private String dayDate;//日期字符串 yyyy-MM-dd

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Map<String, MissionFinishInfoVo> getMissionObj() {
        return missionObj;
    }

    public void setMissionObj(Map<String, MissionFinishInfoVo> missionObj) {
        this.missionObj = missionObj;
    }

    public int getNumTotal() {
        return numTotal;
    }

    public void setNumTotal(int numTotal) {
        this.numTotal = numTotal;
    }

    public String getDayDate() {
        return dayDate;
    }

    public void setDayDate(String dayDate) {
        this.dayDate = dayDate;
    }
}
