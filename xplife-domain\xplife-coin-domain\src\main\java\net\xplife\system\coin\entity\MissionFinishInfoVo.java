package net.xplife.system.coin.entity;

/**
 * <AUTHOR>
 * @date ：Created in 2020/12/11 14:35
 * @description：用户每天完成任务情况
 * @modified By：
 * @version: 1.0$
 */
public class MissionFinishInfoVo {
    private String name;//名称
    private String code;//编号（做为key）
    private int dayNum; //每天可以完成多少次
    private int curNum; //当前完成次数
    private int fetchNum;//已点取获取次数
    private int type;//0：每日；1：终生一次
    private int sortNum;//排序字段，取任务表中的排序字段
    private int costCoin;//任务每次多少积分
    private String          paramAndroid;                  // 自定义组装的json格式，用于andriod前端调用
    private String          paramIos;                      // 自定义组装的json格式，用于ios前端调用

    public MissionFinishInfoVo() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getDayNum() {
        return dayNum;
    }

    public void setDayNum(int dayNum) {
        this.dayNum = dayNum;
    }

    public int getCurNum() {
        return curNum;
    }

    public void setCurNum(int curNum) {
        this.curNum = curNum;
    }

    public int getFetchNum() {
        return fetchNum;
    }

    public void setFetchNum(int fetchNum) {
        this.fetchNum = fetchNum;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getParamAndroid() {
        return paramAndroid;
    }

    public void setParamAndroid(String paramAndroid) {
        this.paramAndroid = paramAndroid;
    }

    public String getParamIos() {
        return paramIos;
    }

    public void setParamIos(String paramIos) {
        this.paramIos = paramIos;
    }

    public int getCostCoin() {
        return costCoin;
    }

    public void setCostCoin(int costCoin) {
        this.costCoin = costCoin;
    }
}
