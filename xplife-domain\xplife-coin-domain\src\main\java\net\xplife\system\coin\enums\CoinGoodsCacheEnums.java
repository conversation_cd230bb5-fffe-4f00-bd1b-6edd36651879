package net.xplife.system.coin.enums;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date ：Created in 2021/1/7 10:57
 */
public enum CoinGoodsCacheEnums implements ICacheEnums {
    COINGOODS_BY_ID("coin:goods:by:id:", "积分商城兑换商品"),
    COINGOODS_temp_BY_USERID("coin:goods:temp:by:%s", "临时商品对象-用户"),
    COINGOODS_ID_LIST("coin:goods:temp:by:list:", "临时商品数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (CoinGoodsCacheEnums combQuesCacheEnums : CoinGoodsCacheEnums.values()) {
            map.put(combQuesCacheEnums.getKey(), combQuesCacheEnums.getDesc());
        }
    }

    private CoinGoodsCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}