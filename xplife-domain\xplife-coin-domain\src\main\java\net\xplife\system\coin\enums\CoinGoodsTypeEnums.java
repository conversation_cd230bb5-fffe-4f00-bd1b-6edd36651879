package net.xplife.system.coin.enums;

import java.util.LinkedHashMap;

/***
 * 商品类型
 */
public enum CoinGoodsTypeEnums {
    GOODS(0, "商品"),
//    HEADIMG(1, "挂件"),
    FONT(2, "字体"),
    STATIONERY(3, "文具"),
    PRINTER(4, "打印机"),
    OTHER(9, "其他"),
    QINGLING(99, "清零操作");
    private final int                                   type;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (CoinGoodsTypeEnums missionTypeEnums : CoinGoodsTypeEnums.values()) {
            map.put(missionTypeEnums.getType(), missionTypeEnums.getDesc());
        }
    }

    CoinGoodsTypeEnums(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
