package net.xplife.system.coin.enums;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date ：Created in 2020/12/16 10:21
 */
public enum MissionCodeEnums {
    REGISTER("register", "用户首次注册并登陆APP赠送。"),
    MOBILEPHONE("mobilephone", "绑定手机号码"),
    UPDATENICKNAME("updateNickName", "修改昵称"),
    UPDATEHEADIMG("updateHeadImg", "更换头像"),
    APPCOMMENT("appComment", "应用市场评论"),
    ATTENTIONASS("attentionAss", "关注星星机小助理"),
    ATTENTIONMANAGER("attentionManager", "关注星星机小管家"),
    UPGRADE("upgrade", "升级到最新版本"),
    CHOICENESS("choiceness", "帖子被设为精选"),
    LOGIN("login", "每天首次打开APP视为签到成功"),
    PRINT("print", "使用星星机打印任意内容"),
    FEEDBACK("feedback", "意见反馈"),
    READKNOWLEDGE("readKnowledge", "每日首次阅读干货"),
    COMPPRINT("compPrint", "每日首次组卷打印"),
    PHOTOPRINT("photoPrint", "每日首次拍题打印"),
    SEARCHPRINT("searchPrint", "每日首次搜题打印"),
    ADDMISTAKESBOOK("addMistakesBook", "添加习题到错题本"),
    ADDSTUDYNOTE("addStudyNote", "发布学习圈"),
    ADDFEEDNOTE("addFeedNote", "发布兴趣圈"),
    ADDCOMMENTNOTE("addCommentNote", "评论他人的动态"),
    GIVELIKE("giveLike", "点赞他人的动态"),
    SHAREKNOWLEDGE("shareKnowledge", "分享干货"),
    SHAREFEEDNOTE("shareFeedNote", "分享圈子动态"),
    CLEAR_COIN("clearCoin", "积分清零"),
    ACTIVITY_INCR("activity", "活动奖励"),// 节假日的活动奖励
    CUSTOM_INCR("custom", "活动奖励"),// 通过后台派送的奖励
    ATTENTION_OVER_10("attentionOver10", "关注10个人"),
    FANS_OVER_300("fansOver300", "粉丝数达到300"),
    FANS_OVER_500("fansOver500", "粉丝数达到500"),
    FANS_OVER_1000("fansOver1000", "粉丝数达到1000"),
    GOODS_CHANGE("goodsChange", "兑换一次商品"),
    PRINT_KNOWLEDGE("printKnowledge", "干货打印"),
    EXERCISE_KS("exerciseKs", "口算练习"),
    AUTOMATIC_SPEECH("automaticSpeech", "语音识别");
    private final String                                   code;
    private final String                                desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<>();
        for (MissionCodeEnums missionCodeEnums : MissionCodeEnums.values()) {
            map.put(missionCodeEnums.getCode(), missionCodeEnums.getDesc());
        }
    }

    MissionCodeEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
