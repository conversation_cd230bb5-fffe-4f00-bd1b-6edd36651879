package net.xplife.system.coin.enums;

import java.util.LinkedHashMap;

/***
 * 任务类型
 */
public enum MissionTypeEnums {
    DAY(0, "每日"),
    ALL(1, "终生一次"),
    SP(2, "特殊");
    private final int                                   type;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (MissionTypeEnums missionTypeEnums : MissionTypeEnums.values()) {
            map.put(missionTypeEnums.getType(), missionTypeEnums.getDesc());
        }
    }

    MissionTypeEnums(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
