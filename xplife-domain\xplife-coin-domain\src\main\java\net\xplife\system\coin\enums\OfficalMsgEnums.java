package net.xplife.system.coin.enums;

import java.util.LinkedHashMap;

/***
 * 官方消息类型及发送文案
 */
public enum OfficalMsgEnums {
    DELETE_FEEDNOTE("deleteFeedNote", "动态违规已被删除通知", "很遗憾~你发表的“%s”动态因不符合平台规定已被管理员删除，如有疑问请咨询官方客服微信：youyin0514。"),
    EXCHANGE("exchange","商品兑换成功！", "恭喜你成功兑换 【%s】，消耗积分%s，当前可用积分余额%s。\n" +
            "如有疑问，请联系官方客服微信：youyin0514。"),
    UPGRADE("upgrade","恭喜你获得版本升级奖励！", "%s积分 已到账喽~"),
    PRINT("print","叮~打印奖励已到账","聪明好学的你昨天共打印里程%scm。特此奉上%s积分奖励~请再接再厉！"),
    REMIND("remind","你还有积分未领取哦","温馨提示~你的积分任务奖励将在今日23:59:59失效，请及时领取。"),
    CHOICENESS("choiceness","你发布的动态被官方评为【精选】啦","厉害了！你发布的“%s”动态实在是太优质了！\n 官方送你%s积分作为奖励，请查收~期待你分享更多优质内容哦。"),
    DELIVER("deliver","商品发货通知","你所兑换的商品【%s】已发货\n" +
    "快递公司：%s\n" +
    "快递单号：%s\n" +
    "因快递时效性问题，无法保证具体的收货时间，请耐心等候。"),
    REMIND_CLEAR_ZERO("remindClearZero","积分失效通知","积分余额将于%s清空，请在有效期限内兑换使用，清空后无法补回。"),
    CLEAR_ZERO("clearZero","积分已清零通知","您的积分余额已清空，快去积分商城做任务换礼品吧！");

    private final String code;
    private final String title;
    private final String content;
    private static final LinkedHashMap<String, OfficalMsgEnums> map;
    OfficalMsgEnums(String code, String title, String content) {
        this.code = code;
        this.title = title;
        this.content = content;
    }

    public String getCode() {
        return code;
    }

    public String getContent() {
        return content;
    }

    public String getTitle() {
        return title;
    }

    static {
        map = new LinkedHashMap<>();
        for (OfficalMsgEnums officalMsgEnums : OfficalMsgEnums.values()) {
            map.put(officalMsgEnums.getCode(), officalMsgEnums);
        }
    }

    public static LinkedHashMap<String, OfficalMsgEnums> getMap() {
        return map;
    }
}
