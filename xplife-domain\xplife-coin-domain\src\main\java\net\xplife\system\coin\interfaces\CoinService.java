package net.xplife.system.coin.interfaces;

import net.xplife.system.coin.entity.*;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import net.xplife.system.coin.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = "yoyin-coin", configuration = FeignConfiguration.class)
public interface CoinService {
    /***
     * 保存商品信息
     * @param params
     * @return
     */
    @RequestMapping(value = "/coin/v1/goods/save", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public CoinGoods saveGoods(@RequestBody Map<String,Object> params);

    /**
     * 删除商品信息
     * @param id
     */
    @RequestMapping(value = "/coin/v1/goods/delete", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void deleteGoods(@RequestParam("id") String id);

    /**
     * 查询商品信息-分页
     * @param pageno
     * @param pagesize
     * @param type
     * @return
     */
    @RequestMapping(value = "/coin/v1/goods/findpage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<CoinGoods> findPageGoods(@RequestParam("pageno") String pageno, @RequestParam("pagesize") String pagesize, @RequestParam("type") String type);

    /**
     * 查询商品的类型
     * @return
     */
    @RequestMapping(value = "/coin/v1/goods/getgoodstypetab", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<Map<String, String>> getGoodsTypeTab();

    /***
     * 保存星币任务
     * @param params
     * @return
     */
    @RequestMapping(value = "/coin/v1/mission/save", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public CoinMission saveMission(@RequestBody Map<String,Object> params);

    /***
     * 删除星币任务
     * @param id
     */
    @RequestMapping(value = "/coin/v1/mission/delete", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void deleteMission(@RequestParam("id") String id);

    /**
     * 星币任务列表
     * @return
     */
    @RequestMapping(value = "/coin/v1/mission/list", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<CoinMission> findListMission();

    /**
     * 当前用户完成指定星币任务
     * @param code
     */
    @RequestMapping(value = "/coin/v1/coinuser/finishmission", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void finishMission(@RequestParam("code") String code);

    /***
     * 给指定用户发送一条关于“用户发表的动态被管理员删除”的消息
     * @param userId
     * @param title
     */
    @RequestMapping(value = "/coin/v1/coinuser/sendmsgdeletefeednote", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void sendMsgDeleteFeedNote(@RequestParam("userId") String userId, @RequestParam("title") String title);

    /***
     * 给指定用户发送一条关于“用户发表的动态被管理员评为精选”的消息
     * @param userId
     * @param title
     */
    @RequestMapping(value = "/coin/v1/coinuser/sendmsgchoicenssfeednote", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void sendMsgChoicenssFeedNote(@RequestParam("userId") String userId, @RequestParam("title") String title);

    /***
     * 查询用户兑换商品时产生的订单-分页
     * @param pageno
     * @param pagesize
     * @param userName
     * @return
     */
    @RequestMapping(value = "/coin/v1/order/findpage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<CoinOrder> findPageOrder(@RequestParam("pageno") String pageno, @RequestParam("pagesize") String pagesize, @RequestParam("userName") String userName);

    /***
     * 更新订单，填写发货地址等信息
     * @param id
     * @param deliveryStatus
     * @param deliveryCode
     * @param deliveryCompany
     * @return
     */
    @RequestMapping(value = "/coin/v1/order/update", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public CoinOrder updateCoinOrder(@RequestParam("id") String id, @RequestParam("deliveryStatus") String deliveryStatus, @RequestParam("deliveryCode") String deliveryCode, @RequestParam("deliveryCompany") String deliveryCompany);

    /***
     * 查询用户兑换过的字体商品
     * @param userId
     * @return
     */
    @RequestMapping(value = "/coin/v1/coinuser/userfonts", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<CoinGoodsDto> getFontByUserId(@RequestParam("userid") String userId);

    /***
     * 查询用户的星币情况
     * @param userId
     * @return
     */
    @RequestMapping(value = "/coin/v1/coinuser/info", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public CoinUserDto getUserInfo(@RequestParam("userid") String userId);

    /***
     * 查询单位时间内，获取星币最多的排名
     * @param beginDate
     * @param endDate
     * @param type
     * @param top
     * @return
     */
    @RequestMapping(value = "/coin/v1/coinuser/ranktop", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<CoinUserRankDto> getRankTop(@RequestParam("beginDate") String beginDate, @RequestParam("endDate") String endDate, @RequestParam("type") String type, @RequestParam("top") int top);

    /**
     * 自定义额外给用户增加积分数量
     * @param userId 用户id
     * @param num   增加的积分数量
     * @param info  描述
     */
    @RequestMapping(value = "/coin/v1/coinuser/incrbycustom", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void incrByCustom(@RequestParam("userId") String userId, @RequestParam("num") int num, @RequestParam("info") String info);

    /***
     * 时间范围内，满足完成某个任务次数的用户记录j
     * @param beginDate
     * @param endDate
     * @param type
     * @param count
     * @return
     */
    @RequestMapping(value = "/coin/v1/coinuser/getcounttaskbybetweendateandtype", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<CoinUserRankDto> findCountTaskByBetweenDateAndType(@RequestParam("beginDate") String beginDate, @RequestParam("endDate") String endDate, @RequestParam("type") String type, @RequestParam("count") int count);

    @RequestMapping(value = "/coin/v1/coinuser/getcoinuserpage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<CoinUser> findCoinUserPage(@RequestParam("pageno") int pageno, @RequestParam("pagesize") int pagesize);

    @RequestMapping(value = "/coin/v1/coinuser/getcoinincomelistbybetweendateandtypeanduserid", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<CoinIncome> getCoinIncomeListByBetweenDateAndTypeAndUserId(@RequestParam("beginDate") String beginDate, @RequestParam("endDate") String endDate, @RequestParam("type") String type, @RequestParam("userId") String userId);

    //-------------------------------------------------------------------------------------
    /***
     * 统计时间范围内积分任务每日的完成情况，统计所有用户
     * @param beginDate
     * @param endDate
     * @return
     */
    @RequestMapping(value = "/coin/v1/coinuser/getstatisticsmissionfinish", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<CoinStatisticsMissionFinishDto> getStatisticsMissionFinish(@RequestParam("beginDate") String beginDate, @RequestParam("endDate") String endDate);

    /***
     * 查询时间范围内积分商城商品的兑换情况，分页
     * @param beginDate
     * @param endDate
     * @return
     */
    @RequestMapping(value = "/coin/v1/coinuser/getstatisticsgoodschangepage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<CoinStatisticsGoodsChangesDto> getStatisticsCoinGoodsChangePage(@RequestParam("beginDate") String beginDate, @RequestParam("endDate") String endDate, @RequestParam("searchUserId") String userId, @RequestParam("pageno")int pageno, @RequestParam("pagesize")int pagesize);

    /***
     * 查询时间范围内积分商城商品的兑换情况，全部记录
     * @param beginDate
     * @param endDate
     * @param userId
     * @return
     */
    @RequestMapping(value = "/coin/v1/coinuser/getstatisticsgoodschangelist", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<CoinStatisticsGoodsChangesDto> getStatisticsCoinGoodsChangeList(@RequestParam("beginDate") String beginDate, @RequestParam("endDate") String endDate, @RequestParam("searchUserId") String userId);

    /***
     * 积分余额排名，如果userId不为空，则获取具体用户id的情况
     * @param top
     * @param userId
     * @return
     */
    @RequestMapping(value = "/coin/v1/coinuser/getstatisticsusercoinranklist", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<CoinStatisticsUserCoinRankDto> getStatisticsUserCoinRankList(@RequestParam("top") int top, @RequestParam("searchUserId") String userId);

    /**
     * 删除贴或评论时，倒扣积分接口
     * @param userId
     * @param missionCode
     * @param datetime
     */
    @RequestMapping(value = "/coin/v1/coinuser/cancelmission", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void cancelMission(@RequestParam("userId") String userId, @RequestParam("missionCode") String missionCode, @RequestParam("datetime") long datetime);

    /***
     * 删除订单
     * @param id
     */
    @RequestMapping(value = "/coin/v1/order/deletebyid", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void deleteOrderById(@RequestParam("id") String id);
}
