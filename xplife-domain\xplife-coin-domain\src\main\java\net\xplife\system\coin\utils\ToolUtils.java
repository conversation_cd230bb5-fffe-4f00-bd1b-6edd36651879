package net.xplife.system.coin.utils;

import cn.hutool.core.date.DateUtil;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2020/12/15 9:02
 * @description：
 * @modified By：
 * @version: $
 */
public class ToolUtils {
    public static String formatDate(Date date){
        if (date == null){
            return "";
        }
        Date curDate = new Date();
        //①当天：显示“时:分”   12:02
        if (DateUtil.format(date, "yyyy-MM-dd").equals(DateUtil.format(curDate, "yyyy-MM-dd"))){
            return DateUtil.format(date, "HH:mm");
        } else if (DateUtil.format(date, "yyyy").equals(DateUtil.format(curDate, "yyyy"))){
            //②同年、非当天：显示“月-日 时:分”   08-20  12:02
            return DateUtil.format(date, "MM-dd HH:mm");
        } else {
            //③非同年非当天：显示“年-月-日 时:分   2019-06-13  12:02
            return DateUtil.format(date, "yyyy-MM-dd HH:mm");
        }
    }
}
