package net.xplife.system.community.dto.activity;

import java.io.Serializable;

/**
 * 扑克牌信息类
 */
public class CardItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private Boolean isActive;

    private String content;

    private String frontImg;

    private String backImg;

    private String bigImg;

    public CardItem() {
        id = 0;
        isActive = false;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getActive() {
        return isActive;
    }

    public void setActive(Boolean active) {
        isActive = active;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFrontImg() {
        return frontImg;
    }

    public void setFrontImg(String frontImg) {
        this.frontImg = frontImg;
    }

    public String getBackImg() {
        return backImg;
    }

    public void setBackImg(String backImg) {
        this.backImg = backImg;
    }

    public String getBigImg() {
        return bigImg;
    }

    public void setBigImg(String bigImg) {
        this.bigImg = bigImg;
    }

}
