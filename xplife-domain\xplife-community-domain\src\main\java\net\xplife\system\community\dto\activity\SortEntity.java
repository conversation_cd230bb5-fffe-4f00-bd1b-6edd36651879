package net.xplife.system.community.dto.activity;

import java.io.Serializable;

/**
 * 排行榜排序实体
 */
public class SortEntity implements Serializable {

    /**
     * 用于存放其余拼接字段
     */
    private String entityString;
    /**
     * 第一个排序参数
     */
    private Integer compactor = null;
    /**
     * 第二个用于排序的参数
     */
    private Integer secondCompactor = null;
    /**
     * 第三个排序参数
     */
    private Long thirdCompactor = null;

    public SortEntity() {
    }

    public SortEntity(String entityString, Integer compactor, Integer secondCompactor, Long thirdCompactor) {
        this.entityString = entityString;
        this.compactor = compactor;
        this.secondCompactor = secondCompactor;
        this.thirdCompactor = thirdCompactor;
    }

    public Integer getCompactor() {
        return compactor;
    }

    public void setCompactor(Integer compactor) {
        this.compactor = compactor;
    }

    public Integer getSecondCompactor() {
        return secondCompactor;
    }

    public void setSecondCompactor(Integer secondCompactor) {
        this.secondCompactor = secondCompactor;
    }

    public String getEntityString() {
        return entityString;
    }

    public void setEntityString(String entityString) {
        this.entityString = entityString;
    }

    public Long getThirdCompactor() {
        return thirdCompactor;
    }

    public void setThirdCompactor(Long thirdCompactor) {
        this.thirdCompactor = thirdCompactor;
    }

    @Override
    public String toString() {
        return "SortEntity [entityString=" + entityString + ", compactor=" + compactor + ", secondCompactor=" + secondCompactor + ", thirdCompactor=" + thirdCompactor + "]";
    }

    /**
     * 功能：排序
     * @param a
     * @param b
     * @param desc  是否降序
     * @return
     */
    public static int compactor(Integer a, Integer b, boolean desc){
        if(desc) {
            return -compactorInteger(a, b);
        } else {
            return compactorInteger(a, b);
        }
    }

    /**
     * 功能：排序
     * @param a
     * @param b
     * @param desc  是否降序
     * @return
     */
    public static int compactorL(Long a, Long b, boolean desc){
        if(desc) {
            return -compactorLong(a, b);
        } else {
            return compactorLong(a, b);
        }
    }

    private static int compactorInteger(Integer a, Integer b){
        if(a > b){
            return 1;
        }
        if(a < b) {
            return -1;
        }
        return 0;
    }

    private static int compactorLong(Long a, Long b){
        if(a > b){
            return 1;
        }
        if(a < b) {
            return -1;
        }
        return 0;
    }

}