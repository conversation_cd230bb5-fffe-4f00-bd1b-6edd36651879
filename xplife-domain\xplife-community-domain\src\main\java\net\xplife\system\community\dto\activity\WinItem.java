package net.xplife.system.community.dto.activity;

import java.io.Serializable;

/**
 * 中奖信息
 */
public class WinItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 昵称
     */
    private String userName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 排名
     */
    private Integer ranking;

    /**
     * 已集套数
     */
    private Integer totalSets;

    /**
     * 总张数
     */
    private Integer totalCards;

    /**
     * 排名时间
     */
    private long rankTime;

    public WinItem() {
        ranking = 0;
        totalSets = 0;
        totalCards = 0;
        rankTime = 0;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public Integer getRanking() {
        return ranking;
    }

    public void setRanking(Integer ranking) {
        this.ranking = ranking;
    }

    public Integer getTotalSets() {
        return totalSets;
    }

    public void setTotalSets(Integer totalSets) {
        this.totalSets = totalSets;
    }

    public Integer getTotalCards() {
        return totalCards;
    }

    public void setTotalCards(Integer totalCards) {
        this.totalCards = totalCards;
    }

    public long getRankTime() {
        return rankTime;
    }

    public void setRankTime(long rankTime) {
        this.rankTime = rankTime;
    }

}
