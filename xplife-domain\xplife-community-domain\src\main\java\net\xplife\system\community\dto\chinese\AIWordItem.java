package net.xplife.system.community.dto.chinese;

import java.io.Serializable;

/**
 * 智能联想输入检索类
 */
public class AIWordItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String name;
    /**
     * 诗词作者(type为poem时)
     */
    private String author;
    /**
     * 诗词内容(type为poem时)
     */
    private String body;
    /**
     * term  单字, 词语；idiom  成语；query  空白；poem  空白（诗词）
     */
    private Integer type;
    /**
     * 诗词连接标识
     */
    private String id;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

}
