package net.xplife.system.community.dto.chinese;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class FavoritesGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    public FavoritesGroup() {
        super();
        this.items = new ArrayList<>();
    }

    /**
     * 分组
     */
    private String group;

    /**
     * 列表项
     */
    private List<FavoritesItem> items;

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public List<FavoritesItem> getItems() {
        return items;
    }

    public void setItems(List<FavoritesItem> items) {
        this.items = items;
    }

}
