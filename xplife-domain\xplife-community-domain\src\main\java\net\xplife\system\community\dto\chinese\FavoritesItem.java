package net.xplife.system.community.dto.chinese;

import java.io.Serializable;

public class FavoritesItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 类型：0 为全部 1 为单字 2 为词语 3 为成语 4 为诗词 5 为作文
     */
    private int type;
    /**
     * 关键字：单字 word, 词语 term 成语 term 诗词 pid 作文 cid
     */
    private String key;
    /**
     * 拼音（单字, 词语，成语）
     */
    private String pinyin;

    /**
     * 关键字显示文本
     */
    private String title;
    /**
     * （诗词和作文）内容摘要
     */
    private String content;
    /**
     * 标贴（诗词保存朝代和作者，作文保存相关标签）
     */
    private String tags;

    /**
     * 作文打印次数（需要服务端统计）
     */
    private int printCount;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public int getPrintCount() {
        return printCount;
    }

    public void setPrintCount(int printCount) {
        this.printCount = printCount;
    }

}
