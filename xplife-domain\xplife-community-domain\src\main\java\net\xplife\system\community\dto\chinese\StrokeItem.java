package net.xplife.system.community.dto.chinese;

import java.io.Serializable;

/**
 * 笔画信息
 */
public class StrokeItem implements Serializable {

    private static final long serialVersionUID = 1L;

    public StrokeItem() {
        super();
    }

    public StrokeItem(String name, String pinyin, String sampleChar, String strokePicUrl, String samplePicUrl, String audioUrl) {
        super();
        this.name = name;
        this.pinyin = pinyin;
        this.sampleChar = sampleChar;
        this.strokePicUrl = strokePicUrl;
        this.samplePicUrl = samplePicUrl;
        this.audioUrl = audioUrl;
    }

    /**
     * 名称
     */
    private String name;
    /**
     * 拼音
     */
    private String pinyin;
    /**
     * 笔画图片链接URL
     */
    private String strokePicUrl;
    /**
     * 笔画样例图片链接URL
     */
    private String samplePicUrl;
    /**
     * 笔画样例字
     */
    private String sampleChar;
    /**
     * 音频链接URL
     */
    private String audioUrl;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getStrokePicUrl() {
        return strokePicUrl;
    }

    public void setStrokePicUrl(String strokePicUrl) {
        this.strokePicUrl = strokePicUrl;
    }

    public String getSamplePicUrl() {
        return samplePicUrl;
    }

    public void setSamplePicUrl(String samplePicUrl) {
        this.samplePicUrl = samplePicUrl;
    }

    public String getSampleChar() {
        return sampleChar;
    }

    public void setSampleChar(String sampleChar) {
        this.sampleChar = sampleChar;
    }

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

}
