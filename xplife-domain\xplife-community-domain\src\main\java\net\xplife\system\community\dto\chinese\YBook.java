package net.xplife.system.community.dto.chinese;

import com.alibaba.fastjson.annotation.JSONField;
import java.io.Serializable;
import java.util.List;

/**
 * 教材类
 */
public class YBook implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询参数 grade
     */
    @JSONField(name = "param")
    public String Param;

    /**
     * 分类值
     */
    @JSONField(name = "cate")
    public String Cate;

    /**
     * 分类显示名称
     */
    @JSONField(name = "display_name")
    public String DisplayName;

    /**
     * 年级列表
     */
    public List<YGrade> items;

    public String getParam() {
        return Param;
    }

    public void setParam(String param) {
        Param = param;
    }

    public String getCate() {
        return Cate;
    }

    public void setCate(String cate) {
        Cate = cate;
    }

    public String getDisplayName() {
        return DisplayName;
    }

    public void setDisplayName(String displayName) {
        DisplayName = displayName;
    }

    public List<YGrade> getItems() {
        return items;
    }

    public void setItems(List<YGrade> items) {
        this.items = items;
    }

}