package net.xplife.system.community.dto.chinese;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 教材类
 */
public class YBookItem implements Serializable {

    private static final long serialVersionUID = 1L;

    public YBookItem() {
        super();
    }

    public YBookItem(String name, String value) {
        super();
        this.name = name;
        this.value = value;
        this.items = new ArrayList<>();
    }

    /**
     * 教材类型名称
     */
    public String name;

    /**
     * 教材类型值
     */
    public String value;

    /**
     * 年级列表
     */
    public List<YGradeItem> items;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public List<YGradeItem> getItems() {
        return items;
    }

    public void setItems(List<YGradeItem> items) {
        this.items = items;
    }

}
