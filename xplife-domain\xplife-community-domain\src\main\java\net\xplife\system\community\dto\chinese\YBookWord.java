package net.xplife.system.community.dto.chinese;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class YBook<PERSON>ord implements Serializable {

    private static final long serialVersionUID = 1L;

    public YBookWord() {
        super();
    }

    public YBookWord(String name, Integer value) {
        super();
        this.name = name;
        this.value = value;
        this.words = new ArrayList<>();
    }

    /**
     * 课文标题
     */
    public String name;

    /**
     * 值
     */
    public Integer value;

    /**
     * 字或词语列表
     */
    public List<YBookWordItem> words;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public List<YBookWordItem> getWords() {
        return words;
    }

    public void setWords(List<YBookWordItem> words) {
        this.words = words;
    }

}
