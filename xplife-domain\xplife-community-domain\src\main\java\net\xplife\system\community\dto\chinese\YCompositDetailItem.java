package net.xplife.system.community.dto.chinese;

import java.io.Serializable;
import java.util.List;

/**
 * 作文详情信息
 */
public class YCompositDetailItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    public String title;

    /**
     * 内容
     */
    public String content;

    /**
     * 文字数量
     */
    public String count;

    /**
     * 文体  category(0).title
     */
    public String category;

    /**
     * 年级 grade(0).title
     */
    public String grade;

    /**
     * 标签 tags(0).title
     */
    public String tags;

    /**
     * 打印次数（需要服务端统计）
     */
    public String printcount;

    /**
     * 老师点评
     */
    public String comments;
    /**
     * 得分
     */
    public String score;

    /**
     * 相关作文列表
     */
    private List<YCompositRecoItem> recommends;

    /**
     * 收藏ID
     */
    private String favoritesId;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getPrintcount() {
        return printcount;
    }

    public void setPrintcount(String printcount) {
        this.printcount = printcount;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public List<YCompositRecoItem> getRecommends() {
        return recommends;
    }

    public void setRecommends(List<YCompositRecoItem> recommends) {
        this.recommends = recommends;
    }

    public String getFavoritesId() {
        return favoritesId;
    }

    public void setFavoritesId(String favoritesId) {
        this.favoritesId = favoritesId;
    }

}
