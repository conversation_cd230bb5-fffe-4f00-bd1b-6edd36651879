package net.xplife.system.community.dto.chinese;

import java.io.Serializable;

/**
 * 作文列表项信息
 */
public class YCompositListItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    public String title;

    /**
     * 详情链接标识 sid
     */
    public String value;

    /**
     * 内容
     */
    public String content;

    /**
     * 文字数量
     */
    public String count;

    /**
     * 文体  category(0).title
     */
    public String category;

    /**
     * 年级 grade(0).title
     */
    public String grade;

    /**
     * 标签 tags(0).title
     */
    public String tags;

    /**
     * 打印次数（需要服务端统计）
     */
    public String printcount;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getPrintcount() {
        return printcount;
    }

    public void setPrintcount(String printcount) {
        this.printcount = printcount;
    }

}
