package net.xplife.system.community.dto.chinese;

import com.alibaba.fastjson.annotation.JSONField;
import java.io.Serializable;

/**
 * 年级
 */
public class YGrade implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 值
     */
    @JSONField(name = "value")
    public String value;

    /**
     * 显示名称
     */
    @JSONField(name = "display_name")
    public String name;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}