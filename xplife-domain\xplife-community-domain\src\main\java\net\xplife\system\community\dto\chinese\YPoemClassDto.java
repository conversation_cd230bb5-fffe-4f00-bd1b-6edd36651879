package net.xplife.system.community.dto.chinese;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class YPoemClassDto implements Serializable {

    private static final long serialVersionUID = 1L;

    public YPoemClassDto() {
        super();
        this.grades = new ArrayList<>();
        this.dynasties = new ArrayList<>();
        this.authors = new ArrayList<>();
    }

    /**
     * 年级列表
     */
    private List<YPoemGradeItem> grades;
    /**
     * 朝代列表
     */
    private List<String> dynasties;
    /**
     * 作者列表
     */
    private List<YPoemClassItem> authors;

    public List<YPoemGradeItem> getGrades() {
        return grades;
    }

    public void setGrades(List<YPoemGradeItem> grades) {
        this.grades = grades;
    }

    public List<String> getDynasties() {
        return dynasties;
    }

    public void setDynasties(List<String> dynasties) {
        this.dynasties = dynasties;
    }

    public List<YPoemClassItem> getAuthors() {
        return authors;
    }

    public void setAuthors(List<YPoemClassItem> authors) {
        this.authors = authors;
    }

}
