package net.xplife.system.community.dto.chinese;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class YPoemClassItem implements Serializable {
    private static final long serialVersionUID = 1L;

    private String group;
    private String value;
    private List<String> items;

    public YPoemClassItem() {
        super();
        this.items = new ArrayList<>();
    }

    public YPoemClassItem(String group, String value) {
        super();
        this.group = group;
        this.value = value;
        this.items = new ArrayList<>();
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public List<String> getItems() {
        return items;
    }

    public void setItems(List<String> items) {
        this.items = items;
    }

}
