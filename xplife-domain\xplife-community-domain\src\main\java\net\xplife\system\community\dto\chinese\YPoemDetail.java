package net.xplife.system.community.dto.chinese;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 诗词详情信息
 */
public class YPoemDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    public YPoemDetail() {
        super();
        this.means = new ArrayList<>();
    }

    private String value;

    /**
     * 标题 display_name
     */
    public String title;

    /**
     * 内容
     */
    public String body;

    /**
     * 朝代
     */
    public String dynasty;

    /**
     * 作者 literature_author(0).display_name
     */
    public String author;

    /**
     * 声音文件地址  recite_url(0)
     */
    public String audioUrl;

    /**
     * 赏析 shangxi(0).text
     */
    public String shangxi;

    /**
     * 原文+译文
     */
    public List<YPoemDetailItem> means;

    /**
     * 收藏ID
     */
    private String favoritesId;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getDynasty() {
        return dynasty;
    }

    public void setDynasty(String dynasty) {
        this.dynasty = dynasty;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public String getShangxi() {
        return shangxi;
    }

    public void setShangxi(String shangxi) {
        this.shangxi = shangxi;
    }

    public List<YPoemDetailItem> getMeans() {
        return means;
    }

    public void setMeans(List<YPoemDetailItem> means) {
        this.means = means;
    }

    public String getFavoritesId() {
        return favoritesId;
    }

    public void setFavoritesId(String favoritesId) {
        this.favoritesId = favoritesId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
