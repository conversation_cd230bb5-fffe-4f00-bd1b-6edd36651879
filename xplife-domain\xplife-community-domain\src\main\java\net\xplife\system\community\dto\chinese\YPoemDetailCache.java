package net.xplife.system.community.dto.chinese;

import java.io.Serializable;

public class YPoemDetailCache implements Serializable {

    /**
     * 百度诗词详情标识
     */
    private String pid;

    /**
     * MD5 (标题 + 作者)
     */
    private String hash;

    /**
     * 排序号
     */
    private int sort;

    /**
     * 年级 唐诗三百首 宋词三百首
     */
    private int grade;

    /**
     * 详情
     */
    private YPoemDetail detail;

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getGrade() {
        return grade;
    }

    public void setGrade(int grade) {
        this.grade = grade;
    }

    public YPoemDetail getDetail() {
        return detail;
    }

    public void setDetail(YPoemDetail detail) {
        this.detail = detail;
    }

    /**
     * 标题
     */
    public String getTitle() {
        if (detail != null) {
            return detail.getTitle();
        }
        return null;
    }

    /**
     * 作者
     */
    public String getAuthor() {
        if (detail != null) {
            return detail.getAuthor();
        }
        return null;
    }

    /**
     * 朝代
     */
    public String getDynasty() {
        if (detail != null) {
            return detail.getDynasty();
        }
        return null;
    }

}
