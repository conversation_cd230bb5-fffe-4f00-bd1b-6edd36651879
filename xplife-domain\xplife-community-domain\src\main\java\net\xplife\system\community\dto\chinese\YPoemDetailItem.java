package net.xplife.system.community.dto.chinese;


import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 诗词详情项信息
 */
public class YPoemDetailItem implements Serializable {

    private static final long serialVersionUID = 1L;

    public YPoemDetailItem() {
        super();
        this.source = StringUtils.EMPTY;
        this.translate = StringUtils.EMPTY;
    }

    /**
     * 段落号
     */
    public String paragraph;

    /**
     * 原文
     */
    public String source;

    /**
     * 译文
     */
    public String translate;

    public String getParagraph() {
        return paragraph;
    }

    public void setParagraph(String paragraph) {
        this.paragraph = paragraph;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTranslate() {
        return translate;
    }

    public void setTranslate(String translate) {
        this.translate = translate;
    }

}
