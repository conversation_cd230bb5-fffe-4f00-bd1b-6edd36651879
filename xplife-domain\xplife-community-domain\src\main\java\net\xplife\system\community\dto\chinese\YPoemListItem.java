package net.xplife.system.community.dto.chinese;

import java.io.Serializable;

/**
 * 诗词列表项信息
 */
public class YPoemListItem implements Serializable {

    private static final long serialVersionUID = 1L;

    public YPoemListItem() {
        super();
    }

    public YPoemListItem(String title, String body, String dynasty, String author, String value) {
        super();
        this.title = title;
        this.body = body;
        this.dynasty = dynasty;
        this.author = author;
        this.value =  value;
    }

    /**
     * 标题 display_name
     */
    public String title;

    /**
     * 内容
     */
    public String body;

    /**
     * 朝代
     */
    public String dynasty;

    /**
     * 作者 literature_author
     */
    public String author;

    /**
     * 详情链接键值 sid
     */
    public String value;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getDynasty() {
        return dynasty;
    }

    public void setDynasty(String dynasty) {
        this.dynasty = dynasty;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}
