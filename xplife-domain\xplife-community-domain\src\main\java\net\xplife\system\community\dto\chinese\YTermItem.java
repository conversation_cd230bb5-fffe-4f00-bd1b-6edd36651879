package net.xplife.system.community.dto.chinese;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class YTermItem implements Serializable {

    private static final long serialVersionUID = 1L;

    public YTermItem() {
        this.words = new ArrayList<>();
        this.antonyms = new ArrayList<>();
        this.synonyms = new ArrayList<>();
        this.sentences = new ArrayList<>();
    }

    /**
     * 名称
     */
    private String name;

    /**
     * 拼音
     */
    private String pinyin;
    /**
     * 声音地址
     */
    private String audioUrl;
    /**
     * 基本释义
     */
    private String basicDef;

    /**
     * 笔画数量
     */
    private String strokeCount;

    /**
     * 详细释义 (有html标贴，需要webView控件)
     */
    private String detailMean;

    /**
     * 文字列表
     */
    private List<YTermWordItem> words;

    /**
     * 相关反义词列表
     */
    private List<String> antonyms;
    /**
     * 相关同义词列表
     */
    private List<String> synonyms;

    /**
     * 相关句子列表
     */
    private List<String> sentences;

    /**
     * 收藏ID
     */
    private String favoritesId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public String getBasicDef() {
        return basicDef;
    }

    public void setBasicDef(String basicDef) {
        this.basicDef = basicDef;
    }

    public String getStrokeCount() {
        return strokeCount;
    }

    public void setStrokeCount(String strokeCount) {
        this.strokeCount = strokeCount;
    }

    public String getDetailMean() {
        return detailMean;
    }

    public void setDetailMean(String detailMean) {
        this.detailMean = detailMean;
    }

    public List<YTermWordItem> getWords() {
        return words;
    }

    public void setWords(List<YTermWordItem> words) {
        this.words = words;
    }

    public List<String> getAntonyms() {
        return antonyms;
    }

    public void setAntonyms(List<String> antonyms) {
        this.antonyms = antonyms;
    }

    public List<String> getSynonyms() {
        return synonyms;
    }

    public void setSynonyms(List<String> synonyms) {
        this.synonyms = synonyms;
    }

    public List<String> getSentences() {
        return sentences;
    }

    public void setSentences(List<String> sentences) {
        this.sentences = sentences;
    }

    public String getFavoritesId() {
        return favoritesId;
    }

    public void setFavoritesId(String favoritesId) {
        this.favoritesId = favoritesId;
    }

}
