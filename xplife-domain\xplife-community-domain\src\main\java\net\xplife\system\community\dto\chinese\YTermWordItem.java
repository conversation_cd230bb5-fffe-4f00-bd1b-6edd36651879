package net.xplife.system.community.dto.chinese;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class YTermWordItem implements Serializable {

    private static final long serialVersionUID = 1L;

    public YTermWordItem() {
        this.strokeOrderSvgUrls = new ArrayList<>();
    }

    public YTermWordItem(String name, String strokeCount) {
        this.name = name;
        this.strokeCount = strokeCount;
        this.strokeOrderSvgUrls = new ArrayList<>();
    }

    /**
     * 名称
     */
    private String name;
    /**
     * 笔画数量
     */
    private String strokeCount;
    /**
     * 笔顺Svg文件Url地址
     */
    private List<String> strokeOrderSvgUrls;

    public String getStrokeCount() {
        return strokeCount;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setStrokeCount(String strokeCount) {
        this.strokeCount = strokeCount;
    }

    public List<String> getStrokeOrderSvgUrls() {
        return strokeOrderSvgUrls;
    }

    public void setStrokeOrderSvgUrls(List<String> strokeOrderSvgUrls) {
        this.strokeOrderSvgUrls = strokeOrderSvgUrls;
    }

}
