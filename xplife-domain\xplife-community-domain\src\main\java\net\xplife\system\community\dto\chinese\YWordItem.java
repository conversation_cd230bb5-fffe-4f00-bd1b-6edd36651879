package net.xplife.system.community.dto.chinese;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class YWordItem implements Serializable {

    private static final long serialVersionUID = 1L;

    public YWordItem() {
        this.pinyins = new ArrayList<>();
        this.strokeOrderSvgUrls = new ArrayList<>();
    }

    /**
     * 名称
     */
    private String name;

    /**
     * 拼音列表（多个表示是多音字）
     */
    private List<YWordPinyin> pinyins;

    /**
     * 部首
     */
    private String radicals;

    /**
     * 结构
     */
    private String structType;

    /**
     * 笔画数量
     */
    private String strokeCount;

    /**
     * 笔顺动画GIF图片地址
     */
    private String strokeOrderGifUrl;

    /**
     * 详细释义 (有html标贴，需要webView控件)
     */
    private String detailMean;

    /**
     * 笔顺Svg文件Url地址
     */
    private List<String> strokeOrderSvgUrls;

    /**
     * 收藏ID
     */
    private String favoritesId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<YWordPinyin> getPinyins() {
        return pinyins;
    }

    public void setPinyins(List<YWordPinyin> pinyins) {
        this.pinyins = pinyins;
    }

    public String getRadicals() {
        return radicals;
    }

    public void setRadicals(String radicals) {
        this.radicals = radicals;
    }

    public String getStructType() {
        return structType;
    }

    public void setStructType(String structType) {
        this.structType = structType;
    }

    public String getStrokeCount() {
        return strokeCount;
    }

    public void setStrokeCount(String strokeCount) {
        this.strokeCount = strokeCount;
    }

    public String getStrokeOrderGifUrl() {
        return strokeOrderGifUrl;
    }

    public void setStrokeOrderGifUrl(String strokeOrderGifUrl) {
        this.strokeOrderGifUrl = strokeOrderGifUrl;
    }

    public String getDetailMean() {
        return detailMean;
    }

    public void setDetailMean(String detailMean) {
        this.detailMean = detailMean;
    }

    public List<String> getStrokeOrderSvgUrls() {
        return strokeOrderSvgUrls;
    }

    public void setStrokeOrderSvgUrls(List<String> strokeOrderSvgUrls) {
        this.strokeOrderSvgUrls = strokeOrderSvgUrls;
    }

    public String getFavoritesId() {
        return favoritesId;
    }

    public void setFavoritesId(String favoritesId) {
        this.favoritesId = favoritesId;
    }

}
