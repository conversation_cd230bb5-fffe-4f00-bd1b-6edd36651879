package net.xplife.system.community.dto.chinese;

import java.io.Serializable;

public class YWordPinyin implements Serializable {

    private static final long serialVersionUID = 1L;

    public YWordPinyin() {
        super();
    }

    public YWordPinyin(String pinyin, String audioUrl, String basicDef) {
        super();
        this.pinyin = pinyin;
        this.audioUrl = audioUrl;
        this.audioUrl = audioUrl;
    }

    /**
     * 拼音
     */
    private String pinyin;
    /**
     * 声音地址
     */
    private String audioUrl;
    /**
     * 基本释义
     */
    private String basicDef;

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public String getBasicDef() {
        return basicDef;
    }

    public void setBasicDef(String basicDef) {
        this.basicDef = basicDef;
    }

}
