package net.xplife.system.community.dto.device;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
import net.xplife.system.account.dto.user.UserAccountDto;
import net.xplife.system.account.dto.user.UserInfoDto;
import net.xplife.system.account.dto.user.UserLoginDto;
import org.springframework.data.mongodb.core.index.Indexed;

public class ConnectLogDto implements Serializable {
    /**
     * 设备连接日志Dto
     */
    private static final long serialVersionUID = 1L;
    private String             userId;                           // 用户ID
    private UserLoginDto        userInfoDto;
    private String             macAddress;                      // 设备mac地址
    private String             deviceSn;                              // 设备SN
    private String             deviceName;                            // 设备名称
    private String             deviceVersion;                   // 设备固件版本号
    private String             appVersion;                      // 应用版本
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date               lastestTime;                     // 最后连接时间

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceVersion() {
        return deviceVersion;
    }

    public void setDeviceVersion(String deviceVersion) {
        this.deviceVersion = deviceVersion;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public Date getLastestTime() {
        return lastestTime;
    }

    public void setLastestTime(Date lastestTime) {
        this.lastestTime = lastestTime;
    }

    public UserLoginDto getUserInfoDto() {
        return userInfoDto;
    }

    public void setUserInfoDto(UserLoginDto userInfoDto) {
        this.userInfoDto = userInfoDto;
    }
}
