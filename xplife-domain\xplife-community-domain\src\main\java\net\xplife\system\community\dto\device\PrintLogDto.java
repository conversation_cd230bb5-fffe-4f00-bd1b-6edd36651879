package net.xplife.system.community.dto.device;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
public class PrintLogDto implements Serializable {
    /**
     * 设备打印日志Dto
     */
    private static final long serialVersionUID = 1L;
    private String            userId;               // 用户ID
    private String            deviceId;             // 设备ID
    private String            deviceName;           // 设备名称
    private String            deviceVersion;        // 设备版本
    private String            appVersion;           // 应用版本
    private String            pic;                  // 打印图片地址
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createtime;           // 创建时间

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceVersion() {
        return deviceVersion;
    }

    public void setDeviceVersion(String deviceVersion) {
        this.deviceVersion = deviceVersion;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }
}
