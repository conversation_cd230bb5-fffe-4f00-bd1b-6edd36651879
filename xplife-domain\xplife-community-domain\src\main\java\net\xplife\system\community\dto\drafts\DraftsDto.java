package net.xplife.system.community.dto.drafts;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
public class DraftsDto implements Serializable {
    /**
     * 草稿箱Dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            pic;                  // 资源图片地址
    private String            data;                 // 资源数据地址
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createTime;           // 创建 时间
    private Object            param;                // 草稿箱参数
    private int               type;                 // 草稿箱类型 0--草稿箱 1--我的历史
    private int               placeType;            // 编辑方向，可选，默认0，打竖，1为横向
    private int               driverType;           // 打印设备：0为默认，星星机机；1：A4打印机
    private String            printerType;          // 打印机型号
    private String            subType;              // 草稿箱副类型 0编辑纸条 1清单 2大字横幅 3便利贴 4网页打印
    private String            version;

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Object getParam() {
        return param;
    }

    public void setParam(Object param) {
        this.param = param;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getPlaceType() {return placeType; }

    public void setPlaceType(int placeType) { this.placeType = placeType; }

    public int getDriverType() {
        return driverType;
    }

    public void setDriverType(int driverType) {
        this.driverType = driverType;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
