package net.xplife.system.community.dto.drafts;
import java.io.Serializable;
public class PrintRecordDto implements Serializable {
    /**
     * 打印记录Dto
     */
    private static final long serialVersionUID = 1L;
    private String            shareId;              // 共享打印ID
    private String            pic;                  // 资源地址
    private String            nickName;             // 回复人昵称
    private String            userPic;              // 回复人头像
    private String            sex;                  // 回复人性别
    private int               anonymous;            // 是否匿名
    private String            from;                 // 回复来源 wx qq mom sina

    public String getShareId() {
        return shareId;
    }

    public void setShareId(String shareId) {
        this.shareId = shareId;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getUserPic() {
        return userPic;
    }

    public void setUserPic(String userPic) {
        this.userPic = userPic;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public int getAnonymous() {
        return anonymous;
    }

    public void setAnonymous(int anonymous) {
        this.anonymous = anonymous;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }
}
