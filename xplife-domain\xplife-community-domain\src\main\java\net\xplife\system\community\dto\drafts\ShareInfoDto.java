package net.xplife.system.community.dto.drafts;
import java.io.Serializable;
public class ShareInfoDto implements Serializable {
    /**
     * 分享信息dto--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private String            title;                // 标题
    private String            content;              // 内容
    private String            iconUrl;              // 小图标地址
    private String            shareUrl;             // 分享地址
    private int               type;                 // 分享类型 1共享打印
    private String            callbackUrl;          // 调用接口地址

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }
}
