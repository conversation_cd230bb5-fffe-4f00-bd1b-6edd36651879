package net.xplife.system.community.dto.drafts;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
public class SharedPrintDto implements Serializable {
    /**
     * 共享打印
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            title;                // 标题
    private String            content;              // 内容
    private String            icon;                 // 图标
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              recoveryTime;         // 回复时间
    private int               totalTime;            // 总时长
    private int               currentTime;          // 当前时长
    private int               isRed;                // 是否标记小红点
    private int               isOfficial;           // 0：普通用户； 1：官方认证

    public int getIsRed() {
        return isRed;
    }

    public void setIsRed(int isRed) {
        this.isRed = isRed;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Date getRecoveryTime() {
        return recoveryTime;
    }

    public void setRecoveryTime(Date recoveryTime) {
        this.recoveryTime = recoveryTime;
    }

    public int getTotalTime() {
        return totalTime;
    }

    public void setTotalTime(int totalTime) {
        this.totalTime = totalTime;
    }

    public int getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(int currentTime) {
        this.currentTime = currentTime;
    }

    public int getIsOfficial() { return isOfficial; }

    public void setIsOfficial(int isOfficial) { this.isOfficial = isOfficial; }
}
