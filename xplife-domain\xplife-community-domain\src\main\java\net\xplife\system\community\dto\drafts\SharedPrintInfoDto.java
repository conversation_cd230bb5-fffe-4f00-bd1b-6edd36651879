package net.xplife.system.community.dto.drafts;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
public class SharedPrintInfoDto implements Serializable {
    /**
     * 共享打印
     */
    private static final long serialVersionUID = 1L;
    private String            shareId;              // 记录ID
    private String            title;                // 标题
    private String            userName;             // 用户名称
    private String            userPic;              // 用户头像
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              validityTime;         // 有效期

    public String getShareId() {
        return shareId;
    }

    public void setShareId(String shareId) {
        this.shareId = shareId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPic() {
        return userPic;
    }

    public void setUserPic(String userPic) {
        this.userPic = userPic;
    }

    public Date getValidityTime() {
        return validityTime;
    }

    public void setValidityTime(Date validityTime) {
        this.validityTime = validityTime;
    }
}
