package net.xplife.system.community.dto.examinationresource;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/7 16:50
 * @description：
 * @modified By：
 * @version: $
 */
public class ExaminationResourceDto {
    private String                      id;
    private String                      title;           // 标题
    private String                      headUrl;         // 封面
    private String                      fileUrl;         // pdf的url地址
    private String                      subject;         // 学科
    private String                      subjectName;     // 学科的名称，冗余字段，用于搜索
    private String                      gradeLevel;      // 年级，xc小学、cc初中、gz高中、dx大学
    private String                      gradeLevelName;  // 年级的名称，冗余字段，用于搜索
    private String                      examType;        // 试题类型
    private String                      examTypeName;    // 试题类型名称，冗余字段，用于搜索

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getGradeLevel() {
        return gradeLevel;
    }

    public void setGradeLevel(String gradeLevel) {
        this.gradeLevel = gradeLevel;
    }

    public String getGradeLevelName() {
        return gradeLevelName;
    }

    public void setGradeLevelName(String gradeLevelName) {
        this.gradeLevelName = gradeLevelName;
    }

    public String getExamType() {
        return examType;
    }

    public void setExamType(String examType) {
        this.examType = examType;
    }

    public String getExamTypeName() {
        return examTypeName;
    }

    public void setExamTypeName(String examTypeName) {
        this.examTypeName = examTypeName;
    }
}
