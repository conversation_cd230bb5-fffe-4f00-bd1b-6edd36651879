package net.xplife.system.community.dto.feed;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
public class AddFeedNoteDto implements Serializable {
    /**
     * 发布动态dto
     */
    private static final long serialVersionUID = 1L;
    private String            content;              // 内容
    private List<String>      pic;                  // 图片地址
    private List<String>      labelIds;             // 标签ID
    private int               isLabel;              // 是否加标签
    private String            title;                // 标题
    private String            gradeLevel;           // 年级
    private String            subject;             // 学科
    private Integer           gradeNum;             // 具体年级1-12
    private Date            delayedTime;          // 延迟发布时间

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getPic() {
        return pic;
    }

    public void setPic(List<String> pic) {
        this.pic = pic;
    }

    public List<String> getLabelIds() {
        return labelIds;
    }

    public void setLabelIds(List<String> labelIds) {
        this.labelIds = labelIds;
    }

    public int getIsLabel() {
        return isLabel;
    }

    public void setIsLabel(int isLabel) {
        this.isLabel = isLabel;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getGradeLevel() {
        return gradeLevel;
    }

    public void setGradeLevel(String gradeLevel) {
        this.gradeLevel = gradeLevel;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public Integer getGradeNum() {
        return gradeNum;
    }

    public void setGradeNum(Integer gradeNum) {
        this.gradeNum = gradeNum;
    }

    public Date getDelayedTime() {
        return delayedTime;
    }

    public void setDelayedTime(Date delayedTime) {
        this.delayedTime = delayedTime;
    }
}
