package net.xplife.system.community.dto.feed;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import com.alibaba.fastjson.annotation.JSONField;
import net.xplife.system.community.utils.ToolUtils;

public class CommentReplyDto implements Serializable {
    /**
     * 评论回复dto
     */
    private static final long serialVersionUID = 1L;
    private String            commentId;            // 评论ID
    private String            replyUserId;          // 回复人ID
    private String            replyUserName;        // 回复人名称
    private String            replyUserPic;         // 回复人头像
    private String            content;              // 回复内容
    private String            replyId;              // 回复ID
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              replyDate;            // 回复时间
    private String            replyObjId;           // 回复对象ID
    private String            replyObjName;         // 回复对象名称
    private int               isOfficial;           // 0:普通用户；1：官方认证
    private Map<String, Object> userTitleObj;       // 用户头衔对象，id，name，nameUrl，borderUrl
    private String            fmtTime;              // 格式化回复时间

    public String getReplyId() {
        return replyId;
    }

    public void setReplyId(String replyId) {
        this.replyId = replyId;
    }

    public String getCommentId() {
        return commentId;
    }

    public void setCommentId(String commentId) {
        this.commentId = commentId;
    }

    public String getReplyUserId() {
        return replyUserId;
    }

    public void setReplyUserId(String replyUserId) {
        this.replyUserId = replyUserId;
    }

    public String getReplyUserName() {
        return replyUserName;
    }

    public void setReplyUserName(String replyUserName) {
        this.replyUserName = replyUserName;
    }

    public String getReplyUserPic() {
        return replyUserPic;
    }

    public void setReplyUserPic(String replyUserPic) {
        this.replyUserPic = replyUserPic;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getReplyDate() {
        return replyDate;
    }

    public void setReplyDate(Date replyDate) {
        this.replyDate = replyDate;
    }

    public String getReplyObjId() {
        return replyObjId;
    }

    public void setReplyObjId(String replyObjId) {
        this.replyObjId = replyObjId;
    }

    public String getReplyObjName() {
        return replyObjName;
    }

    public void setReplyObjName(String replyObjName) {
        this.replyObjName = replyObjName;
    }

    public int getIsOfficial() { return isOfficial; }

    public void setIsOfficial(int isOfficial) { this.isOfficial = isOfficial; }

    public String getFmtTime() {
        return ToolUtils.formatDate(this.replyDate);
//        return fmtTime;
    }

    public void setFmtTime(String fmtTime) {
        this.fmtTime = fmtTime;
    }

    public Map<String, Object> getUserTitleObj() {
        return userTitleObj;
    }

    public void setUserTitleObj(Map<String, Object> userTitleObj) {
        this.userTitleObj = userTitleObj;
    }
}
