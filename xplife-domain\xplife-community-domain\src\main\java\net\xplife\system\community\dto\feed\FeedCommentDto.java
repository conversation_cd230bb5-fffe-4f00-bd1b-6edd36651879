package net.xplife.system.community.dto.feed;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.annotation.JSONField;
import net.xplife.system.community.utils.ToolUtils;

public class FeedCommentDto implements Serializable {
    /**
     * 动态评论dto
     */
    private static final long     serialVersionUID = 1L;
    private String                feedId;               // 动态ID
    private String                commentId;            // 评论ID
    private String                commentUserId;        // 评论人ID
    private int                   isOfficial;           // 评论人是否官方认证
    private String                commentUserName;      // 评论人名称
    private String                commentUserPic;       // 评论人头像
    private String                content;              // 评论内容
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date                  commentDate;          // 评论时间
    private List<CommentReplyDto> commentReplyDto;      // 评论回复列表
    private int                   replyNum;             // 回复数量
    private String                fmtTime;              // 格式化评论时间
    private Map<String, Object>   userTitleObj;       // 用户头衔对象，id，name，nameUrl，borderUrl

    public int getReplyNum() {
        return replyNum;
    }

    public void setReplyNum(int replyNum) {
        this.replyNum = replyNum;
    }

    public List<CommentReplyDto> getCommentReplyDto() {
        return commentReplyDto;
    }

    public void setCommentReplyDto(List<CommentReplyDto> commentReplyDto) {
        this.commentReplyDto = commentReplyDto;
    }

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public String getCommentId() {
        return commentId;
    }

    public void setCommentId(String commentId) {
        this.commentId = commentId;
    }

    public String getCommentUserId() {
        return commentUserId;
    }

    public void setCommentUserId(String commentUserId) {
        this.commentUserId = commentUserId;
    }

    public String getCommentUserName() {
        return commentUserName;
    }

    public void setCommentUserName(String commentUserName) {
        this.commentUserName = commentUserName;
    }

    public String getCommentUserPic() {
        return commentUserPic;
    }

    public void setCommentUserPic(String commentUserPic) {
        this.commentUserPic = commentUserPic;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCommentDate() {
        return commentDate;
    }

    public void setCommentDate(Date commentDate) {
        this.commentDate = commentDate;
    }

    public int getIsOfficial() { return isOfficial; }

    public void setIsOfficial(int isOfficial) { this.isOfficial = isOfficial; }

    public String getFmtTime() {
        return ToolUtils.formatDate(this.commentDate);
//        return fmtTime;
    }

    public void setFmtTime(String fmtTime) {
        this.fmtTime = fmtTime;
    }
    public Map<String, Object> getUserTitleObj() {
        return userTitleObj;
    }

    public void setUserTitleObj(Map<String, Object> userTitleObj) {
        this.userTitleObj = userTitleObj;
    }
}
