package net.xplife.system.community.dto.feed;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import com.alibaba.fastjson.annotation.JSONField;
import net.xplife.system.community.utils.ToolUtils;

public class FeedInfoDto implements Serializable {
    /**
     * 动态信息Dto
     */
    private static final long    serialVersionUID = 1L;
    private String               lastId;               // 最后一条记录ID
    private String               feedId;               // 动态ID
    private int                  codeId;               // 用户codeId
    private String               userId;               // 用户ID
    private String               userName;             // 用户名称
    private String               userPic;              // 用户头像
    private String               sex;                  // 用户性别
    private String               content;              // 内容
    private String               title;                // 标题
    private List<PicDto>         picDto;               // 大图图片
    private List<PicDto>         smallPicDto;          // 小图图片
    private Map<String, String>  labelMap;             // 标签ID和名称
    private int                  likeNum;              // 喜欢数量
    private int                  downLoadNum;          // 下载数量
    private int                  printNum;             // 打印数量
    private int                  commentNum;           // 评论数量
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date                 createTime;           // 创建时间
    private int                  isFriend;             // 是否是好友
    private String               htmlUrl;              // H5地址
    private List<FeedCommentDto> feedCommentDto;       // 评论列表
    private int                  isOfficial;           // 是否官方账号-用户 0否1是  //3.2版本后将废弃
    private String               fmtTime;               // 时间的特殊显示
    private int                  shareNum;              //分享数量
    private int                  giveLikeNum;           //点赞数量
    private int                  hadGivedLike;            // 当前用户是否已点赞
    private int                  isFollow;              // 关注状态；0:
    private String               shareUrl;             //分享的url
    private Map<String, Object>  userTitleObj;       // 用户头衔对象，id，name，nameUrl，borderUrl
    private Map<String, Object>  stickObj;           // 置顶：{999， “置顶”，”https://www.....“}；推荐：{}
    private int                  isLabelObject;         //是否已入选素材库
    private String               subject;           // 科目
    private int                  forgeDownloadNum;  // 造假的下载数据
    private int                  forgePrintNum;     // 造假的打印数据
    private int                  forgeShareNum;     // 造假的分享数据
    private int                  forgeGiveLikeNum;  // 造假的点赞数据

    public int getCodeId() {
        return codeId;
    }

    public void setCodeId(int codeId) {
        this.codeId = codeId;
    }

    public List<PicDto> getSmallPicDto() {
        return smallPicDto;
    }

    public void setSmallPicDto(List<PicDto> smallPicDto) {
        this.smallPicDto = smallPicDto;
    }

    public int getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(int commentNum) {
        this.commentNum = commentNum;
    }

    public List<FeedCommentDto> getFeedCommentDto() {
        return feedCommentDto;
    }

    public void setFeedCommentDto(List<FeedCommentDto> feedCommentDto) {
        this.feedCommentDto = feedCommentDto;
    }

    public String getHtmlUrl() {
        return htmlUrl;
    }

    public void setHtmlUrl(String htmlUrl) {
        this.htmlUrl = htmlUrl;
    }

    public String getLastId() {
        return lastId;
    }

    public void setLastId(String lastId) {
        this.lastId = lastId;
    }

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPic() {
        return userPic;
    }

    public void setUserPic(String userPic) {
        this.userPic = userPic;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<PicDto> getPicDto() {
        return picDto;
    }

    public void setPicDto(List<PicDto> picDto) {
        this.picDto = picDto;
    }

    public Map<String, String> getLabelMap() {
        return labelMap;
    }

    public void setLabelMap(Map<String, String> labelMap) {
        this.labelMap = labelMap;
    }

    public int getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(int likeNum) {
        this.likeNum = likeNum;
    }

    public int getDownLoadNum() {
        return downLoadNum;
    }

    public void setDownLoadNum(int downLoadNum) {
        this.downLoadNum = downLoadNum;
    }

    public int getPrintNum() {
        return printNum;
    }

    public void setPrintNum(int printNum) {
        this.printNum = printNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public int getIsFriend() {
        return isFriend;
    }

    public void setIsFriend(int isFriend) {
        this.isFriend = isFriend;
    }

    public int getIsOfficial() {return isOfficial; }

    public void setIsOfficial(int isOfficial) { this.isOfficial = isOfficial; }

    public String getFmtTime() {
        return ToolUtils.formatDate(this.createTime);
//        return fmtTime;
    }

    public void setFmtTime(String fmtTime) {
        this.fmtTime = fmtTime;
    }

    public int getShareNum() {
        return shareNum;
    }

    public void setShareNum(int shareNum) {
        this.shareNum = shareNum;
    }

    public int getGiveLikeNum() {
        return giveLikeNum;
    }

    public void setGiveLikeNum(int giveLikeNum) {
        this.giveLikeNum = giveLikeNum;
    }

    public int getHadGivedLike() {
        return hadGivedLike;
    }

    public void setHadGivedLike(int hadGivedLike) {
        this.hadGivedLike = hadGivedLike;
    }

    public int getIsFollow() {
        return isFollow;
    }

    public void setIsFollow(int isFollow) {
        this.isFollow = isFollow;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    public Map<String, Object> getUserTitleObj() {
        return userTitleObj;
    }

    public void setUserTitleObj(Map<String, Object> userTitleObj) {
        this.userTitleObj = userTitleObj;
    }

    public Map<String, Object> getStickObj() {
        return stickObj;
    }

    public void setStickObj(Map<String, Object> stickObj) {
        this.stickObj = stickObj;
    }

    public int getIsLabelObject() {
        return isLabelObject;
    }

    public void setIsLabelObject(int isLabelObject) {
        this.isLabelObject = isLabelObject;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public int getForgeDownloadNum() {
        return forgeDownloadNum;
    }

    public void setForgeDownloadNum(int forgeDownloadNum) {
        this.forgeDownloadNum = forgeDownloadNum;
    }

    public int getForgePrintNum() {
        return forgePrintNum;
    }

    public void setForgePrintNum(int forgePrintNum) {
        this.forgePrintNum = forgePrintNum;
    }

    public int getForgeShareNum() {
        return forgeShareNum;
    }

    public void setForgeShareNum(int forgeShareNum) {
        this.forgeShareNum = forgeShareNum;
    }

    public int getForgeGiveLikeNum() {
        return forgeGiveLikeNum;
    }

    public void setForgeGiveLikeNum(int forgeGiveLikeNum) {
        this.forgeGiveLikeNum = forgeGiveLikeNum;
    }
}
