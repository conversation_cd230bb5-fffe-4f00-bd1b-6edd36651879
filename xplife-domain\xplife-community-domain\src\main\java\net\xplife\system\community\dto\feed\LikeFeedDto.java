package net.xplife.system.community.dto.feed;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
import net.xplife.system.community.utils.ToolUtils;

public class LikeFeedDto implements Serializable {
    /**
     * 喜欢的动态Dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            feedId;               // 动态ID
    private String            pic;                  // 图片地址
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createTime;           // 创建 时间
    private String            fmtTime;     // 时间的特殊显示

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public String getFmtTime() {
        return ToolUtils.formatDate(this.createTime);
    }

    public void setFmtTime(String fmtTime) {
        this.fmtTime = fmtTime;
    }
}
