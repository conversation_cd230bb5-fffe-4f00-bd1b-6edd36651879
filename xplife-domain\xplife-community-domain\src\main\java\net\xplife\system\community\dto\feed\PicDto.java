package net.xplife.system.community.dto.feed;
import java.io.Serializable;
public class PicDto implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    private String            pic;                  // 图片地址
    private String            id;                   // 记录ID
    private int               height;               // 高
    private int               width;                // 宽
    private double            size;                 // 大小
    private String            startPoint;           // 开始点
    private String            endPoint;             // 结束点
    private int               likeNum;              // 喜欢数量
    private int               downLoadNum;          // 下载数量
    private int               printNum;             // 打印数量

    public PicDto() {
        super();
    }

    public PicDto(String pic, int height, int width, double size, String startPoint, String endPoint) {
        super();
        this.pic = pic;
        this.height = height;
        this.width = width;
        this.size = size;
        this.startPoint = startPoint;
        this.endPoint = endPoint;
    }

    public PicDto(String pic, int height, int width) {
        super();
        this.pic = pic;
        this.height = height;
        this.width = width;
    }

    public int getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(int likeNum) {
        this.likeNum = likeNum;
    }

    public int getDownLoadNum() {
        return downLoadNum;
    }

    public void setDownLoadNum(int downLoadNum) {
        this.downLoadNum = downLoadNum;
    }

    public int getPrintNum() {
        return printNum;
    }

    public void setPrintNum(int printNum) {
        this.printNum = printNum;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public double getSize() {
        return size;
    }

    public void setSize(double size) {
        this.size = size;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
