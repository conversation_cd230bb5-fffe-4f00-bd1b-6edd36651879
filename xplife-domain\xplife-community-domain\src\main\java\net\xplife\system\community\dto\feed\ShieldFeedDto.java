package net.xplife.system.community.dto.feed;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.Date;

public class ShieldFeedDto implements Serializable {
    /**
     * 屏蔽用户列表dto--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            userId;               // 用户ID
    private String            feedId;               // 屏蔽的动态id
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createTime;           // 创建时间
    private FeedInfoDto       feedInfo;          // 动态数据

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public FeedInfoDto getFeedInfo() {
        return feedInfo;
    }

    public void setFeedInfo(FeedInfoDto feedInfo) {
        this.feedInfo = feedInfo;
    }
}
