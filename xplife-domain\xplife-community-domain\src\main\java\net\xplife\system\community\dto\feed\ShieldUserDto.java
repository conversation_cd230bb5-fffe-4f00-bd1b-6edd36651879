package net.xplife.system.community.dto.feed;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import com.alibaba.fastjson.annotation.JSONField;
public class ShieldUserDto implements Serializable {
    /**
     * 屏蔽用户列表dto--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            userId;               // 用户ID
    private String            nickName;             // 用户昵称
    private String            pic;                  // 用户头像
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createTime;           // 创建时间
    private int               isOfficial;           // 0：普通用户；1：官方用户
    private Map<String, Object> userTitleObj;       // 用户头衔对象，id，name，nameUrl，borderUrl

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public int getIsOfficial() { return isOfficial; }

    public void setIsOfficial(int isOfficial) { this.isOfficial = isOfficial; }

    public Map<String, Object> getUserTitleObj() {
        return userTitleObj;
    }

    public void setUserTitleObj(Map<String, Object> userTitleObj) {
        this.userTitleObj = userTitleObj;
    }
}
