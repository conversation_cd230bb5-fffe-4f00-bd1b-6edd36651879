package net.xplife.system.community.dto.homework;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/8 17:19
 * @description：
 * @modified By：
 * @version: $
 */
public class HomeWorkDto implements Serializable {
    private static final long  serialVersionUID = 1L;
    private String id;
    private String printerSn;               //打印机唯一标识
    private String subject;                 // 作业科目
    private String subjectName;             // 作业科目名称
    private String fileName;                // 文件名
    private String fileUrl;                 // url地址
    private String userName;                // 导入人的名称（取当前用户）
    private String createtime;                // 导入时间
    private String icon;                    // 根据subject，显示具体图标
    private String fileType;                // 文档类型

    public String getPrinterSn() {
        return printerSn;
    }

    public void setPrinterSn(String printerSn) {
        this.printerSn = printerSn;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }


    public String getCreatetime() {
        return createtime;
    }

    public void setCreatetime(String createtime) {
        this.createtime = createtime;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
}
