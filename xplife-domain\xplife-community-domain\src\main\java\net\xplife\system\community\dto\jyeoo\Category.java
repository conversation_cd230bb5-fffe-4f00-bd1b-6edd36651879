package net.xplife.system.community.dto.jyeoo;
import java.io.Serializable;
import java.util.List;
import com.alibaba.fastjson.annotation.JSONField;
// 章节类
public class Category implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    @JSONField(name = "seq")
    public int                Seq;                  // 排序
    @JSONField(name = "id")
    public String             ID;                   // 章节标识
    @JSONField(name = "name")
    public String             Name;                 // 章节名称
    @JSONField(name = "desc")
    public String             Desc;                 // 教材描述
    @JSONField(name = "points")
    public List<Point>        Points;               // 章节考点
    @JSONField(name = "children")
    public List<Category>     Children;             // 子章节
    @JSONField(name = "quesCount")
    public List<DegreeCount>  QuesCount;            // 难度题量

    public int getSeq() {
        return Seq;
    }

    public void setSeq(int seq) {
        Seq = seq;
    }

    public String getID() {
        return ID;
    }

    public void setID(String iD) {
        ID = iD;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getDesc() {
        return Desc;
    }

    public void setDesc(String desc) {
        Desc = desc;
    }

    public List<Point> getPoints() {
        return Points;
    }

    public void setPoints(List<Point> points) {
        Points = points;
    }

    public List<Category> getChildren() {
        return Children;
    }

    public void setChildren(List<Category> children) {
        Children = children;
    }

    public List<DegreeCount> getQuesCount() {
        return QuesCount;
    }

    public void setQuesCount(List<DegreeCount> quesCount) {
        QuesCount = quesCount;
    }
}