package net.xplife.system.community.dto.jyeoo;
import com.alibaba.fastjson.annotation.JSONField;
public class KeyValuePair<K, V> {
    @JSONField(name = "key")
    public K Key;
    @JSONField(name = "value")
    public V Value;

    public KeyValuePair(K key, V value) {
        this.Key = key;
        this.Value = value;
    }

    public K getKey() {
        return Key;
    }

    public void setKey(K key) {
        Key = key;
    }

    public V getValue() {
        return Value;
    }

    public void setValue(V value) {
        Value = value;
    }
}
