package net.xplife.system.community.dto.jyeoo;
import java.io.Serializable;
import java.util.List;
import com.alibaba.fastjson.annotation.JSONField;
public class Ques implements Serializable {
    /**
     * 试题类
     */
    private static final long                 serialVersionUID = 1L;
    @JSONField(name = "subject")
    public byte                               Subject;              // 试题学科
    @JSONField(name = "id")
    public String                             ID;                   // 试题标识
    @JSONField(name = "content")
    public String                             Content;              // 题干
    @JSONField(name = "method")
    public String                             Method;               // 解答
    @JSONField(name = "analyse")
    public String                             Analyse;              // 分析
    @JSONField(name = "discuss")
    public String                             Discuss;              // 点评
    @JSONField(name = "degree")
    public float                              Degree;               // 难度（范围：0.1到1.0，由难到易）
    @JSONField(name = "cate")
    public byte                               Cate;                 // 题型（1：选择题；2：填空题；9：解答题等，参考获取题型接口）
    @JSONField(name = "label")
    public String                             Label;                // 试题来源（例如“2012•重庆”）
    @JSONField(name = "labelReportId")
    public String                             LabelReportID;        // 来源试卷
    @JSONField(name = "teacher")
    public String                             Teacher;              // 答题老师
    @JSONField(name = "cateName")
    public String                             CateName;             // 题型名称
    @JSONField(name = "points")
    public List<KeyValuePair<String, String>> Points;               // 考点
    @JSONField(name = "topics")
    public List<KeyValuePair<String, String>> Topics;               // 专题
    @JSONField(name = "options")
    public List<String>                       Options;              // 选项
    @JSONField(name = "favTime")
    public int                                FavTime;              // 收藏次数
    @JSONField(name = "viewCount")
    public int                                ViewCount;            // 浏览次数
    @JSONField(name = "downCount")
    public int                                DownCount;            // 下载次数
    @JSONField(name = "realCount")
    public int                                RealCount;            // 真题系数
    @JSONField(name = "paperCount")
    public int                                PaperCount;           // 组卷次数
    /// 标准答案（选择题为以0开始的选项序号，其他题型为标准答案文本；选择题以外的题型，标准答案对应于题干中的“<div class="quizPutTag">&nbsp;</div>”）
    @JSONField(name = "answers")
    public List<String>                       Answers;
    @JSONField(name = "seq")
    public int                                Seq;                  // 题号
    @JSONField(name = "score")
    public float                              Score;                // 分值
    @JSONField(name = "userAnswers")
    public List<String>                       UserAnswers;          // 学生答案
    @JSONField(name = "userScores")
    public int[]                              UserScores;           // 答案得分（0：错误；1：正确；2：未判）
    @JSONField(name = "quesFiles")
    public List<QuesFile>                     QuesFiles;            // 试题文件
    @JSONField(name = "quesChilds")
    public List<QuesChild>                    QuesChilds;           // 试题子题（只返回子母题关系）
    @JSONField(name = "parentId")
    public String                             ParentID;             // 父题标识
    @JSONField(name = "parentContent")
    public String                             ParentContent;        // 父题内容
    private String                            template;             // 模板内容
    private String                            collectId;            // 收藏记录ID
    private String                            SID;

    public String getCollectId() {
        return collectId;
    }

    public void setCollectId(String collectId) {
        this.collectId = collectId;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public byte getSubject() {
        return Subject;
    }

    public void setSubject(byte subject) {
        Subject = subject;
    }

    public String getID() {
        return ID;
    }

    public void setID(String iD) {
        ID = iD;
    }

    public String getContent() {
        return Content;
    }

    public void setContent(String content) {
        Content = content;
    }

    public String getMethod() {
        return Method;
    }

    public void setMethod(String method) {
        Method = method;
    }

    public String getAnalyse() {
        return Analyse;
    }

    public void setAnalyse(String analyse) {
        Analyse = analyse;
    }

    public String getDiscuss() {
        return Discuss;
    }

    public void setDiscuss(String discuss) {
        Discuss = discuss;
    }

    public float getDegree() {
        return Degree;
    }

    public void setDegree(float degree) {
        Degree = degree;
    }

    public byte getCate() {
        return Cate;
    }

    public void setCate(byte cate) {
        Cate = cate;
    }

    public String getLabel() {
        return Label;
    }

    public void setLabel(String label) {
        Label = label;
    }

    public String getLabelReportID() {
        return LabelReportID;
    }

    public void setLabelReportID(String labelReportID) {
        LabelReportID = labelReportID;
    }

    public String getTeacher() {
        return Teacher;
    }

    public void setTeacher(String teacher) {
        Teacher = teacher;
    }

    public String getCateName() {
        return CateName;
    }

    public void setCateName(String cateName) {
        CateName = cateName;
    }

    public List<KeyValuePair<String, String>> getPoints() {
        return Points;
    }

    public void setPoints(List<KeyValuePair<String, String>> points) {
        Points = points;
    }

    public List<KeyValuePair<String, String>> getTopics() {
        return Topics;
    }

    public void setTopics(List<KeyValuePair<String, String>> topics) {
        Topics = topics;
    }

    public List<String> getOptions() {
        return Options;
    }

    public void setOptions(List<String> options) {
        Options = options;
    }

    public int getFavTime() {
        return FavTime;
    }

    public void setFavTime(int favTime) {
        FavTime = favTime;
    }

    public int getViewCount() {
        return ViewCount;
    }

    public void setViewCount(int viewCount) {
        ViewCount = viewCount;
    }

    public int getDownCount() {
        return DownCount;
    }

    public void setDownCount(int downCount) {
        DownCount = downCount;
    }

    public int getRealCount() {
        return RealCount;
    }

    public void setRealCount(int realCount) {
        RealCount = realCount;
    }

    public int getPaperCount() {
        return PaperCount;
    }

    public void setPaperCount(int paperCount) {
        PaperCount = paperCount;
    }

    public List<String> getAnswers() {
        return Answers;
    }

    public void setAnswers(List<String> answers) {
        Answers = answers;
    }

    public int getSeq() {
        return Seq;
    }

    public void setSeq(int seq) {
        Seq = seq;
    }

    public float getScore() {
        return Score;
    }

    public void setScore(float score) {
        Score = score;
    }

    public List<String> getUserAnswers() {
        return UserAnswers;
    }

    public void setUserAnswers(List<String> userAnswers) {
        UserAnswers = userAnswers;
    }

    public int[] getUserScores() {
        return UserScores;
    }

    public void setUserScores(int[] userScores) {
        UserScores = userScores;
    }

    public List<QuesFile> getQuesFiles() {
        return QuesFiles;
    }

    public void setQuesFiles(List<QuesFile> quesFiles) {
        QuesFiles = quesFiles;
    }

    public List<QuesChild> getQuesChilds() {
        return QuesChilds;
    }

    public void setQuesChilds(List<QuesChild> quesChilds) {
        QuesChilds = quesChilds;
    }

    public String getParentID() {
        return ParentID;
    }

    public void setParentID(String parentID) {
        ParentID = parentID;
    }

    public String getParentContent() {
        return ParentContent;
    }

    public void setParentContent(String parentContent) {
        ParentContent = parentContent;
    }

    public String getSID() {
        return SID;
    }

    public void setSID(String SID) {
        this.SID = SID;
    }
}
