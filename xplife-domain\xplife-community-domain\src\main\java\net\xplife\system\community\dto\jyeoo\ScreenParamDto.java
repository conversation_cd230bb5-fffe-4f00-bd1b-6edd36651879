package net.xplife.system.community.dto.jyeoo;
import java.io.Serializable;
import java.util.List;
public class ScreenParamDto implements Serializable {
    /**
     * 筛选参数dto
     */
    private static final long                  serialVersionUID = 1L;
    private List<KeyValuePair<String, String>> cate;                 // 题型
    private List<KeyValuePair<Object, String>> quesCate;             // 题类
    private List<KeyValuePair<String, String>> degree;               // 难度
    private List<KeyValuePair<String, String>> source;               // 来源
    private List<KeyValuePair<String, String>> year;                 // 年份
    private List<AreaDto> area;                                      // 所属地区
    private List<KeyValuePair<String, String>> po;                   // 排序
    private List<KeyValuePair<String, String>> pd;                   // 升降

    public List<KeyValuePair<Object, String>> getQuesCate() {
        return quesCate;
    }

    public void setQuesCate(List<KeyValuePair<Object, String>> quesCate) {
        this.quesCate = quesCate;
    }

    public List<KeyValuePair<String, String>> getCate() {
        return cate;
    }

    public void setCate(List<KeyValuePair<String, String>> cate) {
        this.cate = cate;
    }

    public List<KeyValuePair<String, String>> getDegree() {
        return degree;
    }

    public void setDegree(List<KeyValuePair<String, String>> degree) {
        this.degree = degree;
    }

    public List<KeyValuePair<String, String>> getSource() {
        return source;
    }

    public void setSource(List<KeyValuePair<String, String>> source) {
        this.source = source;
    }

    public List<KeyValuePair<String, String>> getYear() {
        return year;
    }

    public void setYear(List<KeyValuePair<String, String>> year) {
        this.year = year;
    }

    public List<KeyValuePair<String, String>> getPo() {
        return po;
    }

    public void setPo(List<KeyValuePair<String, String>> po) {
        this.po = po;
    }

    public List<KeyValuePair<String, String>> getPd() {
        return pd;
    }

    public List<AreaDto> getArea() {
        return area;
    }

    public void setArea(List<AreaDto> area) {
        this.area = area;
    }

    public void setPd(List<KeyValuePair<String, String>> pd) {
        this.pd = pd;
    }

}
