package net.xplife.system.community.dto.jyeoo;
import java.io.Serializable;
public class SearchQuesParamDto implements Serializable {
    /**
     * 搜题查询条件
     */
    private static final long serialVersionUID = 1L;
    private String            subject;              // 学科
    private int               tp;                   // 类型 1：教材；2：考点
    private String            p1;                   // 教材标识或一级考点编号
    private String            p2;                   // 教材目录或二级考点编号
    private String            p3;                   // 考点编号 多个以逗号分隔
    private String            cate;                 // 题型，“0”不限题型，多个以逗号分隔
    private int               degree;               // 难度 0：不限；11：易（0.8到1.0）；12：较易（0.6到0.8）；13：中档（0.4到0.6）；14：较难：（0.2到0.4）；15：难：（0.0到0.2）
    private String            source;               // 来源，“0”不限来源，多个以逗号分隔
    private String            year;                 // 年份 “0”不限年份，多个以逗号分隔
    private int               po;                   // 排序（0：综合排序；1：组卷次数；2：真题次数；3：试题难度）
    private int               pd;                   // 升降（0：升序；1：降序）
    private int               pi;                   // 页码
    private boolean           sc;                   // 真题集（True：真题；False：不限）；
    private boolean           gc;                   // 好题集（True：好题；False：不限）；
    private boolean           rc;                   // 常考题（True：常考题；False：不限）；
    private boolean           yc;                   // 压轴题（True：压轴题；False：不限）；
    private boolean           ec;                   // 易错题（True：易错题；False：不限）；
    private boolean           er;                   // 用户错题（True：用户错题；False：不限）
    private String            rg;                   // 地区编码

    private String            keySearch;                    //关键词
    private int               ps;                   //每页记录数（小于等于10）

    public boolean getSc() {
        return sc;
    }

    public void setSc(boolean sc) {
        this.sc = sc;
    }

    public boolean getGc() {
        return gc;
    }

    public void setGc(boolean gc) {
        this.gc = gc;
    }

    public boolean getRc() {
        return rc;
    }

    public void setRc(boolean rc) {
        this.rc = rc;
    }

    public boolean getYc() {
        return yc;
    }

    public void setYc(boolean yc) {
        this.yc = yc;
    }

    public boolean getEc() {
        return ec;
    }

    public void setEc(boolean ec) {
        this.ec = ec;
    }

    public boolean getEr() {
        return er;
    }

    public void setEr(boolean er) {
        this.er = er;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public int getTp() {
        return tp;
    }

    public void setTp(int tp) {
        this.tp = tp;
    }

    public String getP1() {
        return p1;
    }

    public void setP1(String p1) {
        this.p1 = p1;
    }

    public String getP2() {
        return p2;
    }

    public void setP2(String p2) {
        this.p2 = p2;
    }

    public String getP3() {
        return p3;
    }

    public void setP3(String p3) {
        this.p3 = p3;
    }

    public String getCate() {
        return cate;
    }

    public void setCate(String cate) {
        this.cate = cate;
    }

    public int getDegree() {
        return degree;
    }

    public void setDegree(int degree) {
        this.degree = degree;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public int getPo() {
        return po;
    }

    public void setPo(int po) {
        this.po = po;
    }

    public int getPd() {
        return pd;
    }

    public void setPd(int pd) {
        this.pd = pd;
    }

    public int getPi() {
        return pi;
    }

    public void setPi(int pi) {
        this.pi = pi;
    }

    public int getPs() {
        return ps;
    }

    public void setPs(int ps) {
        this.ps = ps;
    }

    public String getKeySearch() {
        return keySearch;
    }

    public void setKeySearch(String keySearch) {
        this.keySearch = keySearch;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }
}
