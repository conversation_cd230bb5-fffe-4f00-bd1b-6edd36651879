package net.xplife.system.community.dto.jyeoo;
import java.io.Serializable;
public class SubjectsDto implements Serializable {
    /**
     * 学科信息
     */
    private static final long serialVersionUID = 1L;
    private String            name;                 // 名称
    private String            english;              // 英文
    private String            icon;                 // 图标
    private String            subject;              // 学科标识
    private int               sort;                 // 排序
    private int               count;                // 数量
    private String            level;                // 等级

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnglish() {
        return english;
    }

    public void setEnglish(String english) {
        this.english = english;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }
}
