package net.xplife.system.community.dto.knowledge;

import java.io.Serializable;
import java.util.List;

public class KnowledgeArticleDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private String                      id;
    private String                      title;           // 标题
    private String                      headUrl;         // 封面
    private String                      contents;        // h5内容
    private String                      subject;         // 学科
    private String                      gradeLevel;      // 年级
    private int                         shareNum;        // 解锁次数、分享次数
    private int                         printNum;        // 打印次数
    private int                         clickNum;        // 点击次数
    private List<KnowledgePicVo>        pics;            // 图片集合，供打印用
    private int                         shareFlag;       // 0:未分享；1：已分享
    private String                      shareUrl;        // 分享出去的url地址
    private int                         sortNum;         // 排序编号
    private String                      version;         // 该版本后才显示

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    public String getContents() {
        return contents;
    }

    public void setContents(String contents) {
        this.contents = contents;
    }

    public int getShareNum() {
        return shareNum;
    }

    public void setShareNum(int shareNum) {
        this.shareNum = shareNum;
    }

    public int getPrintNum() {
        return printNum;
    }

    public void setPrintNum(int printNum) {
        this.printNum = printNum;
    }

    public List<KnowledgePicVo> getPics() {
        return pics;
    }

    public void setPics(List<KnowledgePicVo> pics) {
        this.pics = pics;
    }

    public int getShareFlag() {
        return shareFlag;
    }

    public void setShareFlag(int shareFlag) {
        this.shareFlag = shareFlag;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getGradeLevel() {
        return gradeLevel;
    }

    public void setGradeLevel(String gradeLevel) {
        this.gradeLevel = gradeLevel;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    public int getClickNum() {
        return clickNum;
    }

    public void setClickNum(int clickNum) {
        this.clickNum = clickNum;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
