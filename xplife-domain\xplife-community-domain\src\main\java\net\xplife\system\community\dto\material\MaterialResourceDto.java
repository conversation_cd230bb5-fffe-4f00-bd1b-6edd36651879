package net.xplife.system.community.dto.material;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import net.xplife.system.community.dto.feed.PicDto;
public class MaterialResourceDto implements Serializable {
    /**
     * 素材库Dto
     */
    private static final long   serialVersionUID = 1L;
    private String              mId;                  // 素材库ID
    private int                 length;               // 纸长度
    private Map<String, PicDto> resMap;               // 资源集合
    private Map<String, PicDto> resMapA4;               // 资源集合A4
    private Map<String, PicDto> resMapA5;               // 资源集合A5
    private int                 type;                 //原先资源的type
    private int                 placeType;            // 0：竖向；1：横向
    private int                 isNew;                // 是否标识new
    private Date                newFlagBeforeDate;       // 如果此值有，当前时间超过此值，则不再显示isNew
    private String              name;   // 资源下面显示的名字

    public String getmId() {
        return mId;
    }

    public void setmId(String mId) {
        this.mId = mId;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public Map<String, PicDto> getResMap() {
        return resMap;
    }

    public void setResMap(Map<String, PicDto> resMap) {
        this.resMap = resMap;
    }

    public int getPlaceType() {
        return placeType;
    }

    public void setPlaceType(int placeType) {
        this.placeType = placeType;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }

    public Date getNewFlagBeforeDate() {
        return newFlagBeforeDate;
    }

    public void setNewFlagBeforeDate(Date newFlagBeforeDate) {
        this.newFlagBeforeDate = newFlagBeforeDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, PicDto> getResMapA4() {
        return resMapA4;
    }

    public void setResMapA4(Map<String, PicDto> resMapA4) {
        this.resMapA4 = resMapA4;
    }

    public Map<String, PicDto> getResMapA5() {
        return resMapA5;
    }

    public void setResMapA5(Map<String, PicDto> resMapA5) {
        this.resMapA5 = resMapA5;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
