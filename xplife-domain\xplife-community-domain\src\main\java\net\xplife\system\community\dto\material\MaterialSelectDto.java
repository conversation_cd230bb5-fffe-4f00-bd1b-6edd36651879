package net.xplife.system.community.dto.material;
import java.io.Serializable;
import java.util.List;

public class MaterialSelectDto implements Serializable {
    /**
     * 素材库Dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            name;                 // 名称
    private String title;
    private String com; // 模板组件名
    private String zhTitle;
    private String enTitle;
    private List<MaterialSelectDto> childs;
    private int               type;                 // 类型 1-编辑纸条 2-清单 3-便签 -5 A4打印
    private int               subType;              // 副类型 1-文字 2-图片 3-贴纸 4-涂鸦 5-二维码 6-纸条箱

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCom() {
        return com;
    }

    public void setCom(String com) {
        this.com = com;
    }

    public String getZhTitle() {
        return zhTitle;
    }

    public void setZhTitle(String zhTitle) {
        this.zhTitle = zhTitle;
    }

    public String getEnTitle() {
        return enTitle;
    }

    public void setEnTitle(String enTitle) {
        this.enTitle = enTitle;
    }

    public List<MaterialSelectDto> getChilds() {
        return childs;
    }

    public void setChilds(List<MaterialSelectDto> childs) {
        this.childs = childs;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }
}
