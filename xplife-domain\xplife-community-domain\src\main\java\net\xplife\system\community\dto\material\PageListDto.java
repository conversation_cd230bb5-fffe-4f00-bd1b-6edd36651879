package net.xplife.system.community.dto.material;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
public class PageListDto implements Serializable {
    /**
     * 素材库Dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            name;                 // 名称
    private String            pic;                  // 图片地址
    private int               height;               // 高
    private int               width;                // 宽
    private int               length;               // 纸长度
    @JSONField(format = "yyyy-MM-dd HH")
    private Date              createTime;           // 创建时间
    private int               isNew;                // 是否显示isNew
    private Date               newFlagBeforeDate;       // 如果此值有，当前时间超过此值，则不再显示isNew

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }

    public Date getNewFlagBeforeDate() {
        return newFlagBeforeDate;
    }

    public void setNewFlagBeforeDate(Date newFlagBeforeDate) {
        this.newFlagBeforeDate = newFlagBeforeDate;
    }
}
