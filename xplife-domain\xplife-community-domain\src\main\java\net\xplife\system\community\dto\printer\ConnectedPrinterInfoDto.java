package net.xplife.system.community.dto.printer;

import org.springframework.data.mongodb.core.index.Indexed;

/**
 * <AUTHOR>
 * @date ：Created in 2023/8/3 10:15
 * @description：记录被连接过的打印机的具体信息
 * @modified By：
 * @version: $
 */
public class ConnectedPrinterInfoDto {
    private String printerSn;           //打印机唯一标识
    private String bluetoothName;       // 蓝牙名称

    private String version;             // 蓝牙名称
    private String macAddress;          // mac地址

    private String serverIp;            // 打印机IP地址
    private String serverPort;          // 打印机端口
    private boolean isTMall;            // 是否携带天猫精灵
    private int printerState=0;           // 0：未配网；  1：在线   2：不在线

    public String getPrinterSn() {
        return printerSn;
    }

    public void setPrinterSn(String printerSn) {
        this.printerSn = printerSn;
    }

    public String getBluetoothName() {
        return bluetoothName;
    }

    public void setBluetoothName(String bluetoothName) {
        this.bluetoothName = bluetoothName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getServerIp() {
        return serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    public String getServerPort() {
        return serverPort;
    }

    public void setServerPort(String serverPort) {
        this.serverPort = serverPort;
    }

    public boolean isTMall() {
        return isTMall;
    }

    public void setTMall(boolean TMall) {
        isTMall = TMall;
    }

    public int getPrinterState() {
        return printerState;
    }

    public void setPrinterState(int printerState) {
        this.printerState = printerState;
    }
}
