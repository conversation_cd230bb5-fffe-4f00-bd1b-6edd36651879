package net.xplife.system.community.dto.printer;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/19 9:37
 * @description：发送指令打印对象
 * @modified By：
 * @version: $
 */
public class PrinterMissionDto {
    private String orderNo;     //打印任务的id
    private String fileName;    //文档名称
    private String fileType;    //打印文档
    private Date createTime;    //任务创建时间
    private String printerSn;   //打印机sn
    private String iconUrl;     //文件图案url地址
    private String userName;    // 用户名

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getPrinterSn() {
        return printerSn;
    }

    public void setPrinterSn(String printerSn) {
        this.printerSn = printerSn;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
