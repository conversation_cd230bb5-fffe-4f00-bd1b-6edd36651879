package net.xplife.system.community.dto.printer;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/15 14:20
 * @description： 用户与打印机关联关系表
 * @modified By：
 * @version: $
 */
public class UserPrinterInfoDto implements Serializable {
    private static final long  serialVersionUID = 1L;
    private String id;
    private String printerSn;               //打印机唯一标识
    private String userId;               // 创建者
    private String printerName;         // 打印机别名
    private String macAddress;          // mac地址
    private String type;                // 打印机类型
    private String bluetoothName;       // 蓝牙名称
    private boolean managerFlag;        // 是否管理者身份
    private boolean demoFlag;           // 是否体验版
    private long updatetime;            // 更新时间，排序用
    private String icon;                // 打印机icon的url地址

    private String version;             //  固件版本号
    private Integer offTime;             // 自动关机时间

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPrinterSn() {
        return printerSn;
    }

    public void setPrinterSn(String printerSn) {
        this.printerSn = printerSn;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPrinterName() {
        return printerName;
    }

    public void setPrinterName(String printerName) {
        this.printerName = printerName;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBluetoothName() {
        return bluetoothName;
    }

    public void setBluetoothName(String bluetoothName) {
        this.bluetoothName = bluetoothName;
    }

    public boolean isManagerFlag() {
        return managerFlag;
    }

    public void setManagerFlag(boolean managerFlag) {
        this.managerFlag = managerFlag;
    }

    public long getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(long updatetime) {
        this.updatetime = updatetime;
    }

    public boolean isDemoFlag() {
        return demoFlag;
    }

    public void setDemoFlag(boolean demoFlag) {
        this.demoFlag = demoFlag;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getOffTime() {
        return offTime;
    }

    public void setOffTime(Integer offTime) {
        this.offTime = offTime;
    }
}
