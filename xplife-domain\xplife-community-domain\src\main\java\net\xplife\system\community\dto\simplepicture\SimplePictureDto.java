package net.xplife.system.community.dto.simplepicture;

import net.xplife.system.community.entity.simplepicture.SimplePicVo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/7 10:38
 * @description：
 * @modified By：
 * @version: $
 */
public class SimplePictureDto implements Serializable {
    private static final long serialVersionUID = 1L;

//    private String              id;                           // 表主键
    private String              name;                         // 标签名称
    private String              remark;                       // 标签描述
    private String              type;                         // 类型
    private SimplePicVo         picVo;                        // 所含图片的地址信息
    private int                 sort;                         // 排序
    private String              sizeType;                     // 2寸：a2或者A4:a4

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public SimplePicVo getPicVo() {
        return picVo;
    }

    public void setPicVo(SimplePicVo picVo) {
        this.picVo = picVo;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getSizeType() {
        return sizeType;
    }

    public void setSizeType(String sizeType) {
        this.sizeType = sizeType;
    }
}
