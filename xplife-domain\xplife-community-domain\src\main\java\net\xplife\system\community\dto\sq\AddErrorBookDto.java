package net.xplife.system.community.dto.sq;
import java.io.Serializable;
import java.util.List;
public class AddErrorBookDto implements Serializable {
    /**
     * 添加错题本dto
     */
    private static final long     serialVersionUID = 1L;
    private String                id;                   // 记录ID
    private String                type;                 // 类型
    private String                imgUrl;               // 图片地址
    private List<AftQuestionsDto> list;                 // 错题信息
    private String                replaceStr;           // 替换字符串
    private String                searchSource;               // 查询来源

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public List<AftQuestionsDto> getList() {
        return list;
    }

    public void setList(List<AftQuestionsDto> list) {
        this.list = list;
    }

    public String getReplaceStr() {
        return replaceStr;
    }

    public void setReplaceStr(String replaceStr) {
        this.replaceStr = replaceStr;
    }

    public String getSearchSource() {
        return searchSource;
    }

    public void setSearchSource(String searchSource) {
        this.searchSource = searchSource;
    }
}
