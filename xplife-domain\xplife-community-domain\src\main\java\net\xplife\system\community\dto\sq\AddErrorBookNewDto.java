package net.xplife.system.community.dto.sq;
import java.io.Serializable;
import java.util.List;
public class AddErrorBookNewDto implements Serializable {
    /**
     * 错题本dto
     */
    private static final long     serialVersionUID = 1L;
    private String                id;                   // 记录ID
    private String                userId;               // 用户ID
    private String                errorSubject;         // 错题科目
    private String                errorType;            // 错题类型
    private String                errorDiffic;          // 错题难度
    private String                errorReason;          // 错题原因
    private String                errorDegree;          // 掌握程度
    private String                errorSource;          // 错题来源
    private String                customLabel;          // 自定义标签
    private String                imgUrl;               // 图片地址
    private String                searchSource;         // 拍题搜题来源
    private List<AftQuestionsDto> list;                 // 错题信息

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public List<AftQuestionsDto> getList() {
        return list;
    }

    public void setList(List<AftQuestionsDto> list) {
        this.list = list;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getErrorSubject() {
        return errorSubject;
    }

    public void setErrorSubject(String errorSubject) {
        this.errorSubject = errorSubject;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public String getErrorDiffic() {
        return errorDiffic;
    }

    public void setErrorDiffic(String errorDiffic) {
        this.errorDiffic = errorDiffic;
    }

    public String getErrorReason() {
        return errorReason;
    }

    public void setErrorReason(String errorReason) {
        this.errorReason = errorReason;
    }

    public String getErrorDegree() {
        return errorDegree;
    }

    public void setErrorDegree(String errorDegree) {
        this.errorDegree = errorDegree;
    }

    public String getErrorSource() {
        return errorSource;
    }

    public void setErrorSource(String errorSource) {
        this.errorSource = errorSource;
    }

    public String getCustomLabel() {
        return customLabel;
    }

    public void setCustomLabel(String customLabel) {
        this.customLabel = customLabel;
    }

    public String getSearchSource() {
        return searchSource;
    }

    public void setSearchSource(String searchSource) {
        this.searchSource = searchSource;
    }
}
