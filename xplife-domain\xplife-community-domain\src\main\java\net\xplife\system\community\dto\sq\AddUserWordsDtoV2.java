package net.xplife.system.community.dto.sq;
import lombok.Getter;
import lombok.Setter;
import net.xplife.system.community.vo.sq.ExampleVo;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class AddUserWordsDtoV2 implements Serializable {
    /**
     * 新增生词本dto
     */
    private static final long serialVersionUID = 1L;
    private String            name;                 // 名称
    private String phonetic;               // 音标
    private List<String> definitions;                // 释义
    private String pronunciation;        // 发音
    private List<ExampleVo> examples;   // 例句
}
