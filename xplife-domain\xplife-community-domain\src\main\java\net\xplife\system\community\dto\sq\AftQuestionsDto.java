package net.xplife.system.community.dto.sq;
import java.io.Serializable;
import java.util.List;
public class AftQuestionsDto implements Serializable {
    /**
     * 阿凡题答案dto
     */
    private static final long   serialVersionUID = 1L;
    private String              html;                 // html信息
    private List<AftContentDto> aftContentDto;        // 阿凡题内容集合
    private String              subject;              // 类目
    private String              abstracts;            // 摘要
    private String              knowledge;            // 知识点
    private String              sid;                  // 箐优网返回的题干的id
    private boolean             hadGotAnswer;         // 是否已获取答案

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getAbstracts() {
        return abstracts;
    }

    public void setAbstracts(String abstracts) {
        this.abstracts = abstracts;
    }

    public String getKnowledge() {
        return knowledge;
    }

    public void setKnowledge(String knowledge) {
        this.knowledge = knowledge;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public List<AftContentDto> getAftContentDto() {
        return aftContentDto;
    }

    public void setAftContentDto(List<AftContentDto> aftContentDto) {
        this.aftContentDto = aftContentDto;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public boolean isHadGotAnswer() {
        if (aftContentDto!=null && aftContentDto.size()>1){
            return true;
        } else {
            return false;
        }
    }

    public void setHadGotAnswer(boolean hadGotAnswer) {
        this.hadGotAnswer = hadGotAnswer;
    }
}
