package net.xplife.system.community.dto.sq;
import java.io.Serializable;
public class CollectLabelDto implements Serializable {
    /**
     * 收藏标签dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // ID
    private String            name;                 // 名称
    private String            icon;                 // 图标
    private int               count;                // 数量

    public CollectLabelDto() {
        super();
    }

    public CollectLabelDto(String id, String name, String icon, int count) {
        super();
        this.id = id;
        this.name = name;
        this.icon = icon;
        this.count = count;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
