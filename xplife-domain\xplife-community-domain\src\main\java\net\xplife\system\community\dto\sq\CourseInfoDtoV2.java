package net.xplife.system.community.dto.sq;
import java.io.Serializable;
import java.util.List;

public class CourseInfoDtoV2 implements Serializable {
    /**
     * 课程信息dto
     */
    private static final long   serialVersionUID = 1L;
    private String              courseId;             // 课程ID
    private String              typeName;             // 类别名称
    private String              name;                 // 课程名称
    private String              pic;                  // 图片地址
    private int                 wordCount;            // 单词数
    private int                 type;                 // 词库类型 0不显示课程 1显示课程 2敬请期待
    private List<WordInfoDtoV2>   wordInfoList;         // 单词信息列表
    private List<CourseInfoDtoV2> courseInfoList;       // 课程信息列表
    private String              currCourseId;         // 当前学习课程ID
    private int                 courseStatus;         // 课程进度 0进行中 1已完成
    private int                 currCount;            // 已学单词数
    private int                 planCount;            // 计划单词数
    private int                 sort;                 // 排序

    public int getCourseStatus() {
        return courseStatus;
    }

    public void setCourseStatus(int courseStatus) {
        this.courseStatus = courseStatus;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCurrCourseId() {
        return currCourseId;
    }

    public void setCurrCourseId(String currCourseId) {
        this.currCourseId = currCourseId;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public int getWordCount() {
        return wordCount;
    }

    public void setWordCount(int wordCount) {
        this.wordCount = wordCount;
    }

    public List<WordInfoDtoV2> getWordInfoList() {
        return wordInfoList;
    }

    public void setWordInfoList(List<WordInfoDtoV2> wordInfoList) {
        this.wordInfoList = wordInfoList;
    }

    public List<CourseInfoDtoV2> getCourseInfoList() {
        return courseInfoList;
    }

    public void setCourseInfoList(List<CourseInfoDtoV2> courseInfoList) {
        this.courseInfoList = courseInfoList;
    }

    public int getCurrCount() {
        return currCount;
    }

    public void setCurrCount(int currCount) {
        this.currCount = currCount;
    }

    public int getPlanCount() {
        return planCount;
    }

    public void setPlanCount(int planCount) {
        this.planCount = planCount;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }
}
