package net.xplife.system.community.dto.sq;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
public class ErrorBookDto implements Serializable {
    /**
     * 错题本dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            url;                  // 问题图片资源
    private String            type;                 // 类目类型
    private String            abstracts;            // 摘要
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createTime;           // 创建时间
    private String            searchSource;         // 拍搜来源

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAbstracts() {
        return abstracts;
    }

    public void setAbstracts(String abstracts) {
        this.abstracts = abstracts;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSearchSource() {
        return searchSource;
    }

    public void setSearchSource(String searchSource) {
        this.searchSource = searchSource;
    }
}
