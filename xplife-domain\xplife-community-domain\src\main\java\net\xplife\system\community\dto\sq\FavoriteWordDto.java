package net.xplife.system.community.dto.sq;

import net.xplife.system.community.vo.sq.ExampleVo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 收藏单词DTO
 */
@Getter
@Setter
public class FavoriteWordDto implements Serializable {
  private static final long serialVersionUID = 1L;

  private String id; // 单词ID
  private String bookId; // 单词本ID
  private String name; // 单词内容
  private String phonetic; // 音标
  private List<String> definitions; // 释义
  private String pronunciation; // 发音
  private List<ExampleVo> examples; // 例句
  private Date createTime; // 收藏时间
}