package net.xplife.system.community.dto.sq;
import java.io.Serializable;
import java.util.List;

public class PunchClockInfoDtoV2 implements Serializable {
    /**
     * 单卡详情信息dto
     */
    private static final long serialVersionUID = 1L;
    private int               day;                  // 天数
    private int               readStatus;           // 读书状态 2正在读 1已读 0未读
    private List<WordInfoDtoV2> wordInfoList;         // 单词信息dto

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public int getReadStatus() {
        return readStatus;
    }

    public void setReadStatus(int readStatus) {
        this.readStatus = readStatus;
    }

    public List<WordInfoDtoV2> getWordInfoList() {
        return wordInfoList;
    }

    public void setWordInfoList(List<WordInfoDtoV2> wordInfoList) {
        this.wordInfoList = wordInfoList;
    }
}
