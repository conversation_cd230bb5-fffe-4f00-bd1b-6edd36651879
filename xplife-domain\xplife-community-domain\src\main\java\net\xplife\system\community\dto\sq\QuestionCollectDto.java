package net.xplife.system.community.dto.sq;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import com.alibaba.fastjson.annotation.JSONField;
import net.xplife.system.community.dto.label.LabelDto;
public class QuestionCollectDto implements Serializable {
    /**
     * 错题收藏dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            url;                  // 问题图片资源
    private String            labelName;            // 标签科目名称
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createTime;           // 创建时间
    private List<LabelDto>    labels;               // 标签集合
    private List<String>      analysis;             // 解析数组
    private String            fmtTime;              // 时间格式化

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getLabelName() {
        return labelName;
    }

    public void setLabelName(String labelName) {
        this.labelName = labelName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public List<LabelDto> getLabels() {
        return labels;
    }

    public void setLabels(List<LabelDto> labels) {
        this.labels = labels;
    }

    public List<String> getAnalysis() {
        return analysis;
    }

    public void setAnalysis(List<String> analysis) {
        this.analysis = analysis;
    }

    public String getFmtTime() {
        return fmtTime;
    }

    public void setFmtTime(String fmtTime) {
        this.fmtTime = fmtTime;
    }
}
