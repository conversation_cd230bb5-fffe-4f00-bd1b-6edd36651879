package net.xplife.system.community.dto.sq;
import java.io.Serializable;
import java.util.List;
public class SearchInfoDto implements Serializable {
    /**
     * 错题本筛选信息dto
     */
    private static final long    serialVersionUID = 1L;
    private List<SearchLabelDto> errorDate;            // 错题日期
    private List<SearchLabelDto> errorSubject;         // 错题科目
    private List<SearchLabelDto> errorType;            // 错题类型
    private List<SearchLabelDto> errorDiffic;          // 错题难度
    private List<SearchLabelDto> errorReason;          // 错题原因
    private List<SearchLabelDto> errorDegree;          // 掌握程度
    private List<SearchLabelDto> errorSource;          // 错题来源
    private List<SearchLabelDto> customLabel;          // 自定义标签

    public List<SearchLabelDto> getErrorDate() {
        return errorDate;
    }

    public void setErrorDate(List<SearchLabelDto> errorDate) {
        this.errorDate = errorDate;
    }

    public List<SearchLabelDto> getErrorSubject() {
        return errorSubject;
    }

    public void setErrorSubject(List<SearchLabelDto> errorSubject) {
        this.errorSubject = errorSubject;
    }

    public List<SearchLabelDto> getErrorType() {
        return errorType;
    }

    public void setErrorType(List<SearchLabelDto> errorType) {
        this.errorType = errorType;
    }

    public List<SearchLabelDto> getErrorDiffic() {
        return errorDiffic;
    }

    public void setErrorDiffic(List<SearchLabelDto> errorDiffic) {
        this.errorDiffic = errorDiffic;
    }

    public List<SearchLabelDto> getErrorReason() {
        return errorReason;
    }

    public void setErrorReason(List<SearchLabelDto> errorReason) {
        this.errorReason = errorReason;
    }

    public List<SearchLabelDto> getErrorDegree() {
        return errorDegree;
    }

    public void setErrorDegree(List<SearchLabelDto> errorDegree) {
        this.errorDegree = errorDegree;
    }

    public List<SearchLabelDto> getErrorSource() {
        return errorSource;
    }

    public void setErrorSource(List<SearchLabelDto> errorSource) {
        this.errorSource = errorSource;
    }

    public List<SearchLabelDto> getCustomLabel() {
        return customLabel;
    }

    public void setCustomLabel(List<SearchLabelDto> customLabel) {
        this.customLabel = customLabel;
    }
}
