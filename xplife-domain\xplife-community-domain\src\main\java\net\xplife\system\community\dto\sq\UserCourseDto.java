package net.xplife.system.community.dto.sq;
import java.io.Serializable;
public class UserCourseDto implements Serializable {
    /**
     * 用户课程dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            courseId;             // 课程ID
    private String            name;                 // 课程名称
    private String            pic;                  // 图片地址
    private int               planCount;            // 课程计划数量
    private int               currCount;            // 当前已背单词数
    private int               totalCount;           // 课程单词总数

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public int getPlanCount() {
        return planCount;
    }

    public void setPlanCount(int planCount) {
        this.planCount = planCount;
    }

    public int getCurrCount() {
        return currCount;
    }

    public void setCurrCount(int currCount) {
        this.currCount = currCount;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }
}
