package net.xplife.system.community.dto.sq;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

public class UserWordsDto implements Serializable {
    /**
     * 用户生词本dto
     */
    private static final long serialVersionUID = 1L;
    private String id;                   // 记录ID
    private String name;                 // 名称
    private SymbolDto symbol;               // 音标
    private List<PartsDto> parts;                // 释义
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;           // 评论时间

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public SymbolDto getSymbol() {
        return symbol;
    }

    public void setSymbol(SymbolDto symbol) {
        this.symbol = symbol;
    }

    public List<PartsDto> getParts() {
        return parts;
    }

    public void setParts(List<PartsDto> parts) {
        this.parts = parts;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}
