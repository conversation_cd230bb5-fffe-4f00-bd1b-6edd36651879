package net.xplife.system.community.dto.sq;

import com.alibaba.fastjson.annotation.JSONField;
import net.xplife.system.community.vo.sq.ExampleVo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class UserWordsDtoV2 implements Serializable {
    /**
     * 用户生词本dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            name;                 // 名称
    private String phonetic;               // 音标
    private List<String> definitions;                // 释义
    private String pronunciation;        // 发音
    private List<ExampleVo> examples;   // 例句
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createDate;           // 评论时间

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhonetic() {
        return phonetic;
    }

    public void setPhonetic(String phonetic) {
        this.phonetic = phonetic;
    }

    public List<String> getDefinitions() {
        return definitions;
    }

    public void setDefinitions(List<String> definitions) {
        this.definitions = definitions;
    }

    public String getPronunciation() {
        return pronunciation;
    }

    public void setPronunciation(String pronunciation) {
        this.pronunciation = pronunciation;
    }

    public List<ExampleVo> getExamples() {
        return examples;
    }
    public void setExamples(List<ExampleVo> examples) {
        this.examples = examples;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}
