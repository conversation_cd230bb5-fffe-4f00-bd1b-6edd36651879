package net.xplife.system.community.dto.sq;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
public class WordCourseDto implements Serializable {
    /**
     * 词库本dto
     */
    private static final long                serialVersionUID = 1L;
    private List<WordTypeDto>                typeList;             // 词库类别列表
    private Map<String, List<CourseInfoDto>> wordCourses;          // 课程信息集合
    private String                           currCourseId;         // 当前学习课程ID
    private int                              courseStatus;         // 课程进度 0进行中 1已完成

    public List<WordTypeDto> getTypeList() {
        return typeList;
    }

    public void setTypeList(List<WordTypeDto> typeList) {
        this.typeList = typeList;
    }

    public Map<String, List<CourseInfoDto>> getWordCourses() {
        return wordCourses;
    }

    public void setWordCourses(Map<String, List<CourseInfoDto>> wordCourses) {
        this.wordCourses = wordCourses;
    }

    public String getCurrCourseId() {
        return currCourseId;
    }

    public void setCurrCourseId(String currCourseId) {
        this.currCourseId = currCourseId;
    }

    public int getCourseStatus() {
        return courseStatus;
    }

    public void setCourseStatus(int courseStatus) {
        this.courseStatus = courseStatus;
    }
}
