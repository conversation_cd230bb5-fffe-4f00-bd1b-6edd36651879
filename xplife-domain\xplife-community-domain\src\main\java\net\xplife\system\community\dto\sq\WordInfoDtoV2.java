package net.xplife.system.community.dto.sq;

import net.xplife.system.community.vo.sq.ExampleVo;

import java.io.Serializable;
import java.util.List;

public class WordInfoDtoV2 implements Serializable {
    /**
     * 单词信息dto
     */
    private static final long serialVersionUID = 1L;
    private String id; // 单词ID
    private String name; // 名称
    private String phonetic; // 音标
    private List<String> definitions; // 释义
    private String pronunciation; // 发音
    private List<ExampleVo> examples; // 例句
    private boolean isFavorite; // 是否被收藏

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhonetic() {
        return phonetic;
    }

    public void setPhonetic(String phonetic) {
        this.phonetic = phonetic;
    }

    public List<String> getDefinitions() {
        return definitions;
    }

    public void setDefinitions(List<String> definitions) {
        this.definitions = definitions;
    }

    public String getPronunciation() {
        return pronunciation;
    }

    public void setPronunciation(String pronunciation) {
        this.pronunciation = pronunciation;
    }

    public List<ExampleVo> getExamples() {
        return examples;
    }

    public void setExamples(List<ExampleVo> examples) {
        this.examples = examples;
    }

    public boolean isFavorite() {
        return isFavorite;
    }

    public void setFavorite(boolean favorite) {
        isFavorite = favorite;
    }
}
