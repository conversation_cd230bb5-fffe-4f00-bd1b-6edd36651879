package net.xplife.system.community.dto.sq;
import java.io.Serializable;
import java.util.List;
public class WordTypeDto implements Serializable {
    /**
     * 词类别
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            name;                 // 类别名称
    private int               type;                 // 词库类型 0不显示课程 1显示课程 2敬请期待
    private int               sort;                 // 排序
    private List<WordTypeDto> subTypeList;          // 子类别列表

    public WordTypeDto() {
        super();
    }

    public WordTypeDto(String id, String name, int type) {
        super();
        this.id = id;
        this.name = name;
        this.type = type;
    }

    public WordTypeDto(String id, String name, int type, int sort) {
        super();
        this.id = id;
        this.name = name;
        this.type = type;
        this.sort = sort;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public List<WordTypeDto> getSubTypeList() {
        return subTypeList;
    }

    public void setSubTypeList(List<WordTypeDto> subTypeList) {
        this.subTypeList = subTypeList;
    }
}
