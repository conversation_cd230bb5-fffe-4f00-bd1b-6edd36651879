package net.xplife.system.community.dto.studynote;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2024/12/27 08:57
 * @description：启蒙训练
 * @modified By：
 * @version: $
 */
public class InitialTrainingCollectDto {
    private String id;
    private String initialTrainingId;
    private String userId;
    private String type;
    private String resUrl;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInitialTrainingId() {
        return initialTrainingId;
    }

    public void setInitialTrainingId(String initialTrainingId) {
        this.initialTrainingId = initialTrainingId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getResUrl() {
        return resUrl;
    }

    public void setResUrl(String resUrl) {
        this.resUrl = resUrl;
    }
}
