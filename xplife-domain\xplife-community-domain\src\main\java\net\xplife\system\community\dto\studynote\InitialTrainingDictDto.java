package net.xplife.system.community.dto.studynote;

/**
 * <AUTHOR>
 * @date ：Created in 2024/12/27 08:57
 * @description：启蒙训练
 * @modified By：
 * @version: $
 */
public class InitialTrainingDictDto {
    private String  id;
    private String  name;           // 名称
    private String  type;           // 类型
    private String  label;          // 标签
    private String  value;          // 值
    private String  localeCode;     // i18n的code值
    private String  icon;           // 图标地址
    private int  sortNum;        // 排序
    private String  remark;     //备注
    private int forbid;   // 是否禁止：0：否； 1：是

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLocaleCode() {
        return localeCode;
    }

    public void setLocaleCode(String localeCode) {
        this.localeCode = localeCode;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getForbid() {
        return forbid;
    }

    public void setForbid(int forbid) {
        this.forbid = forbid;
    }
}
