package net.xplife.system.community.dto.system;
import java.io.Serializable;
import java.util.List;
public class ActivityDto implements Serializable {
    /**
     * 活动弹窗dto
     */
    private static final long serialVersionUID = 1L;
    private List<String>      pics;                 // 图片集合
    private String            name;                 // 名称
    private String            jumpVal;              // 消息跳转值
    private int               jumpType;             // 跳转类型
    private String            startTime;             // 显示开始时间: yyyy-MM-dd HH:mm:ss
    private String            endTime;               // 显示结束时间: yyyy-MM-dd HH:mm:ss
    private String            version;              //显示开始版本
    private String            paramAndroid;                  // 自定义组装的json格式，用于andriod前端调用
    private String            paramIos;                      // 自定义组装的json格式，用于ios前端调用

    public List<String> getPics() {
        return pics;
    }

    public void setPics(List<String> pics) {
        this.pics = pics;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getJumpVal() {
        return jumpVal;
    }

    public void setJumpVal(String jumpVal) {
        this.jumpVal = jumpVal;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getParamAndroid() {
        return paramAndroid;
    }

    public void setParamAndroid(String paramAndroid) {
        this.paramAndroid = paramAndroid;
    }

    public String getParamIos() {
        return paramIos;
    }

    public void setParamIos(String paramIos) {
        this.paramIos = paramIos;
    }
}
