package net.xplife.system.community.dto.system;

import java.io.Serializable;

public class DanglingDto implements Serializable {
    /**
     * 获取悬浮配置
     */
    private static final long serialVersionUID = 1L;

    private String            icon;                 // 图片
    private String            url;                  // 跳转地址

    private String            version;              // 显示开始版本
    private String            paramAndroid;                  // 自定义组装的json格式，用于andriod前端调用
    private String            paramIos;                      // 自定义组装的json格式，用于ios前端调用
    private int               jumpType;             // 跳转类型
    private String            startTime;            // 开启时间
    private String            endTime;              // 结束时间

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getParamAndroid() {
        return paramAndroid;
    }

    public void setParamAndroid(String paramAndroid) {
        this.paramAndroid = paramAndroid;
    }

    public String getParamIos() {
        return paramIos;
    }

    public void setParamIos(String paramIos) {
        this.paramIos = paramIos;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
