package net.xplife.system.community.dto.system;
import java.io.Serializable;
public class IndexPicDto implements Serializable {
    /**
     * 系统开屏页信息
     */
    private static final long serialVersionUID = 1L;
    private String            title;                // 标题
    private int               type;                 // 类型
    private String            value;                // 值
    private String            loadingPic;           // 加载页大图
    private String            loadingMinPic;        // 加载页小图
    private String            version;              // 显示开始版本
    private String            paramAndroid;         // 自定义组装的json格式，用于andriod前端调用
    private String            paramIos;             // 自定义组装的json格式，用于ios前端调用
    private String            startTime;            // 开启时间
    private String            endTime;              // 结束时间

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLoadingPic() {
        return loadingPic;
    }

    public void setLoadingPic(String loadingPic) {
        this.loadingPic = loadingPic;
    }

    public String getLoadingMinPic() {
        return loadingMinPic;
    }

    public void setLoadingMinPic(String loadingMinPic) {
        this.loadingMinPic = loadingMinPic;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getParamAndroid() {
        return paramAndroid;
    }

    public void setParamAndroid(String paramAndroid) {
        this.paramAndroid = paramAndroid;
    }

    public String getParamIos() {
        return paramIos;
    }

    public void setParamIos(String paramIos) {
        this.paramIos = paramIos;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
