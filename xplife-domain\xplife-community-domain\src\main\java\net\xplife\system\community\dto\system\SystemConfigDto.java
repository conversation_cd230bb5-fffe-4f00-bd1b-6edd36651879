package net.xplife.system.community.dto.system;
import java.io.Serializable;
public class SystemConfigDto implements Serializable {
    /**
     * 系统配置信息Dto
     */
    private static final long serialVersionUID = 1L;
    private IndexPicDto       indexPicDto;          // 开屏页信息
    private PaperDto          paperDto;             // 纸张dto
    private ActivityDto       activityDto;          // 活动弹窗dto
    private LatestVerDto      latestVerDto;         // APP最新版本信息
    private DanglingDto       danglingDto;          // 悬浮信息

    public PaperDto getPaperDto() {
        return paperDto;
    }

    public void setPaperDto(PaperDto paperDto) {
        this.paperDto = paperDto;
    }

    public IndexPicDto getIndexPicDto() {
        return indexPicDto;
    }

    public void setIndexPicDto(IndexPicDto indexPicDto) {
        this.indexPicDto = indexPicDto;
    }

    public ActivityDto getActivityDto() {
        return activityDto;
    }

    public void setActivityDto(ActivityDto activityDto) {
        this.activityDto = activityDto;
    }

    public LatestVerDto getLatestVerDto() {
        return latestVerDto;
    }

    public void setLatestVerDto(LatestVerDto latestVerDto) {
        this.latestVerDto = latestVerDto;
    }

    public DanglingDto getDanglingDto() {
        return danglingDto;
    }

    public void setDanglingDto(DanglingDto danglingDto) {
        this.danglingDto = danglingDto;
    }
}
