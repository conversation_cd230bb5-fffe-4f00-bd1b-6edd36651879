package net.xplife.system.community.entity.activity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 翻牌游戏
 */
@Document(collection = "V1_PokerCardGame")
public class PokerCardGame extends IdEntity {

    private static final long serialVersionUID = 1L;
    public static final String COLL = "V1_PokerCardGame";

    public static final String USER_ID_FIELD = "userId";

    /**
     * 用户ID
     */
    @Indexed(name = "_userId_")
    private String userId;

    /**
     * 基本次数 (每天享有 6 次)
     */
    private int everyDayTimes;

    /**
     * 好友助力次数 (好友助力1 次)
     */
    private int friendPlayTimes;

    /**
     * 好友帮玩次数 (好友帮玩2 次)
     */
    private int friendHelpTimes;

    /**
     * 可用翻牌次数
     */
    private int canPlayTimes;

    /**
     * 集齐套数
     */
    private int sets;

    /**
     * 祖国生日快乐
     * 已拥有卡片张数，格式: 0,0,0,0,0,0
     */
    private String cards;

    /**
     * 总的抽中卡数量
     */
    private int cardCount;

    /**
     * 排名
     */
    private int ranking;

    /**
     * 已获得的抽卡次数
     */
    private int drawTimes;

    /**
     * 成功邀请好友人数
     */
    private int inviteTimes;

    /**
     * 分配卡的日期
     */
    private String assignCardDate;

    /**
     * 开始玩的时间
     */
    private long playStartTime;

    /**
     * 最近翻卡时间
     */
    private long lastPlayTime;

    /**
     * 助力好友列表
     */
    private List<String> friendIds;

    // （9月30-10月5打开App天数和次数）

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getEveryDayTimes() {
        return everyDayTimes;
    }

    public void setEveryDayTimes(int everyDayTimes) {
        this.everyDayTimes = everyDayTimes;
    }

    public int getFriendPlayTimes() {
        return friendPlayTimes;
    }

    public void setFriendPlayTimes(int friendPlayTimes) {
        this.friendPlayTimes = friendPlayTimes;
    }

    public int getFriendHelpTimes() {
        return friendHelpTimes;
    }

    public void setFriendHelpTimes(int friendHelpTimes) {
        this.friendHelpTimes = friendHelpTimes;
    }

    public int getCanPlayTimes() {
        return canPlayTimes;
    }

    public void setCanPlayTimes(int canPlayTimes) {
        this.canPlayTimes = canPlayTimes;
    }

    public int getSets() {
        return sets;
    }

    public void setSets(int sets) {
        this.sets = sets;
    }

    public String getCards() {
        return cards;
    }

    public void setCards(String cards) {
        this.cards = cards;
    }

    public int getCardCount() {
        return cardCount;
    }

    public void setCardCount(int cardCount) {
        this.cardCount = cardCount;
    }

    public int getRanking() {
        return ranking;
    }

    public void setRanking(int ranking) {
        this.ranking = ranking;
    }

    public int getDrawTimes() {
        return drawTimes;
    }

    public void setDrawTimes(int drawTimes) {
        this.drawTimes = drawTimes;
    }

    public int getInviteTimes() {
        return inviteTimes;
    }

    public void setInviteTimes(int inviteTimes) {
        this.inviteTimes = inviteTimes;
    }

    public String getAssignCardDate() {
        return assignCardDate;
    }

    public void setAssignCardDate(String assignCardDate) {
        this.assignCardDate = assignCardDate;
    }

    public long getPlayStartTime() {
        return playStartTime;
    }

    public void setPlayStartTime(long playStartTime) {
        this.playStartTime = playStartTime;
    }

    public long getLastPlayTime() {
        return lastPlayTime;
    }

    public void setLastPlayTime(long lastPlayTime) {
        this.lastPlayTime = lastPlayTime;
    }

    public List<String> getFriendIds() {
        return friendIds;
    }

    public void setFriendIds(List<String> friendIds) {
        this.friendIds = friendIds;
    }

}
