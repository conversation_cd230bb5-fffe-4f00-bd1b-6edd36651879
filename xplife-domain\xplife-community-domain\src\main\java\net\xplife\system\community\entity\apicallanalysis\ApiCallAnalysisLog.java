package net.xplife.system.community.entity.apicallanalysis;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2021/2/20 14:28
 * @description：
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_ApiCallAnalysisLog")
public class ApiCallAnalysisLog extends IdEntity {
    @Indexed(name = "_userId_")
    private String             userId;                              // 用户调用
    private Date callTime;                                          // 调用时间
    @Indexed(name = "_businessType_")
    private String             businessType;                        // 接口类型

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getCallTime() {
        return callTime;
    }

    public void setCallTime(Date callTime) {
        this.callTime = callTime;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
}
