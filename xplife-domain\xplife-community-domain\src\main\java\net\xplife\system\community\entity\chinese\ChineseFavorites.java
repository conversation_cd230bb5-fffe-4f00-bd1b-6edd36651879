package net.xplife.system.community.entity.chinese;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 学汉语收藏夹
 */
@Document(collection = "V1_ChineseFavorites")
public class ChineseFavorites extends IdEntity {

    private static final long serialVersionUID = 1L;
    public static final String COLL = "V1_ChineseFavorites";
    public static final String USER_ID_FIELD = "userId";
    public static final String LETTER_FIELD  = "letter";
    public static final String CREATE_TIME_FIELD  = "createtime";
    public static final String KEY_FIELD = "key";

    /**
     * 用户ID
     */
    @Indexed(name = "_userId_")
    private String userId;

    /**
     * 关键字首字母
     */
    private String letter;

    // 创建时间 createtime

    /**
     * 类型：0 为全部 1 为单字 2 为词语 3 为成语 4 为诗词 5 为作文
     */
    private int type;
    /**
     * 关键字：单字 word, 词语 term 成语 term 诗词 pid 作文 cid
     */
    private String key;
    /**
     * 拼音（单字, 词语，成语）
     */
    private String pinyin;

    /**
     * 关键字显示文本
     */
    private String title;
    /**
     * （诗词和作文）内容摘要
     */
    private String content;
    /**
     * 标贴（诗词保存朝代和作者，作文保存相关标签）
     */
    private String tags;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLetter() {
        return letter;
    }

    public void setLetter(String letter) {
        this.letter = letter;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

}
