package net.xplife.system.community.entity.chinese;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 作文打印统计
 */
@Document(collection = "V1_CompositionPrintStat")
public class CompositionPrintStat extends IdEntity {

    private static final long serialVersionUID = 1L;
    public static final String COLL = "V1_CompositionPrintStat";
    public static final String COMP_ID_FIELD = "compId";
    public static final String COUNT_FIELD  = "count";

    /**
     * 作文ID
     */
    @Indexed(name = "_compId_")
    private String compId;

    /**
     * 统计次数
     */
    private int count;

    public String getCompId() {
        return compId;
    }

    public void setCompId(String compId) {
        this.compId = compId;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

}
