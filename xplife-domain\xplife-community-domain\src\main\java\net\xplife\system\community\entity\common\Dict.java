package net.xplife.system.community.entity.common;

import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;

@Document(collection = "V1_Dict")
public class Dict extends IdEntity {
    private String type;    // 字典类型（如 industry）
    private String code;    // 字典项编码（如 industry_software）
    private String name;    // 字典项名称（如 软件行业）
    private String desc;    // 描述（可选）
    private Integer sort;   // 排序（可选）

    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }
    public String getCode() {
        return code;
    }
    public void setCode(String code) {
        this.code = code;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getDesc() {
        return desc;
    }
    public void setDesc(String desc) {
        this.desc = desc;
    }
    public Integer getSort() {
        return sort;
    }
    public void setSort(Integer sort) {
        this.sort = sort;
    }
} 