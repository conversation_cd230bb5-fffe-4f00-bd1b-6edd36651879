package net.xplife.system.community.entity.device;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 设备打印日志
 */
@Document(collection = "V1_DevicePrintLog")
public class DevicePrintLog extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_DevicePrintLog";
    public static final String USER_ID_FIELD    = "userId";
    public static final String DEVICE_ID_FIELD  = "deviceId";
    @Indexed(name = "_userid_")
    private String             userId;                                // 用户ID
    private String             deviceId;                              // 设备ID
    private String             deviceName;                            // 设备名称
    private String             deviceVersion;                         // 设备版本
    private String             appVersion;                            // 应用版本
    private String             pic;                                   // 打印图片地址
    private String             flag;                                  // 标识--图片地址MD5

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceVersion() {
        return deviceVersion;
    }

    public void setDeviceVersion(String deviceVersion) {
        this.deviceVersion = deviceVersion;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }
}
