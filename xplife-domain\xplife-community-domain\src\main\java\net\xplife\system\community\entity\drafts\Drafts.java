package net.xplife.system.community.entity.drafts;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.drafts.DraftsParamVo;
/**
 * 草稿箱
 */
@Document(collection = "V1_Drafts")
public class Drafts extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_Drafts";
    public static final String USER_ID_FIELD    = "userId";
    public static final String TYPE_FIELD       = "type";
    public static final String SUB_TYPE_FIELD   = "subType";
    public static final String LENGTH_FIELD     = "length";
    public static final String DRIVER_TYPE_FIELD= "driverType";
    private String             resPicId;                      // 资源图片ID
    private String             resDataId;                     // 资源数据ID
    @Indexed(name = "_userid_")
    private String             userId;                        // 用户ID
    private int                type;                          // 草稿箱类型 0--草稿箱 1--我的历史
    private int                subType;                       // 草稿箱副类型 0编辑纸条 1清单 2大字横幅 3便利贴 4网页打印
    private int                length;                        // 纸张长度
    private DraftsParamVo      draftsParam;                   // 草稿箱参数
    private int                placeType;                     // 编辑方向，可选，默认0，打竖，1为横向\
    private int                driverType;                    // 打印设备：0为默认，星星机机；1：A4打印机
    private String             appName;                       // 系统标识名
    private String             printerType;                   // 打印机型号
    private String             version;

    public String getResPicId() {
        return resPicId;
    }

    public void setResPicId(String resPicId) {
        this.resPicId = resPicId;
    }

    public String getResDataId() {
        return resDataId;
    }

    public void setResDataId(String resDataId) {
        this.resDataId = resDataId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public DraftsParamVo getDraftsParam() {
        return draftsParam;
    }

    public void setDraftsParam(DraftsParamVo draftsParam) {
        this.draftsParam = draftsParam;
    }

    public int getPlaceType() { return placeType; }

    public void setPlaceType(int placeType) { this.placeType = placeType; }

    public int getDriverType() {
        return driverType;
    }

    public void setDriverType(int driverType) {
        this.driverType = driverType;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
