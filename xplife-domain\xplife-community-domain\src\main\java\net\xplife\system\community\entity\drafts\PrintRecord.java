package net.xplife.system.community.entity.drafts;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 打印记录
 */
@Document(collection = "V1_PrintRecord")
public class PrintRecord extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_PrintRecord";
    public static final String SHARE_ID_FIELD   = "shareId";
    @Indexed(name = "_shareid_")
    private String             shareId;                            // 共享打印ID
    private String             resId;                              // 资源ID
    private String             nickName;                           // 回复人昵称
    private String             userPic;                            // 回复人头像
    private String             sex;                                // 回复人性别
    private int                anonymous;                          // 是否匿名
    private String             from;                               // 回复来源 wx qq mom sina
    private int                isRead;                             // 是否已读
    private int                type;                               // 显示类型 0公共显示 1单方显示
    private String             content;                            // 显示内容

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getIsRead() {
        return isRead;
    }

    public void setIsRead(int isRead) {
        this.isRead = isRead;
    }

    public String getShareId() {
        return shareId;
    }

    public void setShareId(String shareId) {
        this.shareId = shareId;
    }

    public String getResId() {
        return resId;
    }

    public void setResId(String resId) {
        this.resId = resId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getUserPic() {
        return userPic;
    }

    public void setUserPic(String userPic) {
        this.userPic = userPic;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public int getAnonymous() {
        return anonymous;
    }

    public void setAnonymous(int anonymous) {
        this.anonymous = anonymous;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }
}
