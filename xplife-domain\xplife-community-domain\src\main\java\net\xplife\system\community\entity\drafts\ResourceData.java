package net.xplife.system.community.entity.drafts;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.feed.PicVo;
/**
 * 我的资源
 */
@Document(collection = "V1_ResourceData")
public class ResourceData extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_ResourceData";
    @Indexed(name = "_userid_")
    private String             userId;                              // 用户ID
    private PicVo              resUrl;                              // 资源地址
    private int                resType;                             // 资源类型 0--编辑纸条数据 1--图片地址 2--语音 3--共享打印
    private String             flag;                                // 资源标识--MD5
    private String             content;                             // 内容

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public PicVo getResUrl() {
        return resUrl;
    }

    public void setResUrl(PicVo resUrl) {
        this.resUrl = resUrl;
    }

    public int getResType() {
        return resType;
    }

    public void setResType(int resType) {
        this.resType = resType;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }
}
