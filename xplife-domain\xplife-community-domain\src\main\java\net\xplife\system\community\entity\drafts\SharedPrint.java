package net.xplife.system.community.entity.drafts;
import java.util.Date;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 共享打印
 */
@Document(collection = "V1_SharedPrint")
public class SharedPrint extends IdEntity {
    private static final long  serialVersionUID    = 1L;
    public static final String COLL                = "V1_SharedPrint";
    public static final String USER_ID_FIELD       = "userId";
    public static final String RECOVERY_TIME_FIELD = "recoveryTime";
    private String             userId;                                // 用户ID
    private int                codeId;                                // 用户codeId
    private String             title;                                 // 标题
    private Date               validityTime;                          // 有效期
    private Date               sendTime;                              // 发送时间
    private int                totalMinute;                           // 总分钟
    private String             from;                                  // 回复来源 wx qq mom sina
    private Date               recoveryTime;                          // 最后回复时间
    private String             lastId;                                // 最近一次记录ID
    @Indexed(name = "_removeid_")
    private String             removeId;                              // 删除记录时的ID

    public int getCodeId() {
        return codeId;
    }

    public void setCodeId(int codeId) {
        this.codeId = codeId;
    }

    public String getLastId() {
        return lastId;
    }

    public void setLastId(String lastId) {
        this.lastId = lastId;
    }

    public String getRemoveId() {
        return removeId;
    }

    public void setRemoveId(String removeId) {
        this.removeId = removeId;
    }

    public int getTotalMinute() {
        return totalMinute;
    }

    public void setTotalMinute(int totalMinute) {
        this.totalMinute = totalMinute;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getValidityTime() {
        return validityTime;
    }

    public void setValidityTime(Date validityTime) {
        this.validityTime = validityTime;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public Date getRecoveryTime() {
        return recoveryTime;
    }

    public void setRecoveryTime(Date recoveryTime) {
        this.recoveryTime = recoveryTime;
    }
}
