package net.xplife.system.community.entity.feed;
import java.util.Date;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 评论回复表
 */
@Document(collection = "V1_CommentReply")
public class CommentReply extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_CommentReply";
    public static final String COMM_ID_FIELD    = "commId";
    public static final String REPLY_DATE_FIELD = "replyDate";
    private String             commId;                              // 评论ID
    private String             replyUserId;                         // 回复人
    private String             replyObjectId;                       // 回复对象
    private int                type;                                // 回复类型
    private String             conetnt;                             // 回复内容
    private Date               replyDate;                           // 回复时间

    public Date getReplyDate() {
        return replyDate;
    }

    public void setReplyDate(Date replyDate) {
        this.replyDate = replyDate;
    }

    public String getCommId() {
        return commId;
    }

    public void setCommId(String commId) {
        this.commId = commId;
    }

    public String getReplyUserId() {
        return replyUserId;
    }

    public void setReplyUserId(String replyUserId) {
        this.replyUserId = replyUserId;
    }

    public String getReplyObjectId() {
        return replyObjectId;
    }

    public void setReplyObjectId(String replyObjectId) {
        this.replyObjectId = replyObjectId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getConetnt() {
        return conetnt;
    }

    public void setConetnt(String conetnt) {
        this.conetnt = conetnt;
    }
}
