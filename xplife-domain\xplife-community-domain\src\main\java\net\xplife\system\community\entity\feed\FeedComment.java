package net.xplife.system.community.entity.feed;
import java.util.Date;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 动态评论表
 */
@Document(collection = "V1_FeedComment")
public class FeedComment extends IdEntity {
    private static final long  serialVersionUID      = 1L;
    public static final String COLL                  = "V1_FeedComment";
    public static final String FEED_ID_FIELD         = "feedId";
    public static final String COMMENT_USER_ID_FIELD = "commentUserId";
    public static final String COMMENT_DATE_FIELD    = "commentDate";
    private String             feedId;                                  // 动态ID
    private String             commentUserId;                           // 评论用户
    private Date               commentDate;                             // 评论时间
    private String             content;                                 // 评论内容
    private int                replyNum;                                // 回复数量

    public int getReplyNum() {
        return replyNum;
    }

    public void setReplyNum(int replyNum) {
        this.replyNum = replyNum;
    }

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public String getCommentUserId() {
        return commentUserId;
    }

    public void setCommentUserId(String commentUserId) {
        this.commentUserId = commentUserId;
    }

    public Date getCommentDate() {
        return commentDate;
    }

    public void setCommentDate(Date commentDate) {
        this.commentDate = commentDate;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
