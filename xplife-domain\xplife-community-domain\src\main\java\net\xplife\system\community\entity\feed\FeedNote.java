package net.xplife.system.community.entity.feed;
import java.util.List;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.feed.PicVo;
/**
 * 动态帖子表
 */
@Document(collection = "V1_FeedNote")
public class FeedNote extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_FeedNote";
    public static final String FEED_NUM_FIELD   = "feedNum";
    public static final String USER_ID_FIELD    = "userId";
    public static final String HAS_PIC_FIELD    = "hasPic";
    public static final String IS_HOT_FIELD     = "isHot";
    public static final String LABELID_FIELD    = "labelIds";
    public static final String STICK_NUM_FIELD  = "stickNum";
    public static final String SUBJECT_FIELD    = "subject";
    public static final String GRADE_LEVEL_FIELD= "gradeLevel";
    public static final String GRADE_NUM_FIELD  = "gradeNum";
    public static final String KEYWORD_FIELD = "keyword";
    @Indexed(name = "_userid_")
    private String             userId;                          // 用户ID
    private String             title;                           // 标题
    private String             content;                         //
    private List<PicVo>        pic;                             // 图片
    @Indexed(name = "_labelIds_")
    private List<String>       labelIds;                        // 标签集合
    @Indexed(name = "_haspic_")
    private int                hasPic;                          // 是否有图片
    @Indexed(name = "_ishot_")
    private int                isHot;                           // 是否热门动态
    private String             htmlUrl;                         // H5地址
    @Indexed(name = "_stickNum_")
    private int                stickNum;                        // 0:无； 1：精华帖； 999：置顶
    @Indexed(name = "_subject_")
    private String             subject;
    @Indexed(name = "_gradeLevel_")
    private String             gradeLevel;                      // xx,cz,gz
    @Indexed(name = "_gradeNum_")
    private int                gradeNum;                        // 具体年级
    private String             keyword;                         // keyword,新增时作为保存字段

    public String getHtmlUrl() {
        return htmlUrl;
    }

    public void setHtmlUrl(String htmlUrl) {
        this.htmlUrl = htmlUrl;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<PicVo> getPic() {
        return pic;
    }

    public void setPic(List<PicVo> pic) {
        this.pic = pic;
    }

    public List<String> getLabelIds() {
        return labelIds;
    }

    public void setLabelIds(List<String> labelIds) {
        this.labelIds = labelIds;
    }

    public int getHasPic() {
        return hasPic;
    }

    public void setHasPic(int hasPic) {
        this.hasPic = hasPic;
    }

    public int getIsHot() {
        return isHot;
    }

    public void setIsHot(int isHot) {
        this.isHot = isHot;
    }

    public int getStickNum() {
        return stickNum;
    }

    public void setStickNum(int stickNum) {
        this.stickNum = stickNum;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getGradeLevel() {
        return gradeLevel;
    }

    public void setGradeLevel(String gradeLevel) {
        this.gradeLevel = gradeLevel;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public int getGradeNum() {
        return gradeNum;
    }

    public void setGradeNum(int gradeNum) {
        this.gradeNum = gradeNum;
    }
}
