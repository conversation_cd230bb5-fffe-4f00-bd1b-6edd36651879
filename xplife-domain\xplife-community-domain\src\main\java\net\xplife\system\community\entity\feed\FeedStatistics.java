package net.xplife.system.community.entity.feed;
import java.util.Map;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.feed.FeedStatisticsVo;
/**
 * 动态统计信息
 */
@Document(collection = "V1_FeedStatistics")
public class FeedStatistics extends IdEntity {
    private static final long             serialVersionUID    = 1L;
    public static final String            COLL                = "V1_FeedStatistics";
    public static final String            FEED_ID_FIELD       = "feedId";
    public static final String            LIKE_NUM_FIELD      = "likeNum";
    public static final String            DOWN_LOAD_NUM_FIELD = "downLoadNum";
    public static final String            PRINT_NUM_FIELD     = "printNum";
    public static final String            SHARE_NUM_FIELD     = "shareNum";
    public static final String            GIVE_LIKE_NUM_FIELD     = "giveLikeNum";
    @Indexed(name = "_feedid_")
    private String                        feedId;                                   // 动态ID
    private int                           likeNum;                                  // 喜欢数量 -》收藏数量
    private int                           downLoadNum;                              // 下载数量
    private int                           printNum;                                 // 打印数量
    private int                           commentNum;                               // 评论数量
    private int                           shareNum;                                 // 分享数量
    private int                           giveLikeNum;                              // 点赞数量
    private Map<String, FeedStatisticsVo> likeVo;                                   // 喜欢数量
    private Map<String, FeedStatisticsVo> downLoadVo;                               // 下载数量
    private Map<String, FeedStatisticsVo> printVo;                                  // 打印数量

    private int                           forgeDownloadNum;
    private int                           forgePrintNum;
    private int                           forgeShareNum;
    private int                           forgeGiveLikeNum;

    public int getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(int commentNum) {
        this.commentNum = commentNum;
    }

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public int getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(int likeNum) {
        this.likeNum = likeNum;
    }

    public int getDownLoadNum() {
        return downLoadNum;
    }

    public void setDownLoadNum(int downLoadNum) {
        this.downLoadNum = downLoadNum;
    }

    public int getPrintNum() {
        return printNum;
    }

    public void setPrintNum(int printNum) {
        this.printNum = printNum;
    }

    public Map<String, FeedStatisticsVo> getLikeVo() {
        return likeVo;
    }

    public void setLikeVo(Map<String, FeedStatisticsVo> likeVo) {
        this.likeVo = likeVo;
    }

    public Map<String, FeedStatisticsVo> getDownLoadVo() {
        return downLoadVo;
    }

    public void setDownLoadVo(Map<String, FeedStatisticsVo> downLoadVo) {
        this.downLoadVo = downLoadVo;
    }

    public Map<String, FeedStatisticsVo> getPrintVo() {
        return printVo;
    }

    public void setPrintVo(Map<String, FeedStatisticsVo> printVo) {
        this.printVo = printVo;
    }

    public int getShareNum() {
        return shareNum;
    }

    public void setShareNum(int shareNum) {
        this.shareNum = shareNum;
    }

    public int getGiveLikeNum() {
        return giveLikeNum;
    }

    public void setGiveLikeNum(int giveLikeNum) {
        this.giveLikeNum = giveLikeNum;
    }

    public int getForgeDownloadNum() {
        return forgeDownloadNum;
    }

    public void setForgeDownloadNum(int forgeDownloadNum) {
        this.forgeDownloadNum = forgeDownloadNum;
    }

    public int getForgePrintNum() {
        return forgePrintNum;
    }

    public void setForgePrintNum(int forgePrintNum) {
        this.forgePrintNum = forgePrintNum;
    }

    public int getForgeShareNum() {
        return forgeShareNum;
    }

    public void setForgeShareNum(int forgeShareNum) {
        this.forgeShareNum = forgeShareNum;
    }

    public int getForgeGiveLikeNum() {
        return forgeGiveLikeNum;
    }

    public void setForgeGiveLikeNum(int forgeGiveLikeNum) {
        this.forgeGiveLikeNum = forgeGiveLikeNum;
    }
}
