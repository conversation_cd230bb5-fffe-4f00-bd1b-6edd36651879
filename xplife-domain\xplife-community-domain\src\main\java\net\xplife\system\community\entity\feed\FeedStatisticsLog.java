package net.xplife.system.community.entity.feed;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 动态统计信息
 */
@Document(collection = "V1_FeedStatisticsLog")
public class FeedStatisticsLog extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_FeedStatisticsLog";
    public static final String FEED_ID_FIELD    = "feedId";
    public static final String TYPE_FIELD       = "type";
    @Indexed(name = "_feedid_")
    private String             feedId;                                   // 动态ID
    private String             userId;                                   // 用户ID
    private int                type;                                     // 日志类型

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
