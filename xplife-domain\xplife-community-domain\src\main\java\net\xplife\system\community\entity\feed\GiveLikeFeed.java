package net.xplife.system.community.entity.feed;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 点赞的动态
 */
@Document(collection = "V1_GiveLikeFeed")
public class GiveLikeFeed extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_GiveLikeFeed";
    public static final String FEED_ID_FIELD    = "feedId";
    public static final String USER_ID_FIELD    = "userId";
    public static final String FLAG_FIELD       = "flag";
    private String             feedId;                          // 动态ID
    @Indexed(name = "_userid_")
    private String             userId;                          // 用户ID

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
