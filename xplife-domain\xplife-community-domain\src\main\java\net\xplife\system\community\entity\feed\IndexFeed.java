package net.xplife.system.community.entity.feed;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 首页动态
 */
@Document(collection = "V1_IndexFeed")
public class IndexFeed extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_IndexFeed";
    public static final String FEED_ID_FIELD    = "feedId";
    public static final String TYPE_FIELD       = "type";
    public static final String SORT_FIELD       = "sort";
    private String             feedId;                           // 动态ID
    @Indexed(name = "_type_")
    private int                type;                             // 类型 0--最新 1--热门
    private long               sort;                             // 排序字段

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getSort() {
        return sort;
    }

    public void setSort(long sort) {
        this.sort = sort;
    }
}
