package net.xplife.system.community.entity.feed;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.feed.PicVo;
/**
 * 喜欢的动态
 */
@Document(collection = "V1_LikeFeed")
public class LikeFeed extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_LikeFeed";
    public static final String FEED_ID_FIELD    = "feedId";
    public static final String USER_ID_FIELD    = "userId";
    public static final String FLAG_FIELD       = "flag";
    private String             feedId;                          // 动态ID
    @Indexed(name = "_userid_")
    private String             userId;                          // 用户ID
    private PicVo              pic;                             // 图片地址
    private String             flag;                            // 标识--图片地址MD5

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public PicVo getPic() {
        return pic;
    }

    public void setPic(PicVo pic) {
        this.pic = pic;
    }

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
