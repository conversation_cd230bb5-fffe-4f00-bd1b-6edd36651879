package net.xplife.system.community.entity.feed;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 屏蔽用户
 */
@Document(collection = "V1_ShieldFeed")
public class ShieldFeed extends IdEntity {
    private static final long  serialVersionUID     = 1L;
    public static final String COLL                 = "V1_ShieldFeed";
    public static final String USER_ID_FIELD        = "userId";
    public static final String SHIELD_FEED_ID_FIELD = "shieldFeedId";
    @Indexed(name = "_userid_")
    private String             userId;                                // 用户ID
    private String             shieldFeedId;                          // 屏蔽动态ID

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getShieldFeedId() {
        return shieldFeedId;
    }

    public void setShieldFeedId(String shieldFeedId) {
        this.shieldFeedId = shieldFeedId;
    }
}
