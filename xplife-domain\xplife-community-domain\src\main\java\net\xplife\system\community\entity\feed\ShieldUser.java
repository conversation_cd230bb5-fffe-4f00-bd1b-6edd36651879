package net.xplife.system.community.entity.feed;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 屏蔽用户
 */
@Document(collection = "V1_ShieldUser")
public class ShieldUser extends IdEntity {
    private static final long  serialVersionUID     = 1L;
    public static final String COLL                 = "V1_ShieldUser";
    public static final String USER_ID_FIELD        = "userId";
    public static final String SHIELD_USER_ID_FIELD = "shieldUserId";
    @Indexed(name = "_userid_")
    private String             userId;                                // 用户ID
    private String             shieldUserId;                          // 屏蔽用户ID

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getShieldUserId() {
        return shieldUserId;
    }

    public void setShieldUserId(String shieldUserId) {
        this.shieldUserId = shieldUserId;
    }
}
