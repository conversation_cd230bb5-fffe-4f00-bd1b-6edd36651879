package net.xplife.system.community.entity.feed;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 我的上传
 */
@Document(collection = "V1_UserFeed")
public class UserFeed extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_UserFeed";
    public static final String USER_ID_FIELD    = "userId";
    public static final String FEED_ID_FIELD    = "feedId";
    @Indexed(name = "_userid_")
    private String             userId;                          // 用户ID
    @Indexed(name = "_feedid_")
    private String             feedId;                          // 动态ID

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }
}
