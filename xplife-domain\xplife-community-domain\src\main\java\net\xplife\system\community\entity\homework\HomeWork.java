package net.xplife.system.community.entity.homework;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/8 17:12
 * @description：作业模块
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_HomeWork")
public class HomeWork  extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_HomeWork";

    @Indexed(name = "_printerSn_")
    private String printerSn;           //打印机唯一标识
    @Indexed(name = "_subject_")
    private String subject;                // 作业科目
    private String fileName;               // 文件名
    private String fileUrl;                // url地址
    private String userName;               // 创建者
    private String fileType;               // 文件类型

    public String getPrinterSn() {
        return printerSn;
    }

    public void setPrinterSn(String printerSn) {
        this.printerSn = printerSn;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
}
