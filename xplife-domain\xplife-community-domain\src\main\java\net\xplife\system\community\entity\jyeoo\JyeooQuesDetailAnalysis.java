package net.xplife.system.community.entity.jyeoo;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2022/1/14 14:17
 * @description：箐优网查询的试题解析
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_JyeooQuesDetailAnalysis")
public class JyeooQuesDetailAnalysis extends IdEntity {
    private static final long  serialVersionUID = 1L;
    private String subject;

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }
}
