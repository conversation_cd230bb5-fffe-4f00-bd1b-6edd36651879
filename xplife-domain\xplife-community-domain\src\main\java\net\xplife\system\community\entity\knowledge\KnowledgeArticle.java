package net.xplife.system.community.entity.knowledge;

import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.knowledge.KnowledgePicVo;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = "V1_KnowledgeArticle")
public class KnowledgeArticle extends IdEntity {
    private static final long  serialVersionUID     = 1L;
    public static final String COLL                 = "V1_KnowledgeArticle";
    public static final String SUBJECT_FIELD        = "subject";
    public static final String GRADE_LEVEL_FIELD    = "gradeLevel";
    public static final String TITLE_FIELD          = "title";
    public static final String SORT_FIELD           = "sortNum";
    private String                      title;           // 标题
    private String                      headUrl;         // 封面
    private String                      subject;         // 学科
    private String                      gradeLevel;      // 年级，xc小学、cc初中、gz高中、dx大学
    private String                      contents;        // h5内容
    private int                         shareNum;        // 解锁次数、分享次数
    private int                         printNum;        // 打印次数
    private int                         clickNum;        // 点击次数
    private List<KnowledgePicVo>        pics;            // 图片集合，供打印用
    private int                         sortNum;         // 整体排序用，越大越排前面
    private String                      version;         // 该版本后才显示

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    public String getContents() {
        return contents;
    }

    public void setContents(String contents) {
        this.contents = contents;
    }

    public List<KnowledgePicVo> getPics() {
        return pics;
    }

    public void setPics(List<KnowledgePicVo> pics) {
        this.pics = pics;
    }

    public int getShareNum() {
        return shareNum;
    }

    public void setShareNum(int shareNum) {
        this.shareNum = shareNum;
    }

    public int getPrintNum() {
        return printNum;
    }

    public void setPrintNum(int printNum) {
        this.printNum = printNum;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getGradeLevel() {
        return gradeLevel;
    }

    public void setGradeLevel(String gradeLevel) {
        this.gradeLevel = gradeLevel;
    }

    public int getClickNum() {
        return clickNum;
    }

    public void setClickNum(int clickNum) {
        this.clickNum = clickNum;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
