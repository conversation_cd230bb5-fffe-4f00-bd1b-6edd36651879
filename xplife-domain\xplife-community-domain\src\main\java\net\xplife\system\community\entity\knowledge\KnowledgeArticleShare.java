package net.xplife.system.community.entity.knowledge;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "V1_KnowledgeArticleShare")
public class KnowledgeArticleShare extends IdEntity {
    private static final long  serialVersionUID          = 1L;
    public static final String COLL                      = "V1_KnowledgeArticleShare";
    public static final String USER_ID_FIELD             = "userId";
    public static final String KNOWLEDGE_ID_FIELD        = "knowledgeId";
    @Indexed(name = "_userId_")
    private String                      userId;           // 标题
    private String                      knowledgeId;      // 封面

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getKnowledgeId() {
        return knowledgeId;
    }

    public void setKnowledgeId(String knowledgeId) {
        this.knowledgeId = knowledgeId;
    }
}
