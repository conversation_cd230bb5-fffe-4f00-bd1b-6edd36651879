package net.xplife.system.community.entity.label;
import java.util.Map;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 标签表
 */
@Document(collection = "V1_Label")
public class Label extends IdEntity {
    private static final long   serialVersionUID = 1L;
    public static final String  COLL             = "V1_Label";
    public static final String  SORT_FIELD       = "sort";
    private String              name;                         // 标签名称
    private Map<String, String> picMap;                       // 标签图片
    private String              remark;                       // 标签描述
    private int                 sort;                         // 排序
    private int                 showType;                     // 显示类型
    private String              titleZh;                      // 中文名称
    private String              titleEn;                      // 英文名称

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, String> getPicMap() {
        return picMap;
    }

    public void setPicMap(Map<String, String> picMap) {
        this.picMap = picMap;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getShowType() {
        return showType;
    }

    public void setShowType(int showType) {
        this.showType = showType;
    }

    public String getTitleZh() {
        return titleZh;
    }

    public void setTitleZh(String titleZh) {
        this.titleZh = titleZh;
    }

    public String getTitleEn() {
        return titleEn;
    }

    public void setTitleEn(String titleEn) {
        this.titleEn = titleEn;
    }

}
