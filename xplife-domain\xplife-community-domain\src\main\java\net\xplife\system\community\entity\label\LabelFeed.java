package net.xplife.system.community.entity.label;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.feed.PicVo;
/**
 * 标签动态表
 */
@Document(collection = "V1_LabelFeed")
public class LabelFeed extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_LabelFeed";
    public static final String LABEL_ID_FIELD   = "labelId";
    public static final String FEED_ID_FIELD    = "feedId";
    public static final String FLAG_FIELD       = "flag";
    public static final String TYPE_FIELD       = "type";
    @Indexed(name = "_labelid_")
    private String             labelId;                          // 标签ID
    private String             feedId;                           // 动态ID
    private PicVo              pic;                              // 图片地址
    private String             flag;                             // 标识--图片地址MD5
    private int                type;                             // 类型 0标签 1全部

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public PicVo getPic() {
        return pic;
    }

    public void setPic(PicVo pic) {
        this.pic = pic;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getLabelId() {
        return labelId;
    }

    public void setLabelId(String labelId) {
        this.labelId = labelId;
    }

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }
}
