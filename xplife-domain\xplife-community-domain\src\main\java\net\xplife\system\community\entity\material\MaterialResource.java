package net.xplife.system.community.entity.material;
import java.util.Date;
import java.util.Map;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.feed.PicVo;
/**
 * 素材库资源
 */
@Document(collection = "V1_MaterialResource")
public class MaterialResource extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_MaterialResource";
    public static final String M_ID_FIELD       = "mId";
    private String             mId;                                     // 素材库ID
    private int                length;                                  // 纸张长度
    private Map<String, PicVo> resMap;                                  // 资源集合
    private Map<String, PicVo> resMapA4;                                  // 资源集合
    private Map<String, PicVo> resMapA5;                                  // 资源集合
    private int                type;                                    // 主类型
    private int                subType;                                 // 副类型
    private int                placeType;                               // 0：竖向；1：横向
    private int                isNew;                   // 是否标识new
    private Date               newFlagBeforeDate;       // 如果此值有，当前时间超过此值，则不再显示isNew
    private String             name;// 资源名称

    public String getmId() {
        return mId;
    }

    public void setmId(String mId) {
        this.mId = mId;
    }

    public Map<String, PicVo> getResMap() {
        return resMap;
    }

    public void setResMap(Map<String, PicVo> resMap) {
        this.resMap = resMap;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public int getPlaceType() {
        return placeType;
    }

    public void setPlaceType(int placeType) {
        this.placeType = placeType;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }

    public Date getNewFlagBeforeDate() {
        return newFlagBeforeDate;
    }

    public void setNewFlagBeforeDate(Date newFlagBeforeDate) {
        this.newFlagBeforeDate = newFlagBeforeDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, PicVo> getResMapA4() {
        return resMapA4;
    }

    public void setResMapA4(Map<String, PicVo> resMapA4) {
        this.resMapA4 = resMapA4;
    }

    public Map<String, PicVo> getResMapA5() {
        return resMapA5;
    }

    public void setResMapA5(Map<String, PicVo> resMapA5) {
        this.resMapA5 = resMapA5;
    }
}
