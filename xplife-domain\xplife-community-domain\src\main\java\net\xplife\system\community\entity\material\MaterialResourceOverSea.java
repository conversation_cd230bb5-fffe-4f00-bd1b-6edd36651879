package net.xplife.system.community.entity.material;

import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.feed.PicVo;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.Map;

/**
 * 素材库资源(海外版)
 */
@Document(collection = "V1_MaterialResourceOverSea")
public class MaterialResourceOverSea extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_MaterialResourceOverSea";
    public static final String M_ID_FIELD       = "mId";
    private String             mId;                                     // 素材库ID
    private int                length;                                  // 纸张长度
    private Map<String, PicVo> resMap;                                  // 资源集合
    private int                type;                                    // 主类型
    private int                subType;                                 // 副类型
    private int                placeType;                               // 0：竖向；1：横向
    private int                isNew;                // 是否标识new
    private Date newFlagBeforeDate;       // 如果此值有，当前时间超过此值，则不再显示isNew

    public String getmId() {
        return mId;
    }

    public void setmId(String mId) {
        this.mId = mId;
    }

    public Map<String, PicVo> getResMap() {
        return resMap;
    }

    public void setResMap(Map<String, PicVo> resMap) {
        this.resMap = resMap;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public int getPlaceType() {
        return placeType;
    }

    public void setPlaceType(int placeType) {
        this.placeType = placeType;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }
    public Date getNewFlagBeforeDate() {
        return newFlagBeforeDate;
    }

    public void setNewFlagBeforeDate(Date newFlagBeforeDate) {
        this.newFlagBeforeDate = newFlagBeforeDate;
    }
}
