package net.xplife.system.community.entity.material;

import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.feed.PicVo;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * 素材库皮肤/边框（海外版）
 */
@Document(collection = "V1_MaterialSkinOverSea")
public class MaterialSkinOverSea extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_MaterialSkinOverSea";
    public static final String MID_FIELD        = "mId";
    private String             mId;                                 // 素材库ID
    private int                type;                                // 类型  0边框 1皮肤
    private Map<String, PicVo> resMap;                              // 素材资源

    public String getmId() {
        return mId;
    }

    public void setmId(String mId) {
        this.mId = mId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Map<String, PicVo> getResMap() {
        return resMap;
    }

    public void setResMap(Map<String, PicVo> resMap) {
        this.resMap = resMap;
    }
}
