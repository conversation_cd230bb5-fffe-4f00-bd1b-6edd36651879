package net.xplife.system.community.entity.printer;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2023/8/3 10:17
 * @description：被连接过的打印机数据
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_ConnectedPrinterInfo")
public class ConnectedPrinterInfo extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_UserPrinterInfo";
    public static final String COLUMN_PRINTERSN = "printerSn";
    public static final String COLUMN_MACADDRESS = "macAddress";
    public static final String COLUMN_BLUETOOTHNAME = "bluetoothName";
    public static final String COLUMN_USERID = "userId";

    @Indexed(name = "_printerSn_")
    private String printerSn;           //打印机唯一标识
    @Indexed(name = "_bluetoothName_")
    private String bluetoothName;       // 蓝牙名称

    private String version;             // 蓝牙名称
    @Indexed(name = "_macAddress_")
    private String macAddress;          // mac地址

    private String serverIp;            // 打印机IP地址
    private String serverPort;          // 打印机端口
    private boolean isTMall;            // 是否携带天猫精灵

    public String getPrinterSn() {
        return printerSn;
    }

    public void setPrinterSn(String printerSn) {
        this.printerSn = printerSn;
    }

    public String getBluetoothName() {
        return bluetoothName;
    }

    public void setBluetoothName(String bluetoothName) {
        this.bluetoothName = bluetoothName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getServerIp() {
        return serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    public String getServerPort() {
        return serverPort;
    }

    public void setServerPort(String serverPort) {
        this.serverPort = serverPort;
    }

    public boolean isTMall() {
        return isTMall;
    }

    public void setTMall(boolean TMall) {
        isTMall = TMall;
    }
}
