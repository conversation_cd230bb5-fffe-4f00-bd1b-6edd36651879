package net.xplife.system.community.entity.printer;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/15 14:20
 * @description：
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_UserPrinterInfo")
public class UserPrinterInfo extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_UserPrinterInfo";
    public static final String COLUMN_PRINTERSN = "printerSn";
    public static final String COLUMN_USERID = "userId";

    @Indexed(name = "_printerSn_")
    private String printerSn;           //打印机唯一标识
    @Indexed(name = "_userId_")
    private String userId;               // 创建者

    private String printerName;         // 打印机别名
    private String macAddress;          // mac地址
    private String type;                // 打印机类型
    private String bluetoothName;       // 蓝牙名称

    private boolean managerFlag;        // 是否管理者身份
    private boolean demoFlag;           // 是否体验版

    public String getPrinterSn() {
        return printerSn;
    }

    public void setPrinterSn(String printerSn) {
        this.printerSn = printerSn;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPrinterName() {
        return printerName;
    }

    public void setPrinterName(String printerName) {
        this.printerName = printerName;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBluetoothName() {
        return bluetoothName;
    }

    public void setBluetoothName(String bluetoothName) {
        this.bluetoothName = bluetoothName;
    }

    public boolean isManagerFlag() {
        return managerFlag;
    }

    public void setManagerFlag(boolean managerFlag) {
        this.managerFlag = managerFlag;
    }

    public boolean isDemoFlag() {
        return demoFlag;
    }

    public void setDemoFlag(boolean demoFlag) {
        this.demoFlag = demoFlag;
    }
}
