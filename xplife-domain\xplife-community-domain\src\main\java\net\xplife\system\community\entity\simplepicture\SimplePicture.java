package net.xplife.system.community.entity.simplepicture;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/7 10:29
 * @description：简笔画
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_SimplePicture")
public class SimplePicture  extends IdEntity {
    private static final long   serialVersionUID = 1L;
    public static final String  COLL             = "V1_SimplePicture";
    public static final String  SORT_FIELD       = "sort";
    public static final String  TYPE_field       = "type";
    private String              name;                         // 标签名称
    private String              remark;                       // 标签描述
    @Indexed(name = "_type_")
    private String              type;                         // 类型
    private SimplePicVo         picVo;                        // 所含图片的地址信息
    private int                 sort;                         // 排序
    private String              sizeType;                     // 尺寸 2寸：a2 或  A4：a4

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public SimplePicVo getPicVo() {
        return picVo;
    }

    public void setPicVo(SimplePicVo picVo) {
        this.picVo = picVo;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getSizeType() {
        return sizeType;
    }

    public void setSizeType(String sizeType) {
        this.sizeType = sizeType;
    }
}
