package net.xplife.system.community.entity.soulword;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2022/1/6 11:19
 * @description：心灵鸡汤
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_SoulWord")
public class SoulWord extends IdEntity {
    private static final long   serialVersionUID = 1L;
    public static final String  COLL             = "V1_SoulWord";
    public static final String COLUMN_CONTENT = "content";
    private String              content;        // 内容

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
