package net.xplife.system.community.entity.sq;
import java.util.List;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.sq.LabelVo;
/**
 * 收藏标签
 */
@Document(collection = "V1_CollectLabel")
public class CollectLabel extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_CollectLabel";
    public static final String USER_ID_FIELD    = "userId";
    public static final String TYPE_FIELD       = "type";
    @Indexed(name = "_userid_")
    private String             userId;                              // 用户ID
    private List<LabelVo>      labelList;                           // 标签目录集合
    private List<LabelVo>      errorType;                           // 错题类型
    private List<LabelVo>      errorReason;                         // 错题原因
    private List<LabelVo>      errorSource;                         // 错题来源
    private List<LabelVo>      customLabel;                         // 自定义标签

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<LabelVo> getLabelList() {
        return labelList;
    }

    public void setLabelList(List<LabelVo> labelList) {
        this.labelList = labelList;
    }

    public List<LabelVo> getErrorType() {
        return errorType;
    }

    public void setErrorType(List<LabelVo> errorType) {
        this.errorType = errorType;
    }

    public List<LabelVo> getErrorReason() {
        return errorReason;
    }

    public void setErrorReason(List<LabelVo> errorReason) {
        this.errorReason = errorReason;
    }

    public List<LabelVo> getErrorSource() {
        return errorSource;
    }

    public void setErrorSource(List<LabelVo> errorSource) {
        this.errorSource = errorSource;
    }

    public List<LabelVo> getCustomLabel() {
        return customLabel;
    }

    public void setCustomLabel(List<LabelVo> customLabel) {
        this.customLabel = customLabel;
    }
}
