package net.xplife.system.community.entity.sq;
import java.util.List;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 错题本
 */
@Document(collection = "V1_ErrorBook")
public class ErrorBook extends IdEntity {
    private static final long  serialVersionUID   = 1L;
    public static final String COLL               = "V1_ErrorBook";
    public static final String USER_ID_FIELD      = "userId";
    public static final String TYPE_FIELD         = "type";
    public static final String ERROR_TYPE_FIELD   = "errorType";
    public static final String ERROR_DIFFIC_FIELD = "errorDiffic";
    public static final String ERROR_REASON_FIELD = "errorReason";
    public static final String ERROR_DEGREE_FIELD = "errorDegree";
    public static final String ERROR_SOURCE_FIELD = "errorSource";
    public static final String CUSTOM_LABEL_FIELD = "customLabel";
    @Indexed(name = "_userid_")
    private String             userId;                             // 用户ID
    private List<String>       contentIds;                         // 错题内容ID集合
    private String             url;                                // 问题图片资源
    private String             abstracts;                          // 摘要
    @Indexed(name = "_subject_")
    private String             type;                               // 类目类型-错题科目
    @Indexed(name = "_type_")
    private String             errorType;                          // 错题类型
    @Indexed(name = "_diffic_")
    private String             errorDiffic;                        // 错题难度
    @Indexed(name = "_reason_")
    private String             errorReason;                        // 错题原因
    @Indexed(name = "_degree_")
    private String             errorDegree;                        // 掌握程度
    @Indexed(name = "_source_")
    private String             errorSource;                        // 错题来源
    @Indexed(name = "_label_")
    private String             customLabel;                        // 自定义标签
    private String             searchSource;                       // 拍搜题来源

    public String getAbstracts() {
        return abstracts;
    }

    public void setAbstracts(String abstracts) {
        this.abstracts = abstracts;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<String> getContentIds() {
        return contentIds;
    }

    public void setContentIds(List<String> contentIds) {
        this.contentIds = contentIds;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public String getErrorDiffic() {
        return errorDiffic;
    }

    public void setErrorDiffic(String errorDiffic) {
        this.errorDiffic = errorDiffic;
    }

    public String getErrorReason() {
        return errorReason;
    }

    public void setErrorReason(String errorReason) {
        this.errorReason = errorReason;
    }

    public String getErrorDegree() {
        return errorDegree;
    }

    public void setErrorDegree(String errorDegree) {
        this.errorDegree = errorDegree;
    }

    public String getErrorSource() {
        return errorSource;
    }

    public void setErrorSource(String errorSource) {
        this.errorSource = errorSource;
    }

    public String getCustomLabel() {
        return customLabel;
    }

    public void setCustomLabel(String customLabel) {
        this.customLabel = customLabel;
    }

    public String getSearchSource() {
        return searchSource;
    }

    public void setSearchSource(String searchSource) {
        this.searchSource = searchSource;
    }
}
