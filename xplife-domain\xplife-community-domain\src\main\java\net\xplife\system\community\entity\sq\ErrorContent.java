package net.xplife.system.community.entity.sq;
import java.util.List;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.sq.AftContentVo;
/**
 * 错题内容
 */
@Document(collection = "V1_ErrorContent")
public class ErrorContent extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_ErrorContent";
    private String             sid;                                 // 箐优网的sid
    private String             subject;                             // 类目
    private String             abstracts;                           // 摘要
    private List<AftContentVo> aftContentVo;                        // 阿凡题内容集合
    private String             knowledge;                           // 知识点
    private String             html;                                // html信息
    private boolean            needRefresh;                         // 是否需要重新获取

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getAbstracts() {
        return abstracts;
    }

    public void setAbstracts(String abstracts) {
        this.abstracts = abstracts;
    }

    public List<AftContentVo> getAftContentVo() {
        return aftContentVo;
    }

    public void setAftContentVo(List<AftContentVo> aftContentVo) {
        this.aftContentVo = aftContentVo;
    }

    public String getKnowledge() {
        return knowledge;
    }

    public void setKnowledge(String knowledge) {
        this.knowledge = knowledge;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public boolean isNeedRefresh() {
        if (aftContentVo!=null && aftContentVo.size()>1){
            return false;
        } else {
            return true;
        }
    }

    public void setNeedRefresh(boolean needRefresh) {
        this.needRefresh = needRefresh;
    }
}
