package net.xplife.system.community.entity.sq;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;

/**
 * 收藏单词本
 */
@Document(collection = "V2_FavoriteBook")
public class FavoriteBook extends IdEntity {
  private static final long serialVersionUID = 1L;
  public static final String COLL = "V2_FavoriteBook";
  public static final String USER_ID_FIELD = "userId";
  public static final String BOOK_NAME_FIELD = "bookName";

  @Indexed(name = "_userid_")
  private String userId; // 用户ID
  private String bookName; // 单词本名称
  private String description; // 单词本描述
  private int wordCount; // 单词数量

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public String getBookName() {
    return bookName;
  }

  public void setBookName(String bookName) {
    this.bookName = bookName;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public int getWordCount() {
    return wordCount;
  }

  public void setWordCount(int wordCount) {
    this.wordCount = wordCount;
  }
}