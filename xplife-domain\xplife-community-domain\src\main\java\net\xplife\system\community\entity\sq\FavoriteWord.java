package net.xplife.system.community.entity.sq;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;

import java.util.Date;

/**
 * 收藏单词
 */
@Document(collection = "V2_FavoriteWord")
public class FavoriteWord extends IdEntity {
  private static final long serialVersionUID = 1L;
  public static final String COLL = "V2_FavoriteWord";
  public static final String USER_ID_FIELD = "userId";
  public static final String WORD_ID_FIELD = "wordId";
  public static final String BOOK_ID_FIELD = "bookId";

  @Indexed(name = "_userid_")
  private String userId; // 用户ID
  private String wordId; // 单词ID
  private String bookId; // 单词本ID
  private String word; // 单词内容（用于排序）
  private Date createTime; // 收藏时间

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public String getWordId() {
    return wordId;
  }

  public void setWordId(String wordId) {
    this.wordId = wordId;
  }

  public String getBookId() {
    return bookId;
  }

  public void setBookId(String bookId) {
    this.bookId = bookId;
  }

  public String getWord() {
    return word;
  }

  public void setWord(String word) {
    this.word = word;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }
}