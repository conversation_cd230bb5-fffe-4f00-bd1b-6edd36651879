package net.xplife.system.community.entity.sq;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;

import java.util.List;

/**
 * 错题收藏
 */
@Document(collection = "V1_QuestionCollect")
public class QuestionCollect extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_QuestionCollect";
    public static final String USER_ID_FIELD    = "userId";
    public static final String LABEL_ID_FIELD   = "labelId";
    public static final String ERROR_TYPE_FIELD   = "errorType";
    public static final String ERROR_DIFFIC_FIELD = "errorDiffic";
    public static final String ERROR_REASON_FIELD = "errorReason";
    public static final String ERROR_DEGREE_FIELD = "errorDegree";
    public static final String ERROR_SOURCE_FIELD = "errorSource";
    public static final String CUSTOM_LABEL_FIELD = "customLabel";
    @Indexed(name = "_userid_")
    private String             userId;                                 // 用户ID
    @Indexed(name = "_labelid_")
    private String             labelId;                                // 收藏标签ID
    private String             pic;                                    // 图片地址
    @Indexed(name = "_type_")
    private String             errorType;                              // 错题类型
    @Indexed(name = "_diffic_")
    private String             errorDiffic;                            // 错题难度
    @Indexed(name = "_reason_")
    private String             errorReason;                            // 错题原因
    @Indexed(name = "_degree_")
    private String             errorDegree;                            // 掌握程度
    @Indexed(name = "_source_")
    private String             errorSource;                            // 错题来源
    @Indexed(name = "_label_")
    private String             customLabel;                            // 自定义标签

    private List<String>       analysisList;                           // 题目解析的pic，最多3个

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public String getErrorDiffic() {
        return errorDiffic;
    }

    public void setErrorDiffic(String errorDiffic) {
        this.errorDiffic = errorDiffic;
    }

    public String getErrorReason() {
        return errorReason;
    }

    public void setErrorReason(String errorReason) {
        this.errorReason = errorReason;
    }

    public String getErrorDegree() {
        return errorDegree;
    }

    public void setErrorDegree(String errorDegree) {
        this.errorDegree = errorDegree;
    }

    public String getErrorSource() {
        return errorSource;
    }

    public void setErrorSource(String errorSource) {
        this.errorSource = errorSource;
    }

    public String getCustomLabel() {
        return customLabel;
    }

    public void setCustomLabel(String customLabel) {
        this.customLabel = customLabel;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLabelId() {
        return labelId;
    }

    public void setLabelId(String labelId) {
        this.labelId = labelId;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public List<String> getAnalysisList() {
        return analysisList;
    }

    public void setAnalysisList(List<String> analysisList) {
        this.analysisList = analysisList;
    }
}
