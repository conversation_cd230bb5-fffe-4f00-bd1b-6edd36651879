package net.xplife.system.community.entity.sq;
import java.util.List;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 已读记录
 */
@Document(collection = "V1_ReadRecord")
public class ReadRecord extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_ReadRecord";
    public static final String UC_ID_FIELD      = "ucId";
    public static final String DAY_FIELD        = "day";
    @Indexed(name = "_ucid_")
    private String             ucId;                              // 用户课程ID
    @Indexed(name = "_userid_")
    private String             userId;                            // 用户ID
    private int                wordCount;                         // 单词数
    @Indexed(name = "_day_")
    private int                day;                               // 第几天
    private List<String>       wordIds;                           // 单词集合

    public String getUcId() {
        return ucId;
    }

    public void setUcId(String ucId) {
        this.ucId = ucId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getWordCount() {
        return wordCount;
    }

    public void setWordCount(int wordCount) {
        this.wordCount = wordCount;
    }

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public List<String> getWordIds() {
        return wordIds;
    }

    public void setWordIds(List<String> wordIds) {
        this.wordIds = wordIds;
    }
}
