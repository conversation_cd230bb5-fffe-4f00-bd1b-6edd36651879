package net.xplife.system.community.entity.sq;
import java.util.List;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.sq.ReciteVo;
/**
 * 用户课程
 */
@Document(collection = "V1_UserCourse")
public class UserCourse extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_UserCourse";
    public static final String USER_ID_FIELD    = "userId";
    public static final String COURSE_ID_FIELD    = "courseId";
    @Indexed(name = "_userid_")
    private String             userId;                            // 用户ID
    private String             courseId;                          // 课程ID
    private int                dayCount;                          // 天数进度
    private int                planCount;                         // 计划数量
    private int                currCount;                         // 已背单词数量
    private int                totalCount;                        // 总数量
    private List<ReciteVo>     recites;                           // 背诵信息集合

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public int getDayCount() {
        return dayCount;
    }

    public void setDayCount(int dayCount) {
        this.dayCount = dayCount;
    }

    public int getPlanCount() {
        return planCount;
    }

    public void setPlanCount(int planCount) {
        this.planCount = planCount;
    }

    public int getCurrCount() {
        return currCount;
    }

    public void setCurrCount(int currCount) {
        this.currCount = currCount;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public List<ReciteVo> getRecites() {
        return recites;
    }

    public void setRecites(List<ReciteVo> recites) {
        this.recites = recites;
    }
}
