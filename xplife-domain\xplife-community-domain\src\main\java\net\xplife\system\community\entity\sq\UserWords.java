package net.xplife.system.community.entity.sq;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 用户生词本
 */
@Document(collection = "V1_UserWords")
public class UserWords extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_UserWords";
    public static final String USER_ID_FIELD    = "userId";
    public static final String FLAG_FIELD       = "flag";
    @Indexed(name = "_userid_")
    private String             userId;                           // 用户ID
    private String             wordsId;                          // 单词ID
    private String             name;                             // 单词名称
    private String             flag;                             // 单词flag

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getWordsId() {
        return wordsId;
    }

    public void setWordsId(String wordsId) {
        this.wordsId = wordsId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }
}
