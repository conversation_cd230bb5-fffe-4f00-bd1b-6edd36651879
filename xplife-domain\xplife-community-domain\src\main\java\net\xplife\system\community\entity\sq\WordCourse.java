package net.xplife.system.community.entity.sq;
import java.util.Map;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 词课程
 */
@Document(collection = "V1_WordCourse")
public class WordCourse extends IdEntity {
    private static final long   serialVersionUID = 1L;
    public static final String  COLL             = "V1_WordCourse";
    private String              typeId;                            // 词库ID
    private String              pId;                               // 父ID
    private String              typeName;                          // 类别名称
    private String              name;                              // 课程名称
    private String              subName;                           // 副类型课程名称
    private String              remark;                            // 备注
    private Map<String, String> pics;                              // 图片
    private int                 type;                              // 课程类型 0-有课程 1-无课程
    private int                 totalCount;                        // 单词总数
    private int                 sort;                              // 排序

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getpId() {
        return pId;
    }

    public void setpId(String pId) {
        this.pId = pId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    
    
    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Map<String, String> getPics() {
        return pics;
    }

    public void setPics(Map<String, String> pics) {
        this.pics = pics;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }
}
