package net.xplife.system.community.entity.sq;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 词库类型
 */
@Document(collection = "V1_WordType")
public class WordType extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_WordType";
    public static final String SORT_FIELD       = "sort";
    private String             name;                            // 名称
    private String             pId;                             // 父节点ID
    private int                sort;                            // 排序
    private int                type;                            // 词库类型 0不显示课程 1显示课程 2敬请期待

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getpId() {
        return pId;
    }

    public void setpId(String pId) {
        this.pId = pId;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
