package net.xplife.system.community.entity.sq;
import java.util.List;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.sq.PartVo;
import net.xplife.system.community.vo.sq.SymbolVo;
/**
 * 单词
 */
@Document(collection = "V1_Words")
public class Words extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_Words";
    public static final String NAME_FIELD       = "name";
    public static final String COURSE_ID_FIELD  = "courseId";
    @Indexed(name = "_courseid_")
    private String             courseId;                     // 课程ID
    @Indexed(name = "_name_")
    private String             name;                         // 单词名称
    private SymbolVo           symbolVo;                     // 音标
    private List<PartVo>       parts;                        // 释义
    private String             pronounce;                    // 发音

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public SymbolVo getSymbolVo() {
        return symbolVo;
    }

    public void setSymbolVo(SymbolVo symbolVo) {
        this.symbolVo = symbolVo;
    }

    public List<PartVo> getParts() {
        return parts;
    }

    public void setParts(List<PartVo> parts) {
        this.parts = parts;
    }

    public String getPronounce() {
        return pronounce;
    }

    public void setPronounce(String pronounce) {
        this.pronounce = pronounce;
    }
}
