package net.xplife.system.community.entity.sq;
import java.util.List;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.sq.PartVo;
import net.xplife.system.community.vo.sq.SymbolVo;
/**
 * 词库
 */
@Document(collection = "V1_WordsLibrary")
public class WordsLibrary extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_WordsLibrary";
    public static final String NAME_FIELD       = "name";
    public static final String FLAG_FIELD       = "flag";
    @Indexed(name = "_name_")
    private String             name;                                // 单词名称
    @Indexed(name = "_flag_")
    private String             flag;                                // 单词标识
    private SymbolVo           symbolVo;                            // 音标
    private List<PartVo>       parts;                               // 释义

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public SymbolVo getSymbolVo() {
        return symbolVo;
    }

    public void setSymbolVo(SymbolVo symbolVo) {
        this.symbolVo = symbolVo;
    }

    public List<PartVo> getParts() {
        return parts;
    }

    public void setParts(List<PartVo> parts) {
        this.parts = parts;
    }
}
