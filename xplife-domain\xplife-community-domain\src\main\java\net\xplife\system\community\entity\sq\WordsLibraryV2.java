package net.xplife.system.community.entity.sq;

import lombok.Getter;
import lombok.Setter;
import net.xplife.system.community.vo.sq.ExampleVo;
import net.xplife.system.community.vo.sq.PartVo;
import net.xplife.system.community.vo.sq.SymbolVo;
import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 词库
 */
@Document(collection = "V2_WordsLibrary")
@Getter
@Setter
public class WordsLibraryV2 extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V2_WordsLibrary";
    public static final String NAME_FIELD       = "name";
    public static final String FLAG_FIELD       = "flag";
    @Indexed(name = "_name_")
    private String             name;                                // 单词名称
    @Indexed(name = "_flag_")
    private String             flag;                                // 单词标识
    private String phonetic;                            // 音标
    private List<String> definitions;                               // 释义
    private String pronunciation;                    // 发音
    // 例句
    private List<ExampleVo> examples;

}
