package net.xplife.system.community.entity.sq;

import lombok.Getter;
import lombok.Setter;
import net.xplife.system.community.vo.sq.ExampleVo;
import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 单词
 */
@Document(collection = "V2_Words")
@Getter
@Setter
public class WordsV2 extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V2_Words";
    public static final String WORD_FIELD       = "word";
    public static final String COURSE_ID_FIELD  = "courseId";
    @Indexed(name = "_courseid_")
    private String             courseId;                     // 课程ID
    @Indexed(name = "_word_")
    private String word;                         // 单词名称
    private String           phonetic;                     // 音标
    private List<String>       definitions;                        // 释义
    private String pronunciation;                    // 发音
    // 例句
    private List<ExampleVo> examples;
}
