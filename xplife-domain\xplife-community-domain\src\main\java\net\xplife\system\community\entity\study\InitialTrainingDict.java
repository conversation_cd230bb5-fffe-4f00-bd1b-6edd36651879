package net.xplife.system.community.entity.study;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2024/12/27 08:57
 * @description：启蒙训练
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_InitialTrainingDict")
public class InitialTrainingDict extends IdEntity {
    private String  name;           // 名称
    private String  type;           // 类型
    private String  label;          // 标签
    private String  value;          // 值
    private String  localeCode;     // i18n的code值
    private String  icon;           // 图标地址
    private int  sortNum;        // 排序
    private String  remark;     //备注
    private int forbid;   // 是否禁止：0：否； 1：是

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLocaleCode() {
        return localeCode;
    }

    public void setLocaleCode(String localeCode) {
        this.localeCode = localeCode;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getForbid() {
        return forbid;
    }

    public void setForbid(int forbid) {
        this.forbid = forbid;
    }
}
