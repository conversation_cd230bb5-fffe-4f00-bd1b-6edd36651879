package net.xplife.system.community.entity.study;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

/**
 * <AUTHOR>
 * @date ：Created in 2024/12/27 08:57
 * @description：启蒙训练
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_InitialTrainingInfo")
public class InitialTrainingInfo extends IdEntity {
    private String resUrl;
    private String name;
    @Indexed(name = "_type_")
    private String type;
    private int sortNum;

    public String getResUrl() {
        return resUrl;
    }

    public void setResUrl(String resUrl) {
        this.resUrl = resUrl;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }
}
