package net.xplife.system.community.entity.study;

import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.feed.PicVo;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = "V1_StudyNote")
public class StudyNote extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_StudyNote";
    public static final String TITLE_FIELD      = "title";
    public static final String CONTENT_FIELD      = "content";
    public static final String SUBJECT_FIELD      = "subject";
    public static final String GRADE_FIELD      = "grade";
    @Indexed(name = "_userid_")
    private String             userId;                          // 用户ID
    @Indexed(name = "_title_")
    private String             title;                           // 标题
    @Indexed(name = "_content_")
    private String             content;                         // 内容
    private List<PicVo> pic;                                    // 图片
    @Indexed(name = "_grade_")
    private String             grade;                           // 年级
    @Indexed(name = "_subject_")
    private String             subject;                         // 科目
    @Indexed(name = "_haspic_")
    private int                hasPic;                          // 是否有图片
    private String             htmlUrl;                         // H5地址
    private int                stickNum;                        // 0:无； 1：精华帖； 999：置顶

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<PicVo> getPic() {
        return pic;
    }

    public void setPic(List<PicVo> pic) {
        this.pic = pic;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public int getHasPic() {
        return hasPic;
    }

    public void setHasPic(int hasPic) {
        this.hasPic = hasPic;
    }

    public String getHtmlUrl() {
        return htmlUrl;
    }

    public void setHtmlUrl(String htmlUrl) {
        this.htmlUrl = htmlUrl;
    }

    public int getStickNum() {
        return stickNum;
    }

    public void setStickNum(int stickNum) {
        this.stickNum = stickNum;
    }
}
