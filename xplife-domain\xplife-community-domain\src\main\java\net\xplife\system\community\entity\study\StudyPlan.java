package net.xplife.system.community.entity.study;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2024/12/5 08:56
 * @description：
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_StudyPlan")
public class StudyPlan extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_StudyPlan";
    public static final String USERID_FIELD      = "userId";
    @Indexed(name = "_userid_")
    private String userId;
    @Indexed(name = "_dayDate_")
    private String dayDate;
    private String dayTimeBegin;
    private String dayTimeEnd;
    private String content;
    private String planStatus;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDayDate() {
        return dayDate;
    }

    public void setDayDate(String dayDate) {
        this.dayDate = dayDate;
    }

    public String getDayTimeBegin() {
        return dayTimeBegin;
    }

    public void setDayTimeBegin(String dayTimeBegin) {
        this.dayTimeBegin = dayTimeBegin;
    }

    public String getDayTimeEnd() {
        return dayTimeEnd;
    }

    public void setDayTimeEnd(String dayTimeEnd) {
        this.dayTimeEnd = dayTimeEnd;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPlanStatus() {
        return planStatus;
    }

    public void setPlanStatus(String planStatus) {
        this.planStatus = planStatus;
    }
}
