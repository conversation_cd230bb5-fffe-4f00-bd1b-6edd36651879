package net.xplife.system.community.entity.system;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2024/1/11 10:34
 * @description：app中关于打印的功能展示设定
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_FunctionsSetting")
public class FunctionsSetting  extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_FunctionsSetting";
    private String              code;                      // 功能编码
    private String              name;                      // 功能名称
    private String              printerTypes;              // 所属打印机
    private String              params;                    // 参数
    private int                 newFlag;                   // 是否展示新
    private int                 sortNum;                   // 排序
    private String              type;                      // 功能类型

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrinterTypes() {
        return printerTypes;
    }

    public void setPrinterTypes(String printerTypes) {
        this.printerTypes = printerTypes;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public int getNewFlag() {
        return newFlag;
    }

    public void setNewFlag(int newFlag) {
        this.newFlag = newFlag;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }
}
