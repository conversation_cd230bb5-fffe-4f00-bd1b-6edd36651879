package net.xplife.system.community.entity.system;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/6 8:51
 * @description： 打印机驱动更新
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_PrintDriverUpdateInfo")
public class PrintDriverUpdateInfo extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_PrintDriverUpdateInfo";
    @Indexed(name = "_printerModel_")
    private String             printerModel;                      // 打印机型号
    private String             versionName;                       // 固件名称
    private String             versionCode;                       // 版本号编码
    private String             url;                               // 下载地址
    private String             param;                             // 参数
    private String             remark;                            // 版本描述
    private String             title;                             // 标题
    private int                needForceUpdate;                   // 是否强制升级
    private int                needIndexShow;                     // 是否首页显示
    private int                stopFlag;                          // 是否停用，不生效的不返回到前端 0：否，1：是
    private String             preDriverId;                       // 前置升级的固件id
    private int                needCheckPower;                    // 0: 不用，1：要
    private int                powerValue;                        // 符合升级的电量,0--100
    private String             md5;                               // md5码
    private int                isBaseVersion;                     // 是否重要基础版本  0:否， 1：是
    private int                followStatus;                      // 增加流程状态

    public int getStopFlag() {
        return stopFlag;
    }

    public void setStopFlag(int stopFlag) {
        this.stopFlag = stopFlag;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getNeedForceUpdate() {
        return needForceUpdate;
    }

    public void setNeedForceUpdate(int needForceUpdate) {
        this.needForceUpdate = needForceUpdate;
    }

    public int getNeedIndexShow() {
        return needIndexShow;
    }

    public void setNeedIndexShow(int needIndexShow) {
        this.needIndexShow = needIndexShow;
    }

    public String getPrinterModel() {
        return printerModel;
    }

    public void setPrinterModel(String printerModel) {
        this.printerModel = printerModel;
    }

    public String getPreDriverId() {
        return preDriverId;
    }

    public void setPreDriverId(String preDriverId) {
        this.preDriverId = preDriverId;
    }

    public int getNeedCheckPower() {
        return needCheckPower;
    }

    public void setNeedCheckPower(int needCheckPower) {
        this.needCheckPower = needCheckPower;
    }

    public int getPowerValue() {
        return powerValue;
    }

    public void setPowerValue(int powerValue) {
        this.powerValue = powerValue;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public int getIsBaseVersion() {
        return isBaseVersion;
    }

    public void setIsBaseVersion(int isBaseVersion) {
        this.isBaseVersion = isBaseVersion;
    }

    public int getFollowStatus() {
        return followStatus;
    }

    public void setFollowStatus(int followStatus) {
        this.followStatus = followStatus;
    }
}
