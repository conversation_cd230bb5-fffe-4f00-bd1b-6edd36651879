package net.xplife.system.community.entity.system;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * 打印里程
 */
@Document(collection = "V1_PrintMileage")
public class PrintMileage extends IdEntity {

    private static final long  serialVersionUID = 1L;
    public static final String COLL = "V1_PrintMileage";

    public static final String USER_ID_FIELD = "userId";

    /**
     * 用户ID
     */
    @Indexed(name = "_userid_")
    private String userId;
    /**
     * 按天打印长度
     */
    private Map<String, Double> dayMileMap;
    /**
     * 按月打印长度
     */
    private Map<String, Double> monthMileMap;
    /**
     * 按年打印长度
     */
    private Map<String, Double> yearMileMap;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Map<String, Double> getDayMileMap() {
        return dayMileMap;
    }

    public void setDayMileMap(Map<String, Double> dayMileMap) {
        this.dayMileMap = dayMileMap;
    }

    public Map<String, Double> getMonthMileMap() {
        return monthMileMap;
    }

    public void setMonthMileMap(Map<String, Double> monthMileMap) {
        this.monthMileMap = monthMileMap;
    }

    public Map<String, Double> getYearMileMap() {
        return yearMileMap;
    }

    public void setYearMileMap(Map<String, Double> yearMileMap) {
        this.yearMileMap = yearMileMap;
    }
}