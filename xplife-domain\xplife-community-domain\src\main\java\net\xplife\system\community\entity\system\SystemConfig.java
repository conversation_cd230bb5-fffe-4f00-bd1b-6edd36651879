package net.xplife.system.community.entity.system;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 系统配置
 */
@Document(collection = "V1_SystemConfig")
public class SystemConfig extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_SystemConfig";
    private int                type;                                // 类型
    private String             key;                                 // key名称
    private Object             value;                               // 值
    private String             remark;                              // 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }
}
