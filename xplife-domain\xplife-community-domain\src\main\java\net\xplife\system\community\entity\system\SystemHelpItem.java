package net.xplife.system.community.entity.system;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 系统帮助项
 */
@Document(collection = "V1_SystemHelpItem")
public class SystemHelpItem extends IdEntity {

    private static final long  serialVersionUID = 1L;
    public static final String COLL = "V1_SystemHelpItem";
    public static final String COLL_TYPE       = "type";

    /**
     * 标题
     */
    private String title;

    /***
     * 国际化编码
     */
    private String localLanguageCode;

    /**
     * 链接地址
     */
    private String url;
    /***
     * 封面地址
     */
    private String coverUrl;

    private int overseas;
    /**
     * 帮助文档的类型
     */
    @Indexed(name = "_type_")
    private String type;

    private int sortNum;

    /**
     * 打印机类型
     */
    private String printerType;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getOverseas() {
        return overseas;
    }

    public void setOverseas(int overseas) {
        this.overseas = overseas;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getLocalLanguageCode() {
        return localLanguageCode;
    }

    public void setLocalLanguageCode(String localLanguageCode) {
        this.localLanguageCode = localLanguageCode;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }
}
