package net.xplife.system.community.entity.system;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * (XColor应用)系统帮助项
 */
@Document(collection = "V1_SystemHelpItemX")
public class SystemHelpItemX extends IdEntity {

    private static final long  serialVersionUID = 1L;
    public static final String COLL = "V1_SystemHelpItemX";

    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

}