package net.xplife.system.community.entity.system;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 版本更新信息
 */
@Document(collection = "V1_UpdateInfo")
public class UpdateInfo extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_UpdateInfo";
    private String             channel;                           // 渠道
    private String             version;                           // 版本号
    private String             url;                               // 下载地址
    private String             param;                             // 参数
    private String             remark;                            // 版本描述
    private String             title;                             // 标题
    private int                needForceUpdate;                   // 是否强制升级
    private int                needIndexShow;                     // 是否首页显示
    private String             type;                 // 类型：app：安装包；printer：打印机固件

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getNeedForceUpdate() {
        return needForceUpdate;
    }

    public void setNeedForceUpdate(int needForceUpdate) {
        this.needForceUpdate = needForceUpdate;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getNeedIndexShow() {
        return needIndexShow;
    }

    public void setNeedIndexShow(int needIndexShow) {
        this.needIndexShow = needIndexShow;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
