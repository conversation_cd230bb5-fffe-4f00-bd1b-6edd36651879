package net.xplife.system.community.entity.system;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 用户反馈信息类
 */
@Document(collection = "V1_UserFeedback")
public class UserFeedback extends IdEntity {

    private static final long  serialVersionUID = 1L;
    public static final String COLL = "V1_UserFeedback";
    public static final String USER_ID_FIELD = "userId";

    /**
     * 用户ID
     */
    @Indexed(name = "_userid_")
    private String userId;

    /**
     * 反馈类型
     */
    private String type;
    /**
     * 问题类型
     */
    private String qtype;
    /**
     * 反馈文本内容
     */
    private String content;
    /**
     * 反馈图片地址列表
     */
    private List<String> images;
    /**
     * 系统响应结果信息
     */
    private String result;
    /**
     * 错误信息
     */
    private String errormsg;
    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户编号
     */
    private Integer userNo;
    /**
     * 客户端信息
     */
    private String clientInfo;

    /***
     * 打印机类型
     */
    private String printerType;
    /***
     * 应用名
     */
    private String appName;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getQtype() {
        return qtype;
    }

    public void setQtype(String qtype) {
        this.qtype = qtype;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getErrormsg() {
        return errormsg;
    }

    public void setErrormsg(String errormsg) {
        this.errormsg = errormsg;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getUserNo() {
        return userNo;
    }

    public void setUserNo(Integer userNo) {
        this.userNo = userNo;
    }

    public String getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(String clientInfo) {
        this.clientInfo = clientInfo;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }
}
