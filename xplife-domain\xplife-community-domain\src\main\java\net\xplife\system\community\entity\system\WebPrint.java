package net.xplife.system.community.entity.system;

import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.community.vo.system.WebPagePrintVo;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 网页打印
 */
@Document(collection = "V1_WebPrint")
public class WebPrint extends IdEntity {

    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_WebPrint";
    public static final String USER_ID_FIELD    = "userId";
    public static final String IS_DEFAULT_FIELD = "isDefault";

    /**
     * 用户ID
     */
    @Indexed(name = "_userid_")
    private String userId;
    /**
     * 分组名称
     */
    private String name;
    /**
     * 是否默认
     */
    private int isDefault;
    /**
     * 来自默认
     */
    private int fromDefault;
    /**
     * 我的网站集合
     */
    private List<WebPagePrintVo> pageList;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(int isDefault) {
        this.isDefault = isDefault;
    }

    public int getFromDefault() {
        return fromDefault;
    }

    public void setFromDefault(int fromDefault) {
        this.fromDefault = fromDefault;
    }

    public List<WebPagePrintVo> getPageList() {
        return pageList;
    }

    public void setPageList(List<WebPagePrintVo> pageList) {
        this.pageList = pageList;
    }

}