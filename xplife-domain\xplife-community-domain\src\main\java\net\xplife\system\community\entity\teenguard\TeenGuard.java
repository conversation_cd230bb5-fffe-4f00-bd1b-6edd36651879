package net.xplife.system.community.entity.teenguard;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2022/1/6 16:32
 * @description：青少年保护
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_TeenGuard")
public class TeenGuard extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_TeenGuard";
    public static final String USER_ID_FIELD    = "userId";

    /**
     * 用户ID
     */
    @Indexed(name = "_userid_")
    private String userId;
    private String realName;
    private String idCard;
    private String password;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
