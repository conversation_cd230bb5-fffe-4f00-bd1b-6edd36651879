package net.xplife.system.community.entity.user;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 课程表信息
 */
@Document(collection = "V1_ClassSchedule")
public class ClassSchedule extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_ClassSchedule";
    public static final String USER_ID_FIELD    = "userId";
    public static final String NAME_FIELD       = "name";
    @Indexed(name = "_userid_")
    private String             userId;                               // 用户ID
    @Indexed(name = "_name_")
    private String             name;                                 // 课程名称
    private int                sort;                                 // 排序

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }
}
