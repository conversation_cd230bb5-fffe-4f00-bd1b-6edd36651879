package net.xplife.system.community.entity.user;

import net.xplife.system.community.vo.sq.LabelVo;
import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 用户统计信息
 */
@Document(collection = "V2_UserStatistics")
public class UserStatisticsV2 extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V2_UserStatistics";
    public static final String USER_ID_FIELD    = "userId";
    @Indexed(name = "_userid_")
    private String             userId;                                // 用户ID
    private int                errorBookFlag;                         // 错题库标识
    private List<LabelVo>      labelList;                             // 标签目录集合
    private List<LabelVo>      errorType;                             // 错题类型
    private List<LabelVo>      errorReason;                           // 错题原因
    private List<LabelVo>      errorSource;                           // 错题来源
    private List<LabelVo>      customLabel;                           // 自定义标签
    private List<String>       courseIds;                             // 已完成课程ID集合

    public List<String> getCourseIds() {
        return courseIds;
    }

    public void setCourseIds(List<String> courseIds) {
        this.courseIds = courseIds;
    }

    public List<LabelVo> getErrorType() {
        return errorType;
    }

    public void setErrorType(List<LabelVo> errorType) {
        this.errorType = errorType;
    }

    public List<LabelVo> getErrorReason() {
        return errorReason;
    }

    public void setErrorReason(List<LabelVo> errorReason) {
        this.errorReason = errorReason;
    }

    public List<LabelVo> getErrorSource() {
        return errorSource;
    }

    public void setErrorSource(List<LabelVo> errorSource) {
        this.errorSource = errorSource;
    }

    public List<LabelVo> getCustomLabel() {
        return customLabel;
    }

    public void setCustomLabel(List<LabelVo> customLabel) {
        this.customLabel = customLabel;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getErrorBookFlag() {
        return errorBookFlag;
    }

    public void setErrorBookFlag(int errorBookFlag) {
        this.errorBookFlag = errorBookFlag;
    }

    public List<LabelVo> getLabelList() {
        return labelList;
    }

    public void setLabelList(List<LabelVo> labelList) {
        this.labelList = labelList;
    }
}
