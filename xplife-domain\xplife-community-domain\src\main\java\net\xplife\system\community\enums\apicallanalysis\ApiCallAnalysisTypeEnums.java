package net.xplife.system.community.enums.apicallanalysis;

import java.util.LinkedHashMap;

/**
 * 记录调用次数的接口类型
 */
public enum ApiCallAnalysisTypeEnums {
    QQ_ORC("qqOrc", "腾讯语音识别"),
    AFT("aft","阿凡提识别"),
    QY_ORC("qyOrc","箐优网的拍题搜题"),
    OTHER("other", "其他");
    private final String                                type;
    private final String                                desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<>();
        for (ApiCallAnalysisTypeEnums draftsTypeEnums : ApiCallAnalysisTypeEnums.values()) {
            map.put(draftsTypeEnums.getType(), draftsTypeEnums.getDesc());
        }
    }

    ApiCallAnalysisTypeEnums(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
