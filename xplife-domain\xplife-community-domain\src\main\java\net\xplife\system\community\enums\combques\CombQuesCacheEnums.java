package net.xplife.system.community.enums.combques;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum CombQuesCacheEnums implements ICacheEnums {
    COMBQUES_BY_ID("comm:combques:by:id:", "组卷历史对象"),
    COMBQUES_temp_BY_USERID_SUBJECT("comm:combques:temp:by:%s:%s", "临时组卷对象-用户和subject"),
    COMBQUES_ID_LIST("comm:combques:by:list:", "组卷历史数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (CombQuesCacheEnums combQuesCacheEnums : CombQuesCacheEnums.values()) {
            map.put(combQuesCacheEnums.getKey(), combQuesCacheEnums.getDesc());
        }
    }

    private CombQuesCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
