package net.xplife.system.community.enums.drafts;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum DraftsCacheEnums implements ICacheEnums {
    DRAFTS_BY_ID("comm:dra:by:id:", "草稿箱对象"), 
    DRAFTS_ID_LIST("comm:dra:by:list:", "草稿箱数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (DraftsCacheEnums draftsCacheEnums : DraftsCacheEnums.values()) {
            map.put(draftsCacheEnums.getKey(), draftsCacheEnums.getDesc());
        }
    } 

    private DraftsCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
