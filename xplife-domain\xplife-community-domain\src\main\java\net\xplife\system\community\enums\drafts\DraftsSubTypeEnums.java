package net.xplife.system.community.enums.drafts;
import java.util.LinkedHashMap;
/**
 * 草稿箱副类型
 */
public enum DraftsSubTypeEnums {
    EDIT(0, "编辑纸条"), 
    NOTE(1, "清单、便利贴"), 
    BANNER(3, "大字横幅");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (DraftsSubTypeEnums draftsSubTypeEnums : DraftsSubTypeEnums.values()) {
            map.put(draftsSubTypeEnums.getValue(), draftsSubTypeEnums.getDesc());
        }
    }

    DraftsSubTypeEnums(Integer value, String desc) {
        this.value = value; 
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
