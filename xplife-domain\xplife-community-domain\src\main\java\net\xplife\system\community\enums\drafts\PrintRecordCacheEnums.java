package net.xplife.system.community.enums.drafts;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum PrintRecordCacheEnums implements ICacheEnums {
    PRINT_RECORD_BY_ID("comm:prre:by:id:", "打印记录对象"), 
    PRINT_RECORD_ID_LIST("comm:prre:by:list:", "打印记录数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (PrintRecordCacheEnums printRecordCacheEnums : PrintRecordCacheEnums.values()) {
            map.put(printRecordCacheEnums.getKey(), printRecordCacheEnums.getDesc());
        }
    }  
 
    private PrintRecordCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
