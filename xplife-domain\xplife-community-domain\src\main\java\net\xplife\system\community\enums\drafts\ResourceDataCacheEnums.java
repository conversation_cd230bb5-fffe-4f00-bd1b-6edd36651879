package net.xplife.system.community.enums.drafts;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum ResourceDataCacheEnums implements ICacheEnums {
    RESOURCE_DATA_BY_ID("comm:res:da:by:id:", "资源对象");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (ResourceDataCacheEnums resourceDataCacheEnums : ResourceDataCacheEnums.values()) {
            map.put(resourceDataCacheEnums.getKey(), resourceDataCacheEnums.getDesc());
        }
    }

    private ResourceDataCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
