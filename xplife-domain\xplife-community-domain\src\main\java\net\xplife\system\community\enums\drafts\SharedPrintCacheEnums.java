package net.xplife.system.community.enums.drafts;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum SharedPrintCacheEnums implements ICacheEnums {
    SHARED_PRINT_BY_ID("comm:shpr:by:id:", "共享打印对象"), 
    SHARED_PRINT_ID_LIST("comm:shpr:by:list:", "共享打印数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (SharedPrintCacheEnums sharedPrintCacheEnums : SharedPrintCacheEnums.values()) {
            map.put(sharedPrintCacheEnums.getKey(), sharedPrintCacheEnums.getDesc());
        }
    }  

    private SharedPrintCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
