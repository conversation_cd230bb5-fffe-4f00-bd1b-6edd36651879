package net.xplife.system.community.enums.drafts;
import java.util.LinkedHashMap;
/**
 * 共享打印来源
 */
public enum SharedPrintFromEnums {
    WX("wx", "微信","收到微信好友的共享纸条","/api/img/gam/20180827185191/common_ic_nocicewecon.png"), 
    M<PERSON>("mom", "朋友圈","收到朋友圈好友的共享纸条","/api/img/gam/20180827185191/common_ic_noticeweconcircle.png"), 
    QQ("qq", "QQ","收到QQ好友的共享纸条","/api/img/gam/20180827185191/common_ic_noticeqq.png"), 
    SINA("sina", "新浪","收到新浪微博好友的共享纸条","/api/img/gam/20180827185191/common_ic_noticeweibo.png");
    private final String                               value;
    private final String                               desc;
    private final String                               content;
    private final String                               icon;
    private static final LinkedHashMap<String, SharedPrintFromEnums> map;
    static {
        map = new LinkedHashMap<>();
        for (SharedPrintFromEnums sharedPrintFromEnums : SharedPrintFromEnums.values()) {
            map.put(sharedPrintFromEnums.getValue(), sharedPrintFromEnums);
        }
    }

    SharedPrintFromEnums(String value, String desc,String content,String icon) {
        this.value = value;
        this.desc = desc;
        this.content=content;
        this.icon=icon;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public String getContent() {
        return content;
    }

    public String getIcon() {
        return icon;
    }

    public static LinkedHashMap<String, SharedPrintFromEnums> getMap() {
        return map;
    }
}
