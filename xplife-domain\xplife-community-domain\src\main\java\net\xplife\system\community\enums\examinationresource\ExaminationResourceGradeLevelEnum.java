package net.xplife.system.community.enums.examinationresource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum ExaminationResourceGradeLevelEnum {
    gaozhong1("高一", "10","gaozhong"),
    gaozhong2("高二", "11","gaozhong"),
    gaozhong3("高三", "12","gaozhong"),
    chuzhong1("初一", "07","chuzhong"),
    chuzhong2("初二", "08","chuzhong"),
    chuzhong3("初三", "09","chuzhong"),
    xiaoxue1("一年级", "01","xiaoxue"),
    xiaoxue2("二年级", "02","xiaoxue"),
    xiaoxue3("三年级", "03","xiaoxue"),
    xiaoxue4("四年级", "04","xiaoxue"),
    xiaoxue5("五年级","05","xiaoxue"),
    xiaoxue6("六年级","06","xiaoxue"),;
    private final String                                value;
    private final String                                key;
    private final String                                keyword;

    ExaminationResourceGradeLevelEnum(String value, String key, String keyword) {
        this.value = value;
        this.key = key;
        this.keyword = keyword;
    }

    public String getValue() {
        return value;
    }

    public String getKey() {
        return key;
    }

    public String getKeyword() {
        return keyword;
    }

    public static List<Map<String, String>> getList(String keyword){
        List<Map<String, String>> rtn = new ArrayList<>();
        for (ExaminationResourceGradeLevelEnum enumObj: ExaminationResourceGradeLevelEnum.values()) {
            if (enumObj.getKeyword().equals(keyword)){
                Map<String, String> temp = new HashMap<>();
                temp.put("id", enumObj.getKey());
                temp.put("name", enumObj.getValue());
                rtn.add(temp);
            }
        }
        return rtn;
    }

    public static List<Map<String, Object>> getListMap(){
        List<Map<String, Object>> rtn = new ArrayList<>();

        Map<String, Object> all = new HashMap<>();
        all.put("name","全部年级");
        all.put("id","");
        all.put("children", new ArrayList<>());
        rtn.add(all);

        Map<String, Object> gaozhong = new HashMap<>();
        gaozhong.put("id", "gaozhong");
        gaozhong.put("name", "高中");
        gaozhong.put("children", ExaminationResourceGradeLevelEnum.getList("gaozhong"));
        rtn.add(gaozhong);

        Map<String, Object> chuzhong = new HashMap<>();
        chuzhong.put("id", "chuzhong");
        chuzhong.put("name", "初中");
        chuzhong.put("children", ExaminationResourceGradeLevelEnum.getList("chuzhong"));
        rtn.add(chuzhong);

        Map<String, Object> xiaoxue = new HashMap<>();
        xiaoxue.put("id", "xiaoxue");
        xiaoxue.put("name", "小学");
        xiaoxue.put("children", ExaminationResourceGradeLevelEnum.getList("xiaoxue"));
        rtn.add(xiaoxue);

        return rtn;
    }
}
