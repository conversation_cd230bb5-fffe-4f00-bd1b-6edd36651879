package net.xplife.system.community.enums.examinationresource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum ExaminationResourceSubjectEnum {
    YW("语文", "Chinese","xx,cz,gz"),
    SX("数学", "Mathematics","xx,cz,gz"),
    YY("英语", "English","xx,czgz,gz"),
    SW("生物", "Biology","cz,"),
    ZZ("政治", "Politics","gz"),
    LS("历史", "History","cz,gz"),
    DL("地理", "Geography","cz,gz"),
    WL("物理", "Physics","cz,gz"),
    HX("化学", "Chemistry","cz,gz"),
    DF("道德与法制", "Daofa","cz"),
    KX("科学","Science","kx"),;
    private final String                                value;
    private final String                                key;
    private final String                                keyword;

    ExaminationResourceSubjectEnum(String value, String key, String keyword) {
        this.value = value;
        this.key = key;
        this.keyword = keyword;
    }

    public String getValue() {
        return value;
    }

    public String getKey() {
        return key;
    }

    public String getKeyword() {
        return keyword;
    }

    public static List<Map<String, String>> getList(){
        List<Map<String, String>> rtn = new ArrayList<>();

        Map<String, String> all = new HashMap<>();
        all.put("name","全部学科");
        all.put("id","");
        rtn.add(all);

        for (ExaminationResourceSubjectEnum enumObj: ExaminationResourceSubjectEnum.values()) {
            Map<String, String> temp = new HashMap<>();
            temp.put("id", enumObj.getKey());
            temp.put("name", enumObj.getValue());
            rtn.add(temp);
        }
        return rtn;
    }
}
