package net.xplife.system.community.enums.examinationresource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum ExaminationResourceTypeEnum {
    YUEKAO("月考试卷", "yuekao"),
    QIZHONG("期中考试", "qizhong"),
    QIMO("期末开始", "qimo"),
    XIAOSHEGNCHU("小升初复习", "xiaoshengchu"),
    ZHOGNKAO("中考复习", "zhongkao"),
    GAOKAO("高考复习", "gaokao"),
    OTHER("其他", "other");
    private final String                                value;
    private final String                                key;

    ExaminationResourceTypeEnum(String value, String key) {
        this.value = value;
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public String getKey() {
        return key;
    }

    public static List<Map<String, String>> getList(){
        List<Map<String, String>> rtn = new ArrayList<>();

        Map<String, String> all = new HashMap<>();
        all.put("name","全部类型");
        all.put("id","");
        rtn.add(all);

        for (ExaminationResourceTypeEnum enumObj: ExaminationResourceTypeEnum.values()) {
            Map<String, String> temp = new HashMap<>();
            temp.put("id", enumObj.getKey());
            temp.put("name", enumObj.getValue());
            rtn.add(temp);
        }
        return rtn;
    }
}
