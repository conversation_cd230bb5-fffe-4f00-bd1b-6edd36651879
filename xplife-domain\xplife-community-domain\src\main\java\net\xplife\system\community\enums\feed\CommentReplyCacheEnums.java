package net.xplife.system.community.enums.feed;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum CommentReplyCacheEnums implements ICacheEnums {
    COMMENT_REPLY_BY_ID("comm:comm:rep:by:id:", "评论回复记录"),
    COMMENT_REPLY_BY_COUNT("comm:comm:rep:by:count:", "评论回复数"),
    COMMENT_REPLY_ID_LIST("comm:comm:rep:by:list:", "评论回复数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (CommentReplyCacheEnums commentReplyCacheEnums : CommentReplyCacheEnums.values()) {
            map.put(commentReplyCacheEnums.getKey(), commentReplyCacheEnums.getDesc());
        }
    } 
 
    private CommentReplyCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
