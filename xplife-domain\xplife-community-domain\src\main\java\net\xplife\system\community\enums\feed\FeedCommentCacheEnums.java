package net.xplife.system.community.enums.feed;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum FeedCommentCacheEnums implements ICacheEnums {
    FEED_COMMENT_BY_ID("comm:feed:comm:by:id:", "动态评论记录"),
    FEED_COMMENT_ID_LIST("comm:feed:comm:by:list:", "动态评论数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (FeedCommentCacheEnums feedCommentCacheEnums : FeedCommentCacheEnums.values()) {
            map.put(feedCommentCacheEnums.getKey(), feedCommentCacheEnums.getDesc());
        }
    }
 
    private FeedCommentCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
