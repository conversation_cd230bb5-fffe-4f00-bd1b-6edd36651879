package net.xplife.system.community.enums.feed;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum FeedNoteCacheEnums implements ICacheEnums {
    FEED_NOTE_BY_ID("comm:feed:by:id:", "动态记录"),
    FEED_NOTE_FOR_STICK_TOP("comm:feed:stick:top", "置顶记录"),
    FEED_NOTE_FOR_STICK_ESSENCE("comm:feed:stick:essence", "精华记录");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (FeedNoteCacheEnums feedNoteCacheEnums : FeedNoteCacheEnums.values()) {
            map.put(feedNoteCacheEnums.getKey(), feedNoteCacheEnums.getDesc());
        }
    }

    private FeedNoteCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
