package net.xplife.system.community.enums.feed;

import java.util.LinkedHashMap;

public enum FeedNoteStickEnums {
    NONE(0, "无任标志", ""),
    ESSENCE(1, "精华帖", "api/img/gam/common/stick/group_ic_good.png"),
    TOP(999, "置顶", "api/img/gam/common/stick/group_ic_top.png");
    private final int                               key;
    private final String                            desc;
    private final String                            icon;
    private static final LinkedHashMap<Integer, FeedNoteStickEnums> map;
    static {
        map = new LinkedHashMap<Integer, FeedNoteStickEnums>();
        for (FeedNoteStickEnums feedNoteStickEnums : FeedNoteStickEnums.values()) {
            map.put(feedNoteStickEnums.getKey(), feedNoteStickEnums);
        }
    }

    private FeedNoteStickEnums(int key, String desc, String icon) {
        this.key = key;
        this.desc = desc;
        this.icon = icon;
    }

    public int getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public String getIcon() {
        return icon;
    }

    public static LinkedHashMap<Integer, FeedNoteStickEnums> getMap() {
        return map;
    }
}
