package net.xplife.system.community.enums.feed;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum FeedStatisticsCacheEnums implements ICacheEnums {
    FEED_STATISTICS_BY_FEED_ID("comm:feed:sta:by:fid:", "动态统计记录"),
    FEED_STATISTICS_COUNT_NUM("comm:feed:sta:co:num:", "动态操作数量统计");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (FeedStatisticsCacheEnums feedStatisticsCacheEnums : FeedStatisticsCacheEnums.values()) {
            map.put(feedStatisticsCacheEnums.getKey(), feedStatisticsCacheEnums.getDesc());
        }
    }

    private FeedStatisticsCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
