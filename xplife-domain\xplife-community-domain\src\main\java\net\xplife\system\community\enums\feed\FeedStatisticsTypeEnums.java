package net.xplife.system.community.enums.feed;
import java.util.LinkedHashMap;
/**
 * 
 *
 */
public enum FeedStatisticsTypeEnums {
    LIKE(0, "收藏数量", "getLikeVo", "setLikeVo"),
    DOWNLOAD(1, "下载数量", "getDownLoadVo", "setDownLoadVo"), 
    PRINT(2, "打印数量", "getPrintVo","setPrintVo"), 
    COMM(3, "评论数量", "getCommentNum", "setCommentNum"),
    GIVELIKE(4, "点赞数量", "getGiveLikeNum", "setGiveLikeNum"),
    SHARE(5, "分享数量", "getShareNum", "setShareNum");
    private final int                                   value;
    private final String                                desc;
    private final String                                getMethod;
    private final String                                setMethod;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (FeedStatisticsTypeEnums feedStatisticsTypeEnums : FeedStatisticsTypeEnums.values()) {
            map.put(feedStatisticsTypeEnums.getValue(), feedStatisticsTypeEnums.getDesc());
        }
    }

    FeedStatisticsTypeEnums(Integer value, String desc, String getMethod, String setMethod) {
        this.value = value;
        this.desc = desc;
        this.getMethod = getMethod;
        this.setMethod = setMethod;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public String getMethod() {
        return getMethod;
    }

    public String setMethod() {
        return setMethod;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
