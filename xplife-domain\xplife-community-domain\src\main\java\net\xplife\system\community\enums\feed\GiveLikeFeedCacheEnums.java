package net.xplife.system.community.enums.feed;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum GiveLikeFeedCacheEnums implements ICacheEnums {
    GIVE_LIKE_FEED_BY_ID("comm:gi:li:fe:by:id:", "点赞的动态对象"),
    GIVE_LIKE_FEED_BY_FLAG("comm:gi:li:fe:by:flag:", "点赞的动态对象"),
    GIVE_LIKE_FEED_RECORD_UID_LIST("comm:gi:li:fe:re:uid:list:", "点赞的用户集合"),
    GIVE_LIKE_FEED_ID_LIST("comm:gi:li:fe:by:list:", "点赞的动态数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (GiveLikeFeedCacheEnums giveLikeFeedCacheEnums : GiveLikeFeedCacheEnums.values()) {
            map.put(giveLikeFeedCacheEnums.getKey(), giveLikeFeedCacheEnums.getDesc());
        }
    }

    private GiveLikeFeedCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
