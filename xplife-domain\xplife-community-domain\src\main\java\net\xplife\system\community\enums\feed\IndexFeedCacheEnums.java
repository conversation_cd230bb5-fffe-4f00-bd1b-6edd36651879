package net.xplife.system.community.enums.feed;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum IndexFeedCacheEnums implements ICacheEnums {
    INDEX_FEED_BY_ID("comm:in:fe:by:id:", "首页动态数据对象"),
    INDEX_FEED_BY_TYPE_ID("comm:in:fe:by:type:id:", "首页动态数据对象"),
    INDEX_FEED_ID_LIST("comm:in:fe:by:list:", "首页动态数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (IndexFeedCacheEnums indexFeedCacheEnums : IndexFeedCacheEnums.values()) {
            map.put(indexFeedCacheEnums.getKey(), indexFeedCacheEnums.getDesc());
        }
    }

    private IndexFeedCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
