package net.xplife.system.community.enums.feed;
import java.util.LinkedHashMap;
/**
 * 
 *
 */
public enum IndexFeedTypeEnums {
    NEW(0, "最新"), 
    HOT(1, "热门");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (IndexFeedTypeEnums indexFeedTypeEnums : IndexFeedTypeEnums.values()) {
            map.put(indexFeedTypeEnums.getValue(), indexFeedTypeEnums.getDesc());
        }
    }

    IndexFeedTypeEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
