package net.xplife.system.community.enums.feed;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum LikeFeedCacheEnums implements ICacheEnums {
    LIKE_FEED_BY_ID("comm:li:fe:by:id:", "喜欢的动态对象"), 
    LIKE_FEED_BY_FLAG("comm:li:fe:by:flag:", "喜欢的动态对象"), 
    LIKE_FEED_RECORD_UID_LIST("comm:li:fe:re:uid:list:", "喜欢的用户集合"), 
    LIKE_FEED_ID_LIST("comm:li:fe:by:list:", "喜欢的动态数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (LikeFeedCacheEnums likeFeedCacheEnums : LikeFeedCacheEnums.values()) {
            map.put(likeFeedCacheEnums.getKey(), likeFeedCacheEnums.getDesc());
        }
    }

    private LikeFeedCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
