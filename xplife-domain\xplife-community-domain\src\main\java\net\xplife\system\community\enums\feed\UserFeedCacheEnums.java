package net.xplife.system.community.enums.feed;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum UserFeedCacheEnums implements ICacheEnums {
    USER_FEED_BY_ID("comm:us:fe:by:id:", "我的上传数据对象"), 
    USER_FEED_BY_UF_ID("comm:us:fe:by:ufid:", "我的上传数据对象"), 
    USER_FEED_ID_LIST("comm:us:fe:by:list:", "我的上传数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserFeedCacheEnums userFeedCacheEnums : UserFeedCacheEnums.values()) {
            map.put(userFeedCacheEnums.getKey(), userFeedCacheEnums.getDesc());
        }
    }

    private UserFeedCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
