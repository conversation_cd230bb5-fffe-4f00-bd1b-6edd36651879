package net.xplife.system.community.enums.homework;

import java.util.LinkedHashMap;

/**
 * 作业科目
 */
public enum HomeWorkSubjectEnums {
    YW("语文", "Chinese","api/img/gam/common/question3/print_ic_Chinese.png"),
    SX("数学", "Mathematics","api/img/gam/common/question3/print_ic_Mathematics.png"),
    YY("英语", "English","api/img/gam/common/question3/print_ic_English.png"),
    WL("物理", "Physics","api/img/gam/common/question3/print_ic_Physics.png"),
    KX("科学", "Science","api/img/gam/common/question3/print_ic_Science.png"),
    DL("地理", "Geography","api/img/gam/common/question3/print_ic_Physics.png"),
    HX("化学", "Chemistry","api/img/gam/common/question3/print_ic_Chemistry.png"),
    SW("生物", "Biology","api/img/gam/common/question3/print_ic_Biology.png"),
    LS("历史", "History","api/img/gam/common/question3/print_ic_History.png"),
    ZZ("音乐", "Music","api/img/gam/common/question3/print_ic_Music.png"),
    XX("课外", "Extracurricular","api/img/gam/common/question3/print_ic_Extracurricular.png"),
    TY("补习", "Remediation","api/img/gam/common/question3/print_ic_Remediation.png"),;
    private final String                                value;
    private final String                                code;
    private final String                                icon;
    private static final LinkedHashMap<String, HomeWorkSubjectEnums>  map;
    static {
        map = new LinkedHashMap<String, HomeWorkSubjectEnums>();
        for (HomeWorkSubjectEnums homeWorkSubjectEnums : HomeWorkSubjectEnums.values()) {
            map.put(homeWorkSubjectEnums.getCode(), homeWorkSubjectEnums);
        }
    }

    HomeWorkSubjectEnums(String value, String code, String icon) {
        this.value = value;
        this.code = code;
        this.icon = icon;
    }
    
    public String getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }

    public String getIcon() {
        return icon;
    }

    public static LinkedHashMap<String, HomeWorkSubjectEnums> getMap() {
        return map;
    }
}
