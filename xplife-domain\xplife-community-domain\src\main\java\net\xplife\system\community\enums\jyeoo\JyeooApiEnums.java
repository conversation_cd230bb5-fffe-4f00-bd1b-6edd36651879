package net.xplife.system.community.enums.jyeoo;

import java.util.LinkedHashMap;

public enum JyeooApiEnums {
    SEARCH_KEYWORD  ("keyword",     "按关键字搜索试题","jyeoo:request:api:keyword:", 100,       "今日搜索次数用光了 明天再来吧"),
    SEARCH_COND     ("cond",        "按条件精选题库",   "jyeoo:request:api:searchcond:", 250,  "今日搜索次数用光了 明天再来吧"),
    VIEW_QUES       ("viewques",    "查看试题",        "jyeoo:request:api:viewques:", 150,      "今日查看次数用光了 明天再来吧"),
    NOMINATE_QUES   ("nominateques","推荐试题",         "jyeoo:request:api:nominateques:", 50, "相似题次数用光了 明天再来吧"),
    WHITE_LIST_USER  ("whitelist",     "白名单用户列表","jyeoo:request:api:white:list", 0,       ""),
    WHITE_USER  ("whiteuser",     "白名单用户","jyeoo:request:api:white:user:by:", 0,       "");
    private final String                               key;                 //key
    private final String                               desc;                //描述
    private final String                               cacheKey;            //缓存key
    private final Integer                              canReqCount;         //每日可请求次数
    private final String                               exceptionDesc;       //超过后,返回的文案
    private static final LinkedHashMap<String, JyeooApiEnums> map;
    static {
        map = new LinkedHashMap<String, JyeooApiEnums>();
        for (JyeooApiEnums jyeooCacheEnums : JyeooApiEnums.values()) {
            map.put(jyeooCacheEnums.getKey(), jyeooCacheEnums);
        }
    }

    private JyeooApiEnums(String key, String desc, String cacheKey, Integer canReqCount, String exceptionDesc) {
        this.key = key;
        this.desc = desc;
        this.cacheKey = cacheKey;
        this.canReqCount = canReqCount;
        this.exceptionDesc = exceptionDesc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public String getCacheKey(){
        return cacheKey;
    }

    public Integer getCanReqCount() {
        return canReqCount;
    }

    public String getExceptionDesc() {
        return exceptionDesc;
    }

    public static LinkedHashMap<String, JyeooApiEnums> getMap() {
        return map;
    }
}