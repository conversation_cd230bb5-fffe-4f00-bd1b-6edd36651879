package net.xplife.system.community.enums.label;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum LabelFeedCacheEnums implements ICacheEnums {
    LABEL_FEED_BY_ID("comm:lab:fe:by:id:", "标签动态数据对象"),
    LABEL_FEED_BY_FLAG("comm:lab:fe:by:flag:", "标签动态数据对象"),
    LABEL_FEED_ID_LIST("comm:lab:fe:by:list:", "标签动态数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (LabelFeedCacheEnums labelFeedCacheEnums : LabelFeedCacheEnums.values()) {
            map.put(labelFeedCacheEnums.getKey(), labelFeedCacheEnums.getDesc());
        }
    }

    private LabelFeedCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
