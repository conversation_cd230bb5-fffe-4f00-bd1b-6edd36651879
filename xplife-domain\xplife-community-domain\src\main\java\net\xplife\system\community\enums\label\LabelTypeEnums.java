package net.xplife.system.community.enums.label;
import java.util.LinkedHashMap;
/**
 * 标签类型
 */
public enum LabelTypeEnums {
    LABEL(0, "标签"), 
    ALL(1, "全部");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (LabelTypeEnums labelTypeEnums : LabelTypeEnums.values()) {
            map.put(labelTypeEnums.getValue(), labelTypeEnums.getDesc());
        }
    }

    LabelTypeEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
