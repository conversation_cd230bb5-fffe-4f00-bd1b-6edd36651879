package net.xplife.system.community.enums.label;
import java.util.LinkedHashMap;
/**
 * 显示类型
 */
public enum ShowTypeEnums {
    ALL(0, "都显示"), 
    MATERIAL(1, "素材显示"), 
    FEED(2, "发帖显示"),
    NOSHOW(99, "不显示");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (ShowTypeEnums showTypeEnums : ShowTypeEnums.values()) {
            map.put(showTypeEnums.getValue(), showTypeEnums.getDesc());
        }
    }

    ShowTypeEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
