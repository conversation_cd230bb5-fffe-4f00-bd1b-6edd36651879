package net.xplife.system.community.enums.material;
import java.util.LinkedHashMap;
/**
 * 素材图片分辨率
 */
public enum MaterialPicEnums {
    P60x60("60x60", "编辑纸条");
    private final String                               value;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<>();
        for (MaterialPicEnums materialPicEnums : MaterialPicEnums.values()) {
            map.put(materialPicEnums.getValue(), materialPicEnums.getDesc());
        }
    }

    MaterialPicEnums(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
