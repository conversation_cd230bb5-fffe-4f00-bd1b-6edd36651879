package net.xplife.system.community.enums.material;
import java.util.LinkedHashMap;
/**
 * 素材类型
 */
public enum MaterialTypeEnums {
    EDIT(1, "编辑纸条"), 
    DETAIL(2, "清单"), 
    NOTE(3, "草稿箱"), 
    HISTORY(4, "历史"),
    A4(5,"A4打印");

    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (MaterialTypeEnums materialTypeEnums : MaterialTypeEnums.values()) {
            map.put(materialTypeEnums.getValue(), materialTypeEnums.getDesc());
        } 
    }

    MaterialTypeEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
