package net.xplife.system.community.enums.printer;

import java.util.LinkedHashMap;

/**
 * 云打印时，各种类型文档的icon
 */
public enum PrintActionCodeEnums {
    PRINT_INFORMATION("080000","打印说明书", "","正在打印说明书", ""),
    QUERY_HOMEWORK("120000", "查询作业", "query", "您今天有%s份作业", "您今天没有作业"),
    PRINT_HOMEWORK_ALL("130000", "打印全部作业，打印全部文档", "","正在打印今天所有作业", "您今天没有作业需要打印"),
    PRINT_HOMEWORK_YUWEN("140000", "打印语文作业", "Chinese","正在打印语文作业", "您今天没有语文作业需要打印"),
    PRINT_HOMEWORK_SHUXUE("150000", "打印数学作业", "Mathematics","正在打印数学作业", "您今天没有数学作业需要打印"),
    PRINT_HOMEWORK_YINGYU("160000", "打印英文作业", "English","正在打印英文作业", "您今天没有英文作业需要打印"),
    PRINT_HOMEWORK_WULI("170000", "打印物理作业", "Physics","正在打印物理作业", "您今天没有物理作业需要打印"),
    PRINT_HOMEWORK_KEXUE("180000", "打印科学作业", "Science","正在打印科学作业", "您今天没有科学作业需要打印"),
    PRINT_HOMEWORK_DILI("190000", "打印地理作业", "Geography","正在打印地理作业", "您今天没有地理作业需要打印"),
    PRINT_HOMEWORK_HUAXUE("200000", "打印化学作业", "Chemistry","正在打印化学作业", "您今天没有化学作业需要打印"),
    PRINT_HOMEWORK_SHENGWU("210000", "打印生物作业", "Biology","正在打印生物作业", "您今天没有生物作业需要打印"),
    PRINT_HOMEWORK_LISHI("220000", "打印历史作业", "History","正在打印历史作业", "您今天没有历史作业需要打印"),
    PRINT_HOMEWORK_YINYUE("230000", "打印音乐作业", "Music","正在打印音乐作业", "您今天没有音乐作业需要打印"),
    PRINT_HOMEWORK_KEWAI("240000", "打印课外作业", "Extracurricular","正在打印课外作业", "您今天没有课外作业需要打印"),
    PRINT_HOMEWORK_BUXI("250000", "打印补习作业", "Remediation","正在打印补习作业", "您今天没有补习作业需要打印")
    ;
    private final String                                code;
    private final String                             homeworkType;
    private final String                             desc;
    private final String                             replaySuccess;
    private final String                             replayFail;
    private static final LinkedHashMap<String, PrintActionCodeEnums>  map;
    static {
        map = new LinkedHashMap<String, PrintActionCodeEnums>();
        for (PrintActionCodeEnums enums : PrintActionCodeEnums.values()) {
            map.put(enums.getCode(), enums);
        }
    }

    PrintActionCodeEnums(String code, String desc, String homeworkType, String replaySuccess, String replayFail) {
        this.code = code;
        this.desc = desc;
        this.homeworkType = homeworkType;
        this.replaySuccess = replaySuccess;
        this.replayFail = replayFail;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getReplaySuccess() {
        return replaySuccess;
    }

    public String getReplayFail() {
        return replayFail;
    }

    public String getHomeworkType() {
        return homeworkType;
    }

    public static LinkedHashMap<String, PrintActionCodeEnums> getMap() {
        return map;
    }
}
