package net.xplife.system.community.enums.printer;
import java.util.LinkedHashMap;

/**
 * 打印机固件升级数据的审批状态
 */
public enum PrintDriverUpdateFollowTypeEnums {
    COMMON(0, "预发布"),
    AUDIT(1, "提交审核"),
    SUCCESS(99, "审核通过"),;
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (PrintDriverUpdateFollowTypeEnums draftsTypeEnums : PrintDriverUpdateFollowTypeEnums.values()) {
            map.put(draftsTypeEnums.getValue(), draftsTypeEnums.getDesc());
        }
    }

    PrintDriverUpdateFollowTypeEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
