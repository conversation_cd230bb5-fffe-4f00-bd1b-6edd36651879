package net.xplife.system.community.enums.printer;

import java.util.LinkedHashMap;

/**
 * 云打印时，各种类型文档的icon
 */
public enum PrintFileTypeEnums {
    XLS("xls","api/img/gam/common/excel.png"),
    XLSX("xlsx", "api/img/gam/common/excel.png"),
    DOC("doc", "api/img/gam/common/word.png"),
    DOCX("docx", "api/img/gam/common/word.png"),
    PPT("ppt", "api/img/gam/common/ppt.png"),
    PPTX("pptx", "api/img/gam/common/pptx.png"),
    TXT("txt", "api/img/gam/common/txt.png"),
    PDF("pdf", "api/img/gam/common/pdf.png"),
    OTHER("other", "api/img/gam/common/txt.png");
    private final String                                code;
    private final String                                icon;
    private static final LinkedHashMap<String, String>  map;
    static {
        map = new LinkedHashMap<String, String>();
        for (PrintFileTypeEnums printerTypeEnums : PrintFileTypeEnums.values()) {
            map.put(printerTypeEnums.getCode(), printerTypeEnums.getIcon());
        }
    }

    PrintFileTypeEnums(String code, String icon) {
        this.code = code;
        this.icon = icon;
    }

    public String getCode() {
        return code;
    }

    public String getIcon() {
        return icon;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
