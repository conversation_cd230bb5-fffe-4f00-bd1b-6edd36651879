package net.xplife.system.community.enums.printer;

import java.util.LinkedHashMap;

public enum PrinterStatuEnums {
    NOEXIST(0, "未配网"),
    ONLINE(1, "在线"),
    OFFLINE(2, "不在线"),;

    private final int                                code;
    private final String                             desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<Integer, String>();
        for (PrinterStatuEnums enums : PrinterStatuEnums.values()) {
            map.put(enums.getCode(), enums.getDesc());
        }
    }

    PrinterStatuEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
