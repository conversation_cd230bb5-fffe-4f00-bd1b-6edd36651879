package net.xplife.system.community.enums.printer;

import java.util.LinkedHashMap;

/**
 * 打印机类型A4
 */
public enum PrinterTypeA4Enums {
    YW("P81", "P81","api/img/gam/common/printer_p81.png"),
    SX("T81", "T81","api/img/gam/common/printer_t81.png"),
    WL("D81(天猫精灵款)", "D81_TM","api/img/gam/common/printer_d81_tm.png"),
    YY("D81", "D81","api/img/gam/common/printer_d81.png"),
    ;
    private final String                                name;
    private final String                                code;
    private final String                                icon;
    private static final LinkedHashMap<String, PrinterTypeA4Enums>  map;
    static {
        map = new LinkedHashMap<String, PrinterTypeA4Enums>();
        for (PrinterTypeA4Enums printerTypeEnums : PrinterTypeA4Enums.values()) {
            map.put(printerTypeEnums.getCode(), printerTypeEnums);
        }
    }

    PrinterTypeA4Enums(String name, String code, String icon) {
        this.name = name;
        this.code = code;
        this.icon = icon;
    }
    
    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public String getIcon() {
        return icon;
    }

    public static LinkedHashMap<String, PrinterTypeA4Enums> getMap() {
        return map;
    }
}
