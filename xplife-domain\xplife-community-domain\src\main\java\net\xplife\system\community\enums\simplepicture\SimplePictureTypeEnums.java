package net.xplife.system.community.enums.simplepicture;

import java.util.LinkedHashMap;

public enum SimplePictureTypeEnums {
    ANIMAL("animal", "动物"),
    PLANT("plant", "植物"),
    PERSON("person", "人物"),
    FRUIT("fruit", "水果"),
    VEG<PERSON><PERSON><PERSON>("vegetable","蔬菜"),
    TRAFFIC("traffic", "交通"),
    LIFE("life", "生活"),
    GEOMETRY("geometry", "几何"),
    ;

    private final String                               value;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<>();
        for (SimplePictureTypeEnums enumObj : SimplePictureTypeEnums.values()) {
            map.put(enumObj.getValue(), enumObj.getDesc());
        }
    }

    SimplePictureTypeEnums(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
