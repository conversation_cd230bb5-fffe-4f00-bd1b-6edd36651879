package net.xplife.system.community.enums.soulword;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum SoulWordCacheEnums implements ICacheEnums {
    SOUL_WORD_LIST("comm:soul:by:list", "心灵鸡汤数据id集合"),
    SOUL_WORD_LIST_CURRDAY("comm:soul:by:currday", "今天的心灵鸡汤内容");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (SoulWordCacheEnums soulCacheEnums : SoulWordCacheEnums.values()) {
            map.put(soulCacheEnums.getKey(), soulCacheEnums.getDesc());
        }
    }

    private SoulWordCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
