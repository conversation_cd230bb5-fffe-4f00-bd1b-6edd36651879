package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum CollectLabelCacheEnums implements ICacheEnums {
    COLLECT_LABEL_BY_UID("comm:col:la:by:uid:", "收藏标签记录");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (CollectLabelCacheEnums collectLabelCacheEnums : CollectLabelCacheEnums.values()) {
            map.put(collectLabelCacheEnums.getKey(), collectLabelCacheEnums.getDesc());
        }
    }  
  
    private CollectLabelCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
