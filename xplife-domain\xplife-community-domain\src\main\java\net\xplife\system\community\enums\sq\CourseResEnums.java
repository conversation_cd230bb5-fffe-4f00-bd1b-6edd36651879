package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
/**
 * 课程图片资源key
 */
public enum CourseResEnums {
    P200x264("200x264", "课程图片"),;
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<>();
        for (CourseResEnums courseResEnums : CourseResEnums.values()) {
            map.put(courseResEnums.getKey(), courseResEnums.getDesc());
        }
    }

    CourseResEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
