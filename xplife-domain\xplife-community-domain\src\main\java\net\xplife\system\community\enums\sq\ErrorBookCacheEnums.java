package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum ErrorBookCacheEnums implements ICacheEnums {
    ERROR_BOOK_BY_ID("comm:err:bo:by:id:", "错题本记录"),
    ERROR_BOOK_ID_LIST("comm:err:bo:by:list:", "错题本数据ID集合"),
    ERROR_CONTENT_BY_ID("comm:err:con:by:id:", "错题内容记录"),;
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (ErrorBookCacheEnums errorBookCacheEnums : ErrorBookCacheEnums.values()) {
            map.put(errorBookCacheEnums.getKey(), errorBookCacheEnums.getDesc());
        }
    } 
  
    private ErrorBookCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
