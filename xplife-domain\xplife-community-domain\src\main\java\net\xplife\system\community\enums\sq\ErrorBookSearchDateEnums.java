package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
/**
 * 错题本筛选日期
 */
public enum ErrorBookSearchDateEnums {
    THREE_DAY("近三天", "1"), 
    ONE_WEEK("近一周", "2"), 
    ONE_MONTH("近一月", "3"), 
    DESC("时间降序", "4"), 
    ASC("时间升序", "5"),;
    private final String                                value;
    private final String                                type;
    private static final LinkedHashMap<String, String>  map;
    static {
        map = new LinkedHashMap<String, String>();
        for (ErrorBookSearchDateEnums errorBookSearchDateEnums : ErrorBookSearchDateEnums.values()) {
            map.put(errorBookSearchDateEnums.getValue(), errorBookSearchDateEnums.getType());
        } 
    } 

    ErrorBookSearchDateEnums(String value, String type) {
        this.value = value;
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public String getType() {
        return type;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
