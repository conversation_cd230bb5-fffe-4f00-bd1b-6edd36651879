package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
/**
 * 错题本掌握程度枚举
 */
public enum ErrorBookSearchDegreeEnums {
    NO_MASTERED("未掌握", "1"), 
    MASTERED("已掌握", "2"), 
    SKILLED("熟练", "3"),;
    private final String                                value;
    private final String                                type;
    private static final LinkedHashMap<String, String>  map;
    static {
        map = new LinkedHashMap<String, String>();
        for (ErrorBookSearchDegreeEnums errorBookSearchDegreeEnums : ErrorBookSearchDegreeEnums.values()) {
            map.put(errorBookSearchDegreeEnums.getValue(), errorBookSearchDegreeEnums.getType());
        } 
    } 

    ErrorBookSearchDegreeEnums(String value, String type) {
        this.value = value;
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public String getType() {
        return type;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
