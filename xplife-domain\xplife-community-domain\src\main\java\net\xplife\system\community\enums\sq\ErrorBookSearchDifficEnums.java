package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
/**
 * 错题本难度枚举
 */
public enum ErrorBookSearchDifficEnums {
    EASILY("容易", "1"), 
    NORMAL("一般", "2"), 
    HARD("困难", "3"),
    VERY_HARD("非常困难", "4"),;
    private final String                                value;
    private final String                                type;
    private static final LinkedHashMap<String, String>  map;
    static {
        map = new LinkedHashMap<String, String>();
        for (ErrorBookSearchDifficEnums errorBookSearchDifficEnums : ErrorBookSearchDifficEnums.values()) {
            map.put(errorBookSearchDifficEnums.getValue(), errorBookSearchDifficEnums.getType());
        } 
    }  

    ErrorBookSearchDifficEnums(String value, String type) {
        this.value = value;
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public String getType() {
        return type;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
