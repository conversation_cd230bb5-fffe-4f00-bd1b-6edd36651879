package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
/**
 * 错题本筛选类型
 */
public enum ErrorBookSearchTypeEnums {
    SUBJECT("错题科目", "1"), 
    TYPE("错题类型", "2"), 
    DIFFIC("错题难度", "3"), 
    REASON("错题原因", "4"), 
    DEGREE("掌握程度", "5"), 
    SOURCE("错题来源", "6"), 
    CUSTOM("自定义标签", "7"),;
    private final String                                value;
    private final String                                type;
    private static final LinkedHashMap<String, String>  map;
    static {
        map = new LinkedHashMap<String, String>();
        for (ErrorBookSearchTypeEnums errorBookTypeEnums : ErrorBookSearchTypeEnums.values()) {
            map.put(errorBookTypeEnums.getValue(), errorBookTypeEnums.getType());
        } 
    }

    ErrorBookSearchTypeEnums(String value, String type) {
        this.value = value;
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public String getType() {
        return type;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
