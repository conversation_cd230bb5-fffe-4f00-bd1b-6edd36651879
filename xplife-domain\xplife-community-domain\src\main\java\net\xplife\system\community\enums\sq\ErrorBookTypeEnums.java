package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
/**
 * 错题本类型
 */
public enum ErrorBookTypeEnums {
    YW("语文", "1"), 
    SX("数学", "2"), 
    YY("英语", "3"), 
    SW("生物", "4"), 
    ZZ("政治", "5"), 
    LS("历史", "6"), 
    DL("地理", "7"), 
    WL("物理", "8"), 
    HX("化学", "9"), 
    QT("其他", "10"),
    // 以下的为箐优网返回的
    XXSX("小学数学", "10"),
    XXYW("小学语文", "11"),
    XXYY("小学英语", "12"),
    XXKX("小学科学", "14"),
    CZSX("初中数学", "20"),
    CZWL("初中物理", "21"),
    CZHX("初中化学", "22"),
    CZSW("初中生物", "23"),
    CZDL("初中地理", "25"),
    CZYY("初中英语", "27"),
    CZYW("初中语文", "26"),
    CZZZ("初中政治", "28"),
    CZLS("初中历史", "29"),
    GZSX("高中数学", "30"),
    GZWL("高中物理", "31"),
    GZHX("高中化学", "32"),
    GZSW("高中生物", "33"),
    GZDL("高中地理", "35"),
    GZYY("高中英语", "37"),
    GZYW("高中语文", "36"),
    GZZZ("高中政治", "38"),
    GZLS("高中历史", "39"),
    GZXX("高中信息", "42"),
    GZTY("高中通用", "43");


    private final String                                value;
    private final String                                type;
    private static final LinkedHashMap<String, String> map;
    private static final LinkedHashMap<String, String> keyTypeMap;
    static {
        map = new LinkedHashMap<String, String>();
        keyTypeMap = new LinkedHashMap<String, String>();
        for (ErrorBookTypeEnums errorBookTypeEnums : ErrorBookTypeEnums.values()) {
            map.put(errorBookTypeEnums.getValue(), errorBookTypeEnums.getType());
            keyTypeMap.put(errorBookTypeEnums.getType(), errorBookTypeEnums.getValue());
        } 
    }

    ErrorBookTypeEnums(String value, String type) {
        this.value = value;
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public String getType() {
        return type;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }

    public static LinkedHashMap<String, String> getKeyTypeMap() {
        return keyTypeMap;
    }
}
