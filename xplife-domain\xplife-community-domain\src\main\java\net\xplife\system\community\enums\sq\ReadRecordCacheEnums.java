package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum ReadRecordCacheEnums implements ICacheEnums {
    READ_RECORD_BY_COURSE_ID("comm:re:re:by:coid:", "已读记录"),;
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (ReadRecordCacheEnums readRecordCacheEnums : ReadRecordCacheEnums.values()) {
            map.put(readRecordCacheEnums.getKey(), readRecordCacheEnums.getDesc());
        }
    }

    private ReadRecordCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
