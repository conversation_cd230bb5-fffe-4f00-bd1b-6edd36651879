package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
/**
 * 精选题库类型
 */
public enum SelectedQuestionTypeEnums {
//    YW(1,"语文", "chinese","api/img/gam/common/question/print_ic_CN.png","chinese,chinese2,chinese3"),
//    SX(2,"数学", "math","api/img/gam/common/question/print_ic_mathematics.png","math,math2,math3"),
//    YY(3,"英语", "english","api/img/gam/common/question/print_ic_English.png","english,english2,english3"),
//    SW(4,"生物", "bio","api/img/gam/common/question/print_ic_biology.png","bio,bio2"),
//    ZZ(5,"政治", "politics","api/img/gam/common/question/print_ic_politics.png","politics,politics2"),
//    LS(6,"历史", "history","api/img/gam/common/question/print_ic_history.png","history,history2"),
//    DL(7,"地理", "geography","api/img/gam/common/question/print_ic_geography.png","geography,geography2"),
//    WL(8,"物理", "physics","api/img/gam/common/question/print_ic_physics.png","physics,physics2"),
//    HX(9,"化学", "chemistry","api/img/gam/common/question/print_ic_chemistry.png","chemistry,chemistry2"),
//    XX(10,"信息", "information","api/img/gam/common/question/print_ic_information.png","tech1"),
//    TY(11,"通用", "currency","api/img/gam/common/question/print_ic_common.png","tech2"),
//    KX(12,"科学", "science","api/img/gam/common/question/print_ic_Science.png","science,science2,science3"),
//    DF(13,"道法", "politics","api/img/gam/common/question/print_ic_fadao.png","politics"),;
    YW(1,"语文", "chinese","api/img/gam/common/question/print_ic_CN.png","chinese,chinese2,chinese3"),
    SX(2,"数学", "math","api/img/gam/common/question/print_ic_mathematics.png","math,math2,math3"),
    YY(3,"英语", "english","api/img/gam/common/question/print_ic_English.png","english,english2,english3"),
    SW(4,"生物", "bio","api/img/gam/common/question/print_ic_biology.png","bio,bio2"),
    ZZ(5,"政治", "politics","api/img/gam/common/question/print_ic_politics.png","politics,politics2"),
    LS(6,"历史", "history","api/img/gam/common/question/print_ic_history.png","history,history2"),
    DL(7,"地理", "geography","api/img/gam/common/question/print_ic_geography.png","geography,geography2"),
    WL(8,"物理", "physics","api/img/gam/common/question/print_ic_physics.png","physics,physics2"),
    HX(9,"化学", "chemistry","api/img/gam/common/question/print_ic_chemistry.png","chemistry,chemistry2"),
    XX(10,"信息", "information","api/img/gam/common/question/print_ic_information.png","tech1"),
    TY(11,"通用", "currency","api/img/gam/common/question/print_ic_common.png","tech2"),
    KX(12,"科学", "science","api/img/gam/common/question/print_ic_Science.png","science,science2,science3"),
    DF(13,"道法", "politics","api/img/gam/common/question/print_ic_fadao.png","politics"),;
    private final int                                   type;
    private final String                                value;
    private final String                                english;
    private final String                                icon;
    private final String                                keyword;
    private static final LinkedHashMap<String, String>  map;
    static {
        map = new LinkedHashMap<String, String>();
        for (SelectedQuestionTypeEnums selectedQuestionTypeEnums : SelectedQuestionTypeEnums.values()) {
            map.put(selectedQuestionTypeEnums.getValue(), selectedQuestionTypeEnums.getEnglish());
        }  
    }

    SelectedQuestionTypeEnums(int type,String value, String english,String icon,String keyword) {
        this.type=type;
        this.value = value;
        this.english = english;
        this.icon = icon;
        this.keyword = keyword;
    }

    public int getType() {
        return type;
    }
    
    public String getValue() {
        return value;
    }

    public String getEnglish() {
        return english;
    }

    public String getIcon() {
        return icon;
    }
    
    public String getKeyword() {
        return keyword;
    }
    
    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
