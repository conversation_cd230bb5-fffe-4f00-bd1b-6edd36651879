package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;

/**
 * 精选题库类型
 * 3.0.0版本后，取这里的english和icon的地址
 */
public enum SelectedQuestionTypeEnums300 {
    YW(1,"语文", "Chinese","api/img/gam/common/question3/print_ic_Chinese.png","chinese,chinese2,chinese3"),
    SX(2,"数学", "Mathematics","api/img/gam/common/question3/print_ic_Mathematics.png","math,math2,math3"),
    YY(3,"英语", "English","api/img/gam/common/question3/print_ic_English.png","english,english2,english3"),
    SW(4,"生物", "Biology","api/img/gam/common/question3/print_ic_Biology.png","bio,bio2"),
    ZZ(5,"政治", "Politics","api/img/gam/common/question3/print_ic_Politics.png","politics,politics2"),
    LS(6,"历史", "History","api/img/gam/common/question3/print_ic_History.png","history,history2"),
    DL(7,"地理", "Geography","api/img/gam/common/question3/print_ic_Geography.png","geography,geography2"),
    WL(8,"物理", "Physics","api/img/gam/common/question3/print_ic_Physics.png","physics,physics2"),
    HX(9,"化学", "Chemistry","api/img/gam/common/question3/print_ic_Chemistry.png","chemistry,chemistry2"),
    XX(10,"信息", "Information","api/img/gam/common/question3/print_ic_Information.png","tech1"),
    TY(11,"通用", "Currency","api/img/gam/common/question3/print_ic_Currency.png","tech2"),
    KX(12,"科学", "Science","api/img/gam/common/question3/print_ic_Science.png","science,science2,science3"),
    DF(13,"道法", "Politics","api/img/gam/common/question3/print_ic_fadao.png","politics"),;
    private final int                                   type;
    private final String                                value;
    private final String                                english;
    private final String                                icon;
    private final String                                keyword;
    private static final LinkedHashMap<String, String>  map;
    static {
        map = new LinkedHashMap<String, String>();
        for (SelectedQuestionTypeEnums300 selectedQuestionTypeEnums : SelectedQuestionTypeEnums300.values()) {
            map.put(selectedQuestionTypeEnums.getValue(), selectedQuestionTypeEnums.getEnglish());
        }
    }

    SelectedQuestionTypeEnums300(int type, String value, String english, String icon, String keyword) {
        this.type=type;
        this.value = value;
        this.english = english;
        this.icon = icon;
        this.keyword = keyword;
    }

    public int getType() {
        return type;
    }
    
    public String getValue() {
        return value;
    }

    public String getEnglish() {
        return english;
    }

    public String getIcon() {
        return icon;
    }
    
    public String getKeyword() {
        return keyword;
    }
    
    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
