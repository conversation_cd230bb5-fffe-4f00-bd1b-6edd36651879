package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum UserCourseCacheEnums implements ICacheEnums {
    USER_COURSE_BY_UID("comm:us:co:by:uid:", "用户课程记录"),
    WORD_LIST_BY_UID("comm:us:word:by:uid:", "用户课程单词列表"),;
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserCourseCacheEnums userCourseCacheEnums : UserCourseCacheEnums.values()) {
            map.put(userCourseCacheEnums.getKey(), userCourseCacheEnums.getDesc());
        }
    }     
  
    private UserCourseCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
