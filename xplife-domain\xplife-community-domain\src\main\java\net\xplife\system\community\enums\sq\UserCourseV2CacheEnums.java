package net.xplife.system.community.enums.sq;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum UserCourseV2CacheEnums implements ICacheEnums {
    USER_COURSE_BY_UID("comm:us:co:v2:by:uid:", "用户课程记录"),
    WORD_LIST_BY_UID("comm:us:word:v2:by:uid:", "用户课程单词列表"),;
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserCourseV2CacheEnums userCourseCacheEnums : UserCourseV2CacheEnums.values()) {
            map.put(userCourseCacheEnums.getKey(), userCourseCacheEnums.getDesc());
        }
    }

    private UserCourseV2CacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
