package net.xplife.system.community.enums.sq;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum UserWordsV2CacheEnums implements ICacheEnums {
    USER_WORDS_BY_ID("comm:us:wo:v2:by:id:", "用户生词本记录"),
    USER_WORDS_BY_FLAG("comm:us:wo:v2:by:flag:", "用户生词本记录"),
    USER_WORDS_ID_LIST("comm:us:wo:v2:by:list:", "用户生词本ID集合"),;
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserWordsV2CacheEnums userWordsCacheEnums : UserWordsV2CacheEnums.values()) {
            map.put(userWordsCacheEnums.getKey(), userWordsCacheEnums.getDesc());
        }
    }

    private UserWordsV2CacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
