package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
/**
 * 词库类型
 */
public enum WordTypeEnums {
    NO_SHOW(0, "不显示课程"), 
    SHOW(1, "显示课程"), 
    EXPECT(2, "敬请期待"),;
    private final Integer                               value;
    private final String                                type;
    private static final LinkedHashMap<Integer, String>  map;
    static {
        map = new LinkedHashMap<Integer, String>();
        for (WordTypeEnums wordTypeEnums : WordTypeEnums.values()) {
            map.put(wordTypeEnums.getValue(), wordTypeEnums.getType());
        }  
    }

    WordTypeEnums(Integer value, String type) {
        this.value = value;
        this.type = type;
    }

    public Integer getValue() {
        return value;
    }

    public String getType() {
        return type;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
