package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum WordsCacheEnums implements ICacheEnums {
    WORDS_BY_ID("comm:wo:by:id:", "单词记录"),
    WORDS_BY_LIST("comm:wo:by:list:", "单词记录列表"),;
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (WordsCacheEnums wordsCacheEnums : WordsCacheEnums.values()) {
            map.put(wordsCacheEnums.getKey(), wordsCacheEnums.getDesc());
        }
    }    
  
    private WordsCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
