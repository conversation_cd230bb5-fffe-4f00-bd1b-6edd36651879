package net.xplife.system.community.enums.sq;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum WordsLibraryCacheEnums implements ICacheEnums {
    WORDS_LIBRARY_BY_FLAG("comm:wo:li:by:flag:", "词库记录"),;
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (WordsLibraryCacheEnums wordsLibraryCacheEnums : WordsLibraryCacheEnums.values()) {
            map.put(wordsLibraryCacheEnums.getKey(), wordsLibraryCacheEnums.getDesc());
        }
    }   
  
    private WordsLibraryCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
