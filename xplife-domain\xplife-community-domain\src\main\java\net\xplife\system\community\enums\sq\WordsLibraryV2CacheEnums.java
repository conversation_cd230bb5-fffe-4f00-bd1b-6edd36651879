package net.xplife.system.community.enums.sq;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum WordsLibraryV2CacheEnums implements ICacheEnums {
    WORDS_LIBRARY_BY_FLAG("comm:wo:li:v2:by:flag:", "词库记录"),;
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (WordsLibraryV2CacheEnums wordsLibraryCacheEnums : WordsLibraryV2CacheEnums.values()) {
            map.put(wordsLibraryCacheEnums.getKey(), wordsLibraryCacheEnums.getDesc());
        }
    }

    private WordsLibraryV2CacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
