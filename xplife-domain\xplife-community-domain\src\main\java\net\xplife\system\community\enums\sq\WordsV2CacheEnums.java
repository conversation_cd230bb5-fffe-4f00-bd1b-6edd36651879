package net.xplife.system.community.enums.sq;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum WordsV2CacheEnums implements ICacheEnums {
    WORDS_BY_ID("comm:wo:v2:by:id:", "单词记录"),
    WORDS_BY_LIST("comm:wo:v2:by:list:", "单词记录列表"),;
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (WordsV2CacheEnums wordsCacheEnums : WordsV2CacheEnums.values()) {
            map.put(wordsCacheEnums.getKey(), wordsCacheEnums.getDesc());
        }
    }

    private WordsV2CacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
