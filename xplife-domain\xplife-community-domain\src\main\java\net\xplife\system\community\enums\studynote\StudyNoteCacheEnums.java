package net.xplife.system.community.enums.studynote;

import java.util.LinkedHashMap;

public enum StudyNoteCacheEnums {
    STUDY_NOTE_BY_ID("comm:study:note:by:id:", "学习圈记录"),
    STUDY_NOTE_FOR_STICK_TOP("comm:study:note:stick:top", "置顶记录"),
    STUDY_NOTE_FOR_STICK_ESSENCE("comm:study:note:stick:essence", "精华记录");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (StudyNoteCacheEnums studyNoteCacheEnums : StudyNoteCacheEnums.values()) {
            map.put(studyNoteCacheEnums.getKey(), studyNoteCacheEnums.getDesc());
        }
    }

    private StudyNoteCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
