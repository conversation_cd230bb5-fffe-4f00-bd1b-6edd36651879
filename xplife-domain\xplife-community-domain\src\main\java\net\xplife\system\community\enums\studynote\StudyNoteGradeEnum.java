package net.xplife.system.community.enums.studynote;

import net.xplife.system.tools.util.core.ToolsKit;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum StudyNoteGradeEnum {
    XX("小学", "xx", "subject_level_xx"),
    CZ("初中", "cz", "subject_level_cz"),
    GZ("高中", "gz", "subject_level_gz");
    private final String                                value;
    private final String                                key;
    private final String                                desc;

    StudyNoteGradeEnum(String value, String key, String desc) {
        this.value = value;
        this.key = key;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() { return desc;}

    public static List<Map<String, String>> getList(){
        List<Map<String, String>> rtn = new ArrayList<>();
        for (StudyNoteGradeEnum enumObj: StudyNoteGradeEnum.values()) {
            Map<String, String> temp = new HashMap<>();
            temp.put("id", enumObj.getKey());
            temp.put("name", enumObj.getValue());
            temp.put("desc", enumObj.getDesc());
            rtn.add(temp);
        }
        return rtn;
    }

    public static String getValueByKey(String key) {
        if (ToolsKit.isEmpty(key)){
            return "";
        }
        for (StudyNoteGradeEnum enumObj: StudyNoteGradeEnum.values()) {
            if (enumObj.getKey().equals(key)) {
                return enumObj.getValue();
            }
        }
        return "";
    }
}
