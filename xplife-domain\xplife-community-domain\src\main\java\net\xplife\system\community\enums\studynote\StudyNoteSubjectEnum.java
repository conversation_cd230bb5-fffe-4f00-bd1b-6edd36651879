package net.xplife.system.community.enums.studynote;

import net.xplife.system.tools.util.core.ToolsKit;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum StudyNoteSubjectEnum {
    YW("语文", "Chinese","xx,cz,gz", "subject_name_Chinese"),
    SX("数学", "Mathematics","xx,cz,gz", "subject_name_Mathematics"),
    YY("英语", "English","xx,cz,gz", "subject_name_English"),
    SW("生物", "Biology","cz,gz", "subject_name_Biology"),
    HX("化学", "Chemistry","cz,gz","subject_name_Chemistry"),
    WL("物理", "Physics","cz,gz","subject_name_Physics"),
    ZZ("政治", "Politics","gz","subject_name_Politics"),
    LS("历史", "History","cz,gz","subject_name_History"),
    DL("地理", "Geography","cz,gz","subject_name_Geography"),
    XX("其他", "Other","xx,cz,gz","subject_name_Other"),;
    private final String                                value;
    private final String                                key;
    private final String                                keyword;
    private final String                                desc;

    StudyNoteSubjectEnum(String value, String key, String keyword, String desc) {
        this.value = value;
        this.key = key;
        this.keyword = keyword;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getKey() {
        return key;
    }

    public String getKeyword() {
        return keyword;
    }

    public String getDesc() {
        return desc;
    }

    public static List<Map<String, String>> getList(){
        List<Map<String, String>> rtn = new ArrayList<>();
        for (StudyNoteSubjectEnum enumObj: StudyNoteSubjectEnum.values()) {
            Map<String, String> temp = new HashMap<>();
            temp.put("id", enumObj.getKey());
            temp.put("name", enumObj.getValue());
            temp.put("desc", enumObj.getDesc());
            rtn.add(temp);
        }
        return rtn;
    }

    public static String getValueByKey(String key){
        if (ToolsKit.isEmpty(key)){
            return "";
        }
        for (StudyNoteSubjectEnum enumObj: StudyNoteSubjectEnum.values()) {
            if (enumObj.getKey().equals(key)) {
                return enumObj.getValue();
            }
        }
        return "";
    }
}
