package net.xplife.system.community.enums.studynote;

import net.xplife.system.tools.util.core.ToolsKit;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum StudyPlanStatusEnum {
    NOT_START("未开始", "0"),
    BEGINNING("进行中", "1"),
    ADVANCED_COMPLETED("提前完成", "2"),
    COMPLETED("已完成", "9"),
    CANCEL("已取消", "-1");
    private final String                                value;
    private final String                                key;

    StudyPlanStatusEnum(String value, String key) {
        this.value = value;
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public String getKey() {
        return key;
    }

    public static List<Map<String, String>> getList(){
        List<Map<String, String>> rtn = new ArrayList<>();
        for (StudyPlanStatusEnum enumObj: StudyPlanStatusEnum.values()) {
            Map<String, String> temp = new HashMap<>();
            temp.put("id", enumObj.getKey());
            temp.put("name", enumObj.getValue());
            rtn.add(temp);
        }
        return rtn;
    }

    public static String getValueByKey(String key) {
        if (ToolsKit.isEmpty(key)){
            return "";
        }
        for (StudyPlanStatusEnum enumObj: StudyPlanStatusEnum.values()) {
            if (enumObj.getKey().equals(key)) {
                return enumObj.getValue();
            }
        }
        return "";
    }

}
