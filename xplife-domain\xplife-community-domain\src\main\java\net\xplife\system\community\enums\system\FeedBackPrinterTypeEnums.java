package net.xplife.system.community.enums.system;
import java.util.LinkedHashMap;

/**
 * 问题反馈时需要反馈的打印机类型
 */
public enum FeedBackPrinterTypeEnums {
    TP2_S("YOYINPRINTER", "TP2-S","https://m.yoyin.net/api/img/gam/common/feedback/tp2_s.png"),
    TP4("YOYINPRINTER", "TP4","https://m.yoyin.net/api/img/gam/common/feedback/tp4.png"),
    TP1("YOYINPRINTER", "TP1","https://m.yoyin.net/api/img/gam/common/feedback/tp1.png"),
    TP1_Y("YOYINPRINTER", "TP1-Y","https://m.yoyin.net/api/img/gam/common/feedback/tp1_y.png"),
    TP2("YOYINPRINTER", "TP2","https://m.yoyin.net/api/img/gam/common/feedback/tp2.png"),
    TP2_Y("YOYINPRINTER", "TP2-Y","https://m.yoyin.net/api/img/gam/common/feedback/tp2_y.png"),
    Other("YOYINPRINTER", "其他","https://m.yoyin.net/api/img/gam/common/feedback/other.png"),

    TP8("YOYIN4APRINTER", "P81","https://m.yoyin.net/api/img/gam/common/feedback/tp8.png"),
    TPT81("YOYIN4APRINTER", "T81","https://m.yoyin.net/api/img/gam/common/printer_t81.png"),
    TPD81("YOYIN4APRINTER", "D81","https://m.yoyin.net/api/img/gam/common/printer_d81.png"),
    Other4A("YOYIN4APRINTER", "其他","https://m.yoyin.net/api/img/gam/common/feedback/other_4.png"),;
    private final String                                appName;
    private final String                                key;
    private final String                                picUrl;


    FeedBackPrinterTypeEnums(String appName, String key, String picUrl) {
        this.appName = appName;
        this.key = key;
        this.picUrl = picUrl;
    }

    public String getAppName() {
        return appName;
    }

    public String getKey() {
        return key;
    }

    public String getPicUrl() {
        return picUrl;
    }

}
