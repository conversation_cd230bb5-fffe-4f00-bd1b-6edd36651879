package net.xplife.system.community.enums.system;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum PrintMileageCacheEnums implements ICacheEnums {
    PRINT_MILEAGE_BY_ID("system:print:mileage:by:id:", "打印里程对象"),
    PRINT_MILEAGE_RANKINGS_BY_MONTH("system:print:mileage:by:month:", "所有用户月打印id"),
    PRINT_MILEAGE_RANKINGS_BY_TOTAL("system:print:mileage:by:total", "所有用户打印id"),
    PRINT_MILEAGE_RANKINGS_BY_YEAR("system:print:mileage:by:year:", "所有用户年打印id") ;
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (PrintMileageCacheEnums cacheEnums : PrintMileageCacheEnums.values()) {
            map.put(cacheEnums.getKey(), cacheEnums.getDesc());
        }
    }

    private PrintMileageCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
