package net.xplife.system.community.enums.system;
import java.util.LinkedHashMap;
/**
 * 系统配置信息枚举
 */
public enum SysConfTypeEnums {
    INDEX_PIC(1, "indexpic","启动页"),
    PAPER(2, "paper","首页纸张"),
    PRINT_INDEX(3, "printindex","H5打印页"),
    WHILE_LIST(4, "whilelist","白名单"),
    ACTIVITY(5, "activity","活动页"),
    COURSE(6, "course","课程"),
    LATEST_VER(7, "latestver","最新版本"),
    DANGLING(8, "dangling", "悬浮按钮");
    private final int                                   type;
    private final String                                key;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (SysConfTypeEnums sysConfTypeEnums : SysConfTypeEnums.values()) {
            map.put(sysConfTypeEnums.getType(), sysConfTypeEnums.getKey());
        }
    }

    SysConfTypeEnums(Integer type, String key,String desc) {
        this.type = type;
        this.key = key;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
    
    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
