package net.xplife.system.community.enums.system;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum SystemConfigCacheEnums implements ICacheEnums {
    SYSTEM_CONFIG_PRE("comm:value:type:", "系统参数");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (SystemConfigCacheEnums systemConfigCacheEnums : SystemConfigCacheEnums.values()) {
            map.put(systemConfigCacheEnums.getKey(), systemConfigCacheEnums.getDesc());
        }
    } 

    private SystemConfigCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
