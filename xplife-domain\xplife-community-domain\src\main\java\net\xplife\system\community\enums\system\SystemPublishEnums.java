package net.xplife.system.community.enums.system;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;

public enum SystemPublishEnums implements ICacheEnums {
	LABEL_UPDATE_PUB_SUB("comm:label:update:pub:sub", "标签更新广播"),
	MATERIAL_UPDATE_PUB_SUB("comm:material:update:pub:sub", "素材库更新广播"),
	MATERIAL_RES_UPDATE_PUB_SUB("comm:material:res:update:pub:sub", "素材库资源更新广播"),
	UPDATE_INFO_UPDATE_PUB_SUB("comm:update:info:update:pub:sub", "版本更新广播"),
    PRINTER_DRIVER_UPDATE_PUB_SUB("comm:printerdriver:update:pub:sub", "固件升级更新"),
    WORD_UPDATE_PUB_SUB("comm:word:update:pub:sub", "生词本更新"),
    INITIAL_DICT_UPDATE_PUB_SUB("comm:initialdict:update:pub:sub", "启蒙训练字典项更新广播"),;
    private final String key;
    private final String desc;
    private static final LinkedHashMap<String, String> map;

    private SystemPublishEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
    
    

    static {
        map = new LinkedHashMap<>();  
        for (SystemPublishEnums systemCacheEnums : SystemPublishEnums.values()) {
            map.put(systemCacheEnums.getKey(), systemCacheEnums.getDesc());
        }
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
    
    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
