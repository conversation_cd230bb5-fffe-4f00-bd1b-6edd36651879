package net.xplife.system.community.enums.teenguard;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum TeenGuardCacheEnums implements ICacheEnums {
    TEEN_GUARD_BY_USERID("comm:teenguard:by:userid:", "青少年保护userid"),
    TEEN_GUARD_BY_IDCARD("comm:teenguard:by:idcard:", "青少年保护身份证"),
    TEEN_GUARD_BY_DAY_COUNT("comm:teenguard:by:daycount:","青少年保护userid每日验证次数");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (TeenGuardCacheEnums teenGuardCacheEnums : TeenGuardCacheEnums.values()) {
            map.put(teenGuardCacheEnums.getKey(), teenGuardCacheEnums.getDesc());
        }
    }

    private TeenGuardCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
