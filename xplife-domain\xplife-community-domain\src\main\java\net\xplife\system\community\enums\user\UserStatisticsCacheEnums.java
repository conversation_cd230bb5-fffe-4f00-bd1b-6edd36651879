package net.xplife.system.community.enums.user;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum UserStatisticsCacheEnums implements ICacheEnums {
    USER_STATISTICS_BY_ID("comm:u:stat:by:id:", "用户统计信息记录");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserStatisticsCacheEnums userStatisticsCacheEnums : UserStatisticsCacheEnums.values()) {
            map.put(userStatisticsCacheEnums.getKey(), userStatisticsCacheEnums.getDesc());
        }
    } 
   
    private UserStatisticsCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
