package net.xplife.system.community.enums.user;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum UserStatisticsV2CacheEnums implements ICacheEnums {
    USER_STATISTICS_BY_ID("comm:u:stat:v2:by:id:", "用户统计信息记录");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserStatisticsV2CacheEnums userStatisticsCacheEnums : UserStatisticsV2CacheEnums.values()) {
            map.put(userStatisticsCacheEnums.getKey(), userStatisticsCacheEnums.getDesc());
        }
    }

    private UserStatisticsV2CacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
