package net.xplife.system.community.enums.webprint;

import net.xplife.system.community.vo.system.WebPagePrintVo;
import net.xplife.system.community.vo.system.WebPrintGroupVo;

import java.util.ArrayList;
import java.util.LinkedHashMap;

public enum WebPrintDefaultEnums {
    BAIDUSOUSUO("百度搜索", "https://www.baidu.com/", "https://m.yoyin.net/api/img/gam/common/webaddress/baidusousuo.png"),
    BAIDUHANYU("百度汉语", "https://hanyu.baidu.com/", "https://m.yoyin.net/api/img/gam/common/webaddress/baiduhanyu.png"),
    DINGXIANGYISHENG("丁香医生", "https://m.dxy.com/", "https://m.yoyin.net/api/img/gam/common/webaddress/dingxiang.png"),
//    YOUDAOFANYI("有道翻译", "http://m.youdao.com/", "https://m.yoyin.net/api/img/gam/common/webaddress/youdao.png"),
    GUSHIWENWANG("古诗文网", "https://m.gushiwen.cn/", "https://m.yoyin.net/api/img/gam/common/webaddress/gushici.png"),
    ZHIHU("知乎", "https://www.zhihu.com/", "https://m.yoyin.net/api/img/gam/common/webaddress/zhihu.png"),
    MEISHIJIECAIPU("美食杰菜谱", "https://m.meishij.net/", "https://m.yoyin.net/api/img/gam/common/webaddress/caipu.png"),
    WEIXINRESOU("微信热搜", "https://weixin.sogou.com", "https://m.yoyin.net/api/img/gam/common/webaddress/weixin.png"),;
    private final String                               name;
    private final String                               url;
    private final String                               icon;
    private static final WebPrintGroupVo defaultWebPrintGroup;

    static {
        defaultWebPrintGroup = new WebPrintGroupVo();
        defaultWebPrintGroup.setId("");
        defaultWebPrintGroup.setIsDefault(1);
        defaultWebPrintGroup.setName("热门网站");
        defaultWebPrintGroup.setPageList(new ArrayList<>());

        for (WebPrintDefaultEnums enumsObj: WebPrintDefaultEnums.values()) {
            WebPagePrintVo wpp = new WebPagePrintVo();
            wpp.setCodeId(1);
            wpp.setGroupId("");
            wpp.setIconUrl(enumsObj.getIcon());
            wpp.setName(enumsObj.getName());
            wpp.setLinkUrl(enumsObj.getUrl());
            wpp.setIsDefault(1);
            defaultWebPrintGroup.getPageList().add(wpp);
        }
    }

    WebPrintDefaultEnums(String name, String url, String icon) {
        this.icon = icon;
        this.name = name;
        this.url = url;
    }

    public String getName() {
        return name;
    }

    public String getUrl() {
        return url;
    }

    public String getIcon() {
        return icon;
    }

    public static WebPrintGroupVo getDefaultWebPrintGroup() {
        return defaultWebPrintGroup;
    }
}
