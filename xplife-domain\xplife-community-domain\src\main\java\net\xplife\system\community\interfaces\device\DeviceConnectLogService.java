package net.xplife.system.community.interfaces.device;

import net.xplife.system.community.entity.device.DeviceConnectLog;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 微服务对外提供api服务层
 *
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface DeviceConnectLogService {
    /***
     * 获取分页
     * @param pageno
     * @param pagesize
     * @return
     */
    @RequestMapping(value = "/community/v1/device/getPage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<DeviceConnectLog> findPage(@RequestParam("pageno") int pageno,
                                           @RequestParam("pagesize") int pagesize,
                                           @RequestParam("otherUserId") String userId,
                                           @RequestParam("deviceSn") String deviceSn,
                                           @RequestParam("deviceName") String deviceName,
                                           @RequestParam("macAddress") String macAddress,
                                           @RequestParam("lastTimeBegin") String lastTimeBegin,
                                           @RequestParam("lastTimeEnd") String lastTimeEnd);

}
