package net.xplife.system.community.interfaces.feed;

import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface FeedCommentService {
    @RequestMapping(value = "/community/v1/comment/sendcomment", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void sendComment(@RequestParam("userId") String userId, @RequestParam("feedId") String feedId, @RequestParam("content") String content, @RequestParam("version") String version);
}