package net.xplife.system.community.interfaces.feed;

import net.xplife.system.web.core.FeignConfiguration;
import net.xplife.system.community.dto.feed.AddFeedNoteDto;
import net.xplife.system.community.dto.feed.FeedInfoDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface FeedNoteService {
    @RequestMapping(value = "/community/v1/feed/addfeednotestudy", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void addFeedNoteForStudy(AddFeedNoteDto dto, @RequestParam("userid") String userId);

    @RequestMapping(value = "/community/v1/feed/addfeednote", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void addFeedNote(AddFeedNoteDto dto, @RequestParam("userid") String userId);

    @RequestMapping(value = "/community/v1/feed/addGiveLikefeed", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void addGiveLike(@RequestParam("feedId") String feedId, @RequestParam("userId") String userId);

    /***
     * 对存在缓存列表中的动态数据，符合时间条件的进行点赞，并删除掉具体的缓存数据
     */
    @RequestMapping(value = "/community/v1/feed/autogivelike", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void autoGiveLike();

    /***
     * 对一天内发布的动态进行自动点赞排位
     */
    @RequestMapping(value = "/community/v1/feed/autoaddgivelikelist", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void autoAddGiveLikeList();

    /***
     * 根据feedId获取对应的动态信息
     * @param feedId
     * @return
     */
    @RequestMapping(value = "/community/v1/feed/getfeednote", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public FeedInfoDto getFeedInfoById(@RequestParam("feedid") String feedId);

    /***
     * 根据feedId删除对应的动态信息
     * @param id
     */
    @RequestMapping(value = "/community/v1/feed/delfeednote", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void delFeedNote(@RequestParam("feedid") String id);
}
