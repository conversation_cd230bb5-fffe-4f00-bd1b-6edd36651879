package net.xplife.system.community.interfaces.knowledge;

import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface KnowledgeArticleService {

    @RequestMapping(value = "/community/v1/knowledge/formulainfo", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Map<String, Object> getKnowledgeMapInfoById(@RequestParam("id") String id);


}
