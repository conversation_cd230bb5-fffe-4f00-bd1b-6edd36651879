package net.xplife.system.community.interfaces.material;

import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import net.xplife.system.community.dto.material.MaterialResourceDto;
import net.xplife.system.community.dto.material.MaterialSelectDto;
import net.xplife.system.community.dto.material.PageListDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Locale;

@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface MaterialOverSeaService {

    @RequestMapping(value = "/community/v1/materialoversea/getmateriallist", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<MaterialSelectDto> getMaterialOverSeaList(Locale locale);

    @RequestMapping(value = "/community/v1/materialoversea/getpagelist", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Page<PageListDto> getPagelistOverSea(@RequestParam("id") String id, @RequestParam("pageno") int pageno, @RequestParam("pagesize") int pagesize);

    @RequestMapping(value = "/community/v1/materialoversea/saveorupdate", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void saveOrUpdateResource(@RequestParam("id") String id, @RequestParam("mId") String mId, @RequestParam("length") String length, @RequestParam("picDto") String picDto, @RequestParam("placeType") String placeType, @RequestParam("isNew") String isNew);

    @RequestMapping(value = "/community/v1/materialoversea/del", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void delResource(@RequestParam("id") String id);


    @RequestMapping(value = "/community/v1/materialoversea/getmaterial", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    MaterialResourceDto getMaterial(@RequestParam("id") String id);

    @RequestMapping(value = "/community/v1/materialoversea/addMaterial", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void addMaterial(@RequestParam("name") String name,@RequestParam("zhTitle") String zhTitle,@RequestParam("enTitle") String enTitle,@RequestParam("com") String com,@RequestParam("type") String type,@RequestParam("subType") String subType);

    @RequestMapping(value = "/community/v1/materialoversea/updateMaterial", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void updateMaterial(@RequestParam("id") String id, @RequestParam("zhTitle") String zhTitle, @RequestParam("enTitle") String enTitle);

    @RequestMapping(value = "/community/v1/materialoversea/deleteMaterial", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void deleteMaterial(@RequestParam("id") String id);
}
