package net.xplife.system.community.interfaces.miles;

import net.xplife.system.web.core.FeignConfiguration;
import net.xplife.system.community.dto.system.PrintMileageDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2020/12/21 12:00
 */
@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface PrintMileageService {
    /***
     * 根据日期“yyyyMMdd”获取所有用户的打印里程数据
     * @param daydate
     * @return
     */
    @RequestMapping(value = "/community/v1/mileage/getallusermilebydate", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<Map<String, Object>> getUserMile(@RequestParam("daydate") String daydate);

    /***
     * 根据日期“yyyyMMdd”获取当前用户的打印里程数值，单位毫米
     * @param day
     * @return
     */
    @RequestMapping(value = "/community/v1/mileage/getDayMile", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Double getUserPrintMileageByDay(@RequestParam("day") String day, @RequestParam("userId") String userId);

    @RequestMapping(value = "/community/v1/mileage/rankingyear", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<PrintMileageDto> getRankingYear();

    @RequestMapping(value = "/community/v1/mileage/rankinguser", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Map<String, Object> getRankingUser(@RequestParam("userId") String userId);
}
