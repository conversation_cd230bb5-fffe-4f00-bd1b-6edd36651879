package net.xplife.system.community.interfaces.study;

import net.xplife.system.community.entity.study.InitialTrainingDict;
import net.xplife.system.community.entity.study.InitialTrainingInfo;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 微服务对外提供api服务层
 *
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface InitialTrainingDictService {
    /***
     * 获取分页
     * @param pageno
     * @param pagesize
     * @param type
     * @return
     */
    @RequestMapping(value = "/community/v1/initialdict/findPage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<InitialTrainingDict> findPage(@RequestParam("pageno") int pageno, @RequestParam("pagesize") int pagesize, @RequestParam("type") String type);

    /***
     * 删除
     * @param id
     */
    @RequestMapping(value = "/community/v1/initialdict/del", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void delete(@RequestParam("id") String id);

    /**
     * 新增或修改
     * @param initialTrainingDict
     */
    @RequestMapping(value = "/community/v1/initialdict/addOrUpdate", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String saveOrUpdate(@RequestBody InitialTrainingDict initialTrainingDict);

    /***
     * 获取所有类型的列表
     * @return
     */
    @RequestMapping(value = "/community/v1/initialdict/findDistinctTypeList", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<Map<String, String>> getDistinctType();

}
