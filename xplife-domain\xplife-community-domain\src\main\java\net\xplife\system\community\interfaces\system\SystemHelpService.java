package net.xplife.system.community.interfaces.system;

import net.xplife.system.community.entity.system.SystemHelpItem;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date ：Created in 2021/5/31 8:52
 * @description：
 * @modified By：
 * @version: $
 */
@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface SystemHelpService {

    @RequestMapping(value = "/community/v1/help/deleteitem", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void deleteById(@RequestParam("id") String id);

//    @RequestMapping(value = "/community/v1/help/additem", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    public void addItem(@RequestParam("title") String title, @RequestParam("url") String url, @RequestParam("sortNum") String sortNum, @RequestParam("type") String type);

    @RequestMapping(value = "/community/v1/help/additem", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void addItem(@RequestBody SystemHelpItem item);

    @RequestMapping(value = "/community/v1/help/updateitem", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void updateItem(@RequestBody SystemHelpItem item);

    @RequestMapping(value = "/community/v1/help/getPage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<SystemHelpItem> findPage(@RequestParam("pageno") int pageno, @RequestParam("pagesize") int pagesize, @RequestParam("type") String type, @RequestParam("printerType") String printerType);

}
