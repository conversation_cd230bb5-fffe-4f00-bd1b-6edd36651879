package net.xplife.system.community.interfaces.words;

import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface WrodTypeService {
    @RequestMapping(value = "/community/v1/feed/checkHadShield", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String initWordType(@RequestParam("shieldUserId") String shieldUserId);
}
