package net.xplife.system.community.utils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.xplife.system.web.common.WebKit;
public class SignWeiXinUtil {
    /**
     * 获取微信session_key
     */
    @SuppressWarnings({ "unchecked", "deprecation"})
    public static String getSRSessionKey(String code, String appid, String appSecret) {
        try {
            String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appid + "&secret=" + appSecret + "&js_code=" + code
                    + "&grant_type=authorization_code";
            String result = WebKit.getForObject(url);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 获取微信access_token
     */
    @SuppressWarnings({ "unchecked", "deprecation"})
    public static String getWebAccessToken(String code, String appid, String appSecret) {
        try {
            String url = "https://api.weixin.qq.com/sns/oauth2/access_token?grant_type=authorization_code&appid=" + appid + "&secret=" + appSecret + "&code="
                    + code;
            String result = WebKit.getForObject(url);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 获取用户个人信息（UnionID机制）
     */
    @SuppressWarnings({ "unchecked", "deprecation"})
    public static String getUserInfo(String accessToken, String openId) {
        try {
            String url = "https://api.weixin.qq.com/sns/userinfo?access_token=" + accessToken + "&openid=" + openId;
            String result = WebKit.getForObject(url);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 获取微信access_token
     */
    @SuppressWarnings({ "unchecked", "deprecation"})
    public static String getGzAccessToken(String appid, String appSecret) {
        try {
            String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret=" + appSecret;
            String result = WebKit.getForObject(url);
            JSONObject object = JSON.parseObject(result);
            return object.getString("access_token");
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 获取jsapi_ticket
     * 
     * @param accessToken
     */
    @SuppressWarnings({ "unchecked", "deprecation"})
    public static String getGzTicket(String accessToken) {
        try {
            String url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=" + accessToken + "&type=jsapi";
            String result = WebKit.getForObject(url);
            JSONObject object = JSON.parseObject(result);
            return object.getString("ticket");
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
