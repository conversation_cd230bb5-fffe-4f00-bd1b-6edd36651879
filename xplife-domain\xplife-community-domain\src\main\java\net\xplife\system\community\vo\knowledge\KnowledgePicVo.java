package net.xplife.system.community.vo.knowledge;

import java.io.Serializable;

public class KnowledgePicVo  implements Serializable {
    /**
     * 图片信息vo
     */
    private static final long serialVersionUID = 1L;
    private String            pic;                  // 图片地址
    private String            name;                 // 图片名称
    private int               height;               // 高
    private int               width;                // 宽
    private double            size;                 // 大小
    private String            startPoint;           // 开始点
    private String            endPoint;             // 结束点
    private int               sort;                 // 排名序号
    private int               printFlag;            // 是否参与打印
    private int               canRead;              // 不分享能否查看，0不能；1能

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public double getSize() {
        return size;
    }

    public void setSize(double size) {
        this.size = size;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getPrintFlag() {
        return printFlag;
    }

    public void setPrintFlag(int printFlag) {
        this.printFlag = printFlag;
    }

    public int getCanRead() {
        return canRead;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCanRead(int canRead) {
        this.canRead = canRead;
    }
}
