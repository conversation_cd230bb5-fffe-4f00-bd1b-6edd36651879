package net.xplife.system.community.vo.sq;
import java.io.Serializable;
public class LabelVo implements Serializable {
    /**
     * 收藏标签vo
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 生成随机ID
    private String            name;                 // 名称
    private String            icon;                 // 图标
    private int               count;                // 数量

    public LabelVo() {
        super();
    }

    public LabelVo(String id, String name, String icon, int count) {
        super();
        this.id = id;
        this.name = name;
        this.icon = icon;
        this.count = count;
    }

    public LabelVo(String id, String name, String icon) {
        super();
        this.id = id;
        this.name = name;
        this.icon = icon;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
