package net.xplife.system.community.vo.system;

import java.io.Serializable;
import java.util.List;

public class UserFeedbackVo implements Serializable {

    public UserFeedbackVo() {
        super();
    }

    public UserFeedbackVo(List<UserFeedbackItem> softwares, List<UserFeedbackItem> hardwares) {
        super();
        this.softwares = softwares;
        this.hardwares = hardwares;
    }

    /**
     * 软件问题列表
     */
    private List<UserFeedbackItem> softwares;

    /**
     * 硬件问题列表
     */
    private List<UserFeedbackItem> hardwares;

    public List<UserFeedbackItem> getSoftwares() {
        return softwares;
    }

    public void setSoftwares(List<UserFeedbackItem> softwares) {
        this.softwares = softwares;
    }

    public List<UserFeedbackItem> getHardwares() {
        return hardwares;
    }

    public void setHardwares(List<UserFeedbackItem> hardwares) {
        this.hardwares = hardwares;
    }

}
