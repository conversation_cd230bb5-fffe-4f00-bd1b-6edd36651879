package net.xplife.system.community.vo.system;

import java.io.Serializable;
import java.util.List;

public class UserFeedbackXVo implements Serializable {

    public UserFeedbackXVo() {
        super();
    }

    public UserFeedbackXVo(String userId, String type, String qtype, String content, List<String> images, String result) {
        super();
        this.userId = userId;
        this.type = type;
        this.qtype = qtype;
        this.content = content;
        this.images = images;
        this.result = result;
    }

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 反馈类型
     */
    private String type;
    /**
     * 问题类型
     */
    private String qtype;
    /**
     * 反馈文本内容
     */
    private String content;
    /**
     * 反馈图片地址列表
     */
    private List<String> images;
    /**
     * 系统响应结果信息
     */
    private String result;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getQtype() {
        return qtype;
    }

    public void setQtype(String qtype) {
        this.qtype = qtype;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

}