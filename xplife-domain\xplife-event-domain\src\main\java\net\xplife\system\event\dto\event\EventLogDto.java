package net.xplife.system.event.dto.event;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
public class EventLogDto implements Serializable {
    /**
     * api日志数据采集dto
     */
    private static final long serialVersionUID = 1L;
    private String            requestId;            // 请求ID
    private String            method;               // 方法
    private String            host;                 // host
    private String            remoteIp;             // 客户端真实 IP
    private String            intranetIp;           // slb的内网IP
    @JSONField(format = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date              requestDate;          // 请求时间
    private String            uri;                  // 请求uri
    private String            scheme;               // scheme
    private String            contentType;          // 请求类型
    private String            head;                 // 请求头
    private String            param;                // 请求参数
    private String            result;               // 响应结果
    private String            type;                 // 类型
    private String            uaFlag;               // ua
    private String            source;               // 来源

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getUaFlag() {
        return uaFlag;
    }

    public void setUaFlag(String uaFlag) {
        this.uaFlag = uaFlag;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    public String getIntranetIp() {
        return intranetIp;
    }

    public void setIntranetIp(String intranetIp) {
        this.intranetIp = intranetIp;
    }

    public Date getRequestDate() {
        return requestDate;
    }

    public void setRequestDate(Date requestDate) {
        this.requestDate = requestDate;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getScheme() {
        return scheme;
    }

    public void setScheme(String scheme) {
        this.scheme = scheme;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }
}
