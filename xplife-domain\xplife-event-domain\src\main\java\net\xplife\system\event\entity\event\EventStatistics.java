package net.xplife.system.event.entity.event;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 事件配置表
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "V1_EventStatistics")
public class EventStatistics extends IdEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_EventStatistics";
    private String             type;                                // 主类型
    private String             subType;                             // 副类型
    private String             name;                                // 事件名称
    private String             cacheKey;                            // 缓存key
    private String             eventType;                           // 事件类型
    private String             code;                                // 事件编码
    private String             itemId;                              // 商城商品ID

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCacheKey() {
        return cacheKey;
    }

    public void setCacheKey(String cacheKey) {
        this.cacheKey = cacheKey;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }
}
