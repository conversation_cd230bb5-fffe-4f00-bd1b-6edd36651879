package net.xplife.system.event.entity.event;
import java.util.Date;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.AnalyEntity;
/**
 * 请求日志
 * 
 * <AUTHOR> 2018年6月24日
 */
@Document(collection = "ODS_RequestLog")
public class RequestLog extends AnalyEntity {
    /**
     * 
     */
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "ODS_RequestLog";
    public static final String TYPE_FIELD       = "type";
    @Indexed(name = "_requestid_")
    private String             requestId;                         // 请求ID
    private String             method;                            // 方法
    private String             host;                              // host
    private String             remoteIp;                          // 客户端真实 IP
    private String             intranetIp;                        // slb的内网IP
    @Indexed(name = "_requestdate_")
    private Date               requestDate;                       // 请求时间
    @Indexed(name = "_uri_")
    private String             uri;                               // 请求uri
    private String             scheme;                            // scheme
    private String             contentType;                       // 请求类型
    private String             head;                              // 请求头
    private String             param;                             // 请求参数
    private String             result;                            // 响应结果
    @Indexed(name = "_type_")
    private String             type;                              // 类型
    @Indexed(name = "_userid_")
    private String             userId;                            // 用户ID
    private String             deviceId;                          // 设备ID
    private String             uaFlag;                            // ua

    public String getUaFlag() {
        return uaFlag;
    }

    public void setUaFlag(String uaFlag) {
        this.uaFlag = uaFlag;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    public String getIntranetIp() {
        return intranetIp;
    }

    public void setIntranetIp(String intranetIp) {
        this.intranetIp = intranetIp;
    }

    public Date getRequestDate() {
        return requestDate;
    }

    public void setRequestDate(Date requestDate) {
        this.requestDate = requestDate;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getScheme() {
        return scheme;
    }

    public void setScheme(String scheme) {
        this.scheme = scheme;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
}
