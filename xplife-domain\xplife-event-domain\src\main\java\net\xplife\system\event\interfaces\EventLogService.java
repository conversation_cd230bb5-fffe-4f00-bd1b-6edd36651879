package net.xplife.system.event.interfaces;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import net.xplife.system.event.dto.event.EventLogDto;
import net.xplife.system.web.core.FeignConfiguration;
/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR> 2018年6月25日
 */
@FeignClient(name = "yoyin-event", configuration = FeignConfiguration.class)
public interface EventLogService {
    /**
     * api调用日志收录
     * 
     * @return
     */
    @RequestMapping(value = "/event/v1/log/saveapilog", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void saveApiLog(@RequestBody EventLogDto eventLogDto);
}
