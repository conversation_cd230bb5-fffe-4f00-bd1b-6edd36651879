package net.xplife.system.event.interfaces;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import net.xplife.system.event.dto.event.EventLogDto;
// import net.xplife.system.kafka.common.KafkaConst;
// import net.xplife.system.kafka.core.KafkaKit;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.common.JsonKit;
import net.xplife.system.web.config.CommonProperties;
import net.xplife.system.web.dto.QueueMessageDto;
import net.xplife.system.web.queue.ConsumeInterface;
import net.xplife.system.web.queue.QueueMessage;
/**
 * 事件日志服务类
 * 
 * <AUTHOR> 2018年6月25日
 */
@Service
public class RequestEventLogBuService implements ConsumeInterface {
    @Autowired
    private EventLogService  eventLogService;
    @Autowired
    private CommonProperties commonProperties;

    @Override
    public void consumeQueue(QueueMessage queueMessage) {
        if (ToolsKit.isNotEmpty(queueMessage.getBody())) {
            QueueMessageDto queueMessageDto = JsonKit.jsonParseObject(queueMessage.getBody() + StringUtils.EMPTY, QueueMessageDto.class);
            EventLogDto eventLogDto = new EventLogDto();
            ToolsKit.Bean.copyProperties(queueMessageDto, eventLogDto);
            if (commonProperties.getKafkaProductEnable()) {
                // KafkaKit.send(KafkaConst.P_EVENT_LOG_TOPICS, eventLogDto);
            } else {// http发送请求
                eventLogService.saveApiLog(eventLogDto);
            }
        }
    }
}
