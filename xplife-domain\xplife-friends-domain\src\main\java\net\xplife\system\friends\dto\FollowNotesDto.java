package net.xplife.system.friends.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

public class FollowNotesDto implements Serializable {
    /**
     * 关注消息Dto
     */
    private static final long serialVersionUID = 1L;
    private String            noteFlag;             // 关注消息flag
    private String            userId;               // 用户ID
    private String            userName;             // 用户名称
    private String            userPic;              // 用户头像
    private String            sex;                  // 用户性别
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;           // 创建 时间
    private String            title;                // 标题
    private int               hasMsg;               // 是否有新消息
    private int isFollow;
    private int               isOfficial;           // 0:普通用户；1:官方认证
    private Map<String, Object> userTitleObj;       // 用户头衔对象，id，name，nameUrl，borderUrl


    public int getHasMsg() {
        return hasMsg;
    }

    public void setHasMsg(int hasMsg) {
        this.hasMsg = hasMsg;
    }

    public String getNoteFlag() {
        return noteFlag;
    }

    public void setNoteFlag(String noteFlag) {
        this.noteFlag = noteFlag;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPic() {
        return userPic;
    }

    public void setUserPic(String userPic) {
        this.userPic = userPic;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getIsFollow() {
        return isFollow;
    }

    public void setIsFollow(int isFollow) {
        this.isFollow = isFollow;
    }

    public int getIsOfficial() {return isOfficial;}

    public void setIsOfficial(int isOfficial) {this.isOfficial = isOfficial;}

    public Map<String, Object> getUserTitleObj() {
        return userTitleObj;
    }

    public void setUserTitleObj(Map<String, Object> userTitleObj) {
        this.userTitleObj = userTitleObj;
    }

}