package net.xplife.system.friends.dto;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
public class FriendsApplyDto implements Serializable {
    /**
     * 好友申请列表dto--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            userId;               // 用户ID
    private String            nickName;             // 用户昵称
    private String            sex;                  // 用户性别
    private String            content;              // 描述
    private String            pic;                  // 用户头像
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              applyDate;            // 申请时间
    private int               applyStatus;          // 申请状态

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public int getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(int applyStatus) {
        this.applyStatus = applyStatus;
    }
}
