package net.xplife.system.friends.dto;
import java.io.Serializable;
import java.util.Map;

public class FriendsDto implements Serializable {
    /**
     * 好友列表dto--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private String            lastId;               // 最后一条记录ID
    private int               codeId;               // 用户codeId
    private String            userId;               // 用户ID
    private String            nickName;             // 用户昵称
    private String            pic;                  // 用户头像
    private int               isFriend;             // 是否是好友
    private int               isShield;             // 是否已屏蔽
    private String            shieldId;             // 如果屏蔽，获取屏蔽的id
    private int               isFollow;             // 关注状态，0为关注，1已关注，2相互关注
    private int               isOfficial;           // 是否官方账号-用户 0否1是
    private Map<String, Object> userTitleObj;       // 用户头衔对象，id，name，nameUrl，borderUrl

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getLastId() {
        return lastId;
    }

    public void setLastId(String lastId) {
        this.lastId = lastId;
    }

    public int getCodeId() {
        return codeId;
    }

    public void setCodeId(int codeId) {
        this.codeId = codeId;
    }

    public int getIsFriend() {
        return isFriend;
    }

    public void setIsFriend(int isFriend) {
        this.isFriend = isFriend;
    }

    public int getIsShield() {
        return isShield;
    }

    public void setIsShield(int isShield) {
        this.isShield = isShield;
    }

    public String getShieldId() {
        return shieldId;
    }

    public void setShieldId(String shieldId) {
        this.shieldId = shieldId;
    }

    public int getIsFollow() {
        return isFollow;
    }

    public void setIsFollow(int isFollow) {
        this.isFollow = isFollow;
    }

    public int getIsOfficial() {return isOfficial; }

    public void setIsOfficial(int isOfficial) { this.isOfficial = isOfficial; }
    public Map<String, Object> getUserTitleObj() {
        return userTitleObj;
    }

    public void setUserTitleObj(Map<String, Object> userTitleObj) {
        this.userTitleObj = userTitleObj;
    }
}
