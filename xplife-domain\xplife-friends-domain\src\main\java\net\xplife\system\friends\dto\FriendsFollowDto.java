package net.xplife.system.friends.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 好友关注数据转换对象类
 */
public class FriendsFollowDto implements Serializable {

    /**
     * 好友关注列表dto--服务器返回
     */
    private static final long serialVersionUID = 1L;
    /**
     * 最后一条记录ID
     */
    private String lastId;
    /**
     * 用户codeId
     */
    private int codeId;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 用户头像
     */
    private String pic;
    /**
     * 是否关注
     */
    private int isFollow;

    /**
     * 0：普通用户；1：官方认证
     * */
    private int isOfficial;

    private Map<String, Object> userTitleObj;       // 用户头衔对象，id，name，nameUrl，borderUrl

    private int               hasMsg;               // 是否有新消息
    private String            noteFlag;             // 关注消息flag
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;           // 创建 时间

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getLastId() {
        return lastId;
    }

    public void setLastId(String lastId) {
        this.lastId = lastId;
    }

    public int getCodeId() {
        return codeId;
    }

    public void setCodeId(int codeId) {
        this.codeId = codeId;
    }

    public int getIsFollow() {
        return isFollow;
    }

    public void setIsFollow(int isFollow) {
        this.isFollow = isFollow;
    }

    public int getHasMsg() {
        return hasMsg;
    }

    public void setHasMsg(int hasMsg) {
        this.hasMsg = hasMsg;
    }

    public String getNoteFlag() {
        return noteFlag;
    }

    public void setNoteFlag(String noteFlag) {
        this.noteFlag = noteFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public FriendsFollowDto() {
        // 已读取
        this.hasMsg = 0;
    }

    public int getIsOfficial() {
        return isOfficial;
    }

    public void setIsOfficial(int isOfficial) {
        this.isOfficial = isOfficial;
    }
    public Map<String, Object> getUserTitleObj() {
        return userTitleObj;
    }

    public void setUserTitleObj(Map<String, Object> userTitleObj) {
        this.userTitleObj = userTitleObj;
    }
}
