package net.xplife.system.friends.dto;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import com.alibaba.fastjson.annotation.JSONField;
public class NotesDetailsDto implements Serializable {
    /**
     * 小纸条详情Dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            userId;               // 用户ID
    private String            userName;             // 用户名称
    private String            userPic;              // 用户头像
    private String            sex;                  // 用户性别
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createTime;           // 创建 时间
    private int               isRead;               // 是否已读
    private PicDto            pic;                  // 图片地址
    private int               type;                 // 显示类型 0公共显示 1单方显示
    private String            content;              // 显示内容
    private int               isOfficial;           // 是否官方认证，0：普通；1：官方认证
    private Map<String, Object> userTitleObj;       // 用户头衔对象，id，name，nameUrl，borderUrl

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public PicDto getPic() {
        return pic;
    }

    public void setPic(PicDto pic) {
        this.pic = pic;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPic() {
        return userPic;
    }

    public void setUserPic(String userPic) {
        this.userPic = userPic;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public int getIsRead() {
        return isRead;
    }

    public void setIsRead(int isRead) {
        this.isRead = isRead;
    }

    public int getIsOfficial() { return isOfficial; }

    public void setIsOfficial(int isOfficial) {
        this.isOfficial = isOfficial;
    }

    public Map<String, Object> getUserTitleObj() {
        return userTitleObj;
    }

    public void setUserTitleObj(Map<String, Object> userTitleObj) {
        this.userTitleObj = userTitleObj;
    }
}
