package net.xplife.system.friends.dto;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
public class UserFriendsDto implements Serializable {
    /**
     * 好友列表dto--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private int               codeId;               // 用户codeId
    private String            userId;               // 用户ID
    private String            nickName;             // 用户昵称
    private String            pic;                  // 用户头像
    private int               fCodeId;              // 好友用户codeId
    private String            fUserId;              // 好友用户ID
    private String            fNickName;            // 好友用户昵称
    private String            fPic;                 // 好友用户头像
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createTime;           // 创建时间

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public int getCodeId() {
        return codeId;
    }

    public void setCodeId(int codeId) {
        this.codeId = codeId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public int getfCodeId() {
        return fCodeId;
    }

    public void setfCodeId(int fCodeId) {
        this.fCodeId = fCodeId;
    }

    public String getfUserId() {
        return fUserId;
    }

    public void setfUserId(String fUserId) {
        this.fUserId = fUserId;
    }

    public String getfNickName() {
        return fNickName;
    }

    public void setfNickName(String fNickName) {
        this.fNickName = fNickName;
    }

    public String getfPic() {
        return fPic;
    }

    public void setfPic(String fPic) {
        this.fPic = fPic;
    }
}
