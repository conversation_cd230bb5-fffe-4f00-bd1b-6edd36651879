package net.xplife.system.friends.dto;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import com.alibaba.fastjson.annotation.JSONField;
public class UserNotesDto implements Serializable {
    /**
     * 用户小纸条Dto
     */
    private static final long serialVersionUID = 1L;
    private String            noteFlag;             // 用户小纸条flag
    private String            userId;               // 用户ID
    private String            userName;             // 用户名称
    private String            userPic;              // 用户头像
    private String            sex;                  // 用户性别
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createTime;           // 创建 时间
    private String            title;                // 标题
    private int               hasMsg;               // 是否有新消息
    private int               isOfficial;           // 0：普通用户；1：官方认证
    private Map<String, Object> userTitleObj;       // 用户头衔对象，id，name，nameUrl，borderUrl
    private int               isFeedbackColumn;     // 是否为官方反馈栏目
    private int               typeColumn;           // 0 表示原来的纸条消息，1：官方反馈消息，2：系统的官方消息，   isFeedbackColumn弃用
    private String            fmtTime;              // 格式化时间
    private int               count;                //短信数量

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getHasMsg() {
        return hasMsg;
    }

    public void setHasMsg(int hasMsg) {
        this.hasMsg = hasMsg;
    }

    public String getNoteFlag() {
        return noteFlag;
    }

    public void setNoteFlag(String noteFlag) {
        this.noteFlag = noteFlag;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPic() {
        return userPic;
    }

    public void setUserPic(String userPic) {
        this.userPic = userPic;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getIsOfficial() { return isOfficial; }

    public void setIsOfficial(int isOfficial) { this.isOfficial = isOfficial; }
    public Map<String, Object> getUserTitleObj() {
        return userTitleObj;
    }

    public void setUserTitleObj(Map<String, Object> userTitleObj) {
        this.userTitleObj = userTitleObj;
    }

    public int getIsFeedbackColumn() {
        return isFeedbackColumn;
    }

    public void setIsFeedbackColumn(int isFeedbackColumn) {
        this.isFeedbackColumn = isFeedbackColumn;
    }

    public String getFmtTime() {
        return fmtTime;
    }

    public void setFmtTime(String fmtTime) {
        this.fmtTime = fmtTime;
    }

    public int getTypeColumn() {
        return typeColumn;
    }

    public void setTypeColumn(int typeColumn) {
        this.typeColumn = typeColumn;
    }
}
