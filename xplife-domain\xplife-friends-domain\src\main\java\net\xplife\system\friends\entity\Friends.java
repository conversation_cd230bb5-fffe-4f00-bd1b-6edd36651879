package net.xplife.system.friends.entity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 好友关系表
 */
@Document(collection = "V1_Friends")
public class Friends extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_Friends";
    public static final String USER_ID_FIELD    = "userId";
    public static final String FRIEND_ID_FIELD  = "friendId";
    @Indexed(name = "_userid_")
    private String             userId;                         // 用户ID
    @Indexed(name = "_friendid_")
    private String             friendId;                       // 好友ID
    private String             noteFlag;                       // 用户小纸条标识

    public String getNoteFlag() {
        return noteFlag;
    }

    public void setNoteFlag(String noteFlag) {
        this.noteFlag = noteFlag;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFriendId() {
        return friendId;
    }

    public void setFriendId(String friendId) {
        this.friendId = friendId;
    }
}
