package net.xplife.system.friends.entity;
import java.util.Date;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 好友申请表
 */
@Document(collection = "V1_FriendsApply")
public class FriendsApply extends IdEntity {
    private static final long  serialVersionUID    = 1L;
    public static final String COLL                = "V1_FriendsApply";
    public static final String USER_ID_FIELD       = "userId";
    public static final String APPLY_USER_ID_FIELD = "applyUserId";
    @Indexed(name = "_userid_")
    private String             userId;
    @Indexed(name = "_applyuserid_")
    private String             applyUserId;                            // 申请人ID
    private Date               applyDate;                              // 申请时间
    private int                applyStatus;                            // 申请状态

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getApplyUserId() {
        return applyUserId;
    }

    public void setApplyUserId(String applyUserId) {
        this.applyUserId = applyUserId;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public int getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(int applyStatus) {
        this.applyStatus = applyStatus;
    }
}
