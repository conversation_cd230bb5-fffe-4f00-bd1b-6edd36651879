package net.xplife.system.friends.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 用户关注关系表
 */
@Document(collection = "V1_FriendsFollow")
public class FriendsFollow extends IdEntity {

    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_FriendsFollow";
    public static final String USER_ID_FIELD    = "userId";
    public static final String FRIEND_ID_FIELD  = "friendId";

    /**
     * 用户ID（本用户）
     */
    @Indexed(name = "_userid_")
    private String userId;
    /**
     * 好友ID（关注用户）
     */
    @Indexed(name = "_friendid_")
    private String friendId;
    /**
     * 用户小纸条标识
     */
    private String noteFlag;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFriendId() {
        return friendId;
    }

    public void setFriendId(String friendId) {
        this.friendId = friendId;
    }

    public String getNoteFlag() {
        return noteFlag;
    }

    public void setNoteFlag(String noteFlag) {
        this.noteFlag = noteFlag;
    }

}
