package net.xplife.system.friends.entity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 小纸条详情
 */
@Document(collection = "V1_NotesDetails")
public class NotesDetails extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_NotesDetails";
    public static final String NOTES_FLAG_FIELD = "noteFlag";
    @Indexed(name = "_noteflag_")
    private String             noteFlag;                            // 小纸条flag
    private String             userId;                              // 用户ID
    private String             resId;                               // 资源ID
    private int                isRead;                              // 是否已读
    private int                type;                                // 显示类型 0公共显示 1单方显示
    private String             content;                             // 显示内容
    private String             recvId;                              // 接收人ID
    private String             detailTag;                           // 小纸条详情flag

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNoteFlag() {
        return noteFlag;
    }

    public void setNoteFlag(String noteFlag) {
        this.noteFlag = noteFlag;
    }

    public String getResId() {
        return resId;
    }

    public void setResId(String resId) {
        this.resId = resId;
    }

    public int getIsRead() {
        return isRead;
    }

    public void setIsRead(int isRead) {
        this.isRead = isRead;
    }

    public String getRecvId() {
        return recvId;
    }

    public void setRecvId(String recvId) {
        this.recvId = recvId;
    }

    public String getDetailTag() {
        return detailTag;
    }

    public void setDetailTag(String detailTag) {
        this.detailTag = detailTag;
    }
}
