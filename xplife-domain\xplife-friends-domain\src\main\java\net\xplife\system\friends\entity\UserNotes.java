package net.xplife.system.friends.entity;
import java.util.Date;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 用户小纸条
 */
@Document(collection = "V1_UserNotes")
public class UserNotes extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_UserNotes";
    public static final String USER_ID_FIELD    = "userId";
    public static final String FRIEND_ID_FIELD  = "friendId";
    public static final String FLAG_FIELD       = "flag";
    public static final String LAST_DATE_FIELD  = "lastDate";
    public static final String HAS_MSG_FIELD    = "hasMsg";
    public static final String TITLE_FIELD    = "title";
    private String             userId;                           // 用户 ID
    private String             friendId;                         // 好友ID
    private Date               lastDate;                         // 最后更新时间
    private int                hasMsg;                           // 是否有新消息
    private String             title;                            // 标题
    @Indexed(name = "_flag_")
    private String             flag;                             // 标识
    private String             lastId;                           // 最近一次记录ID
    @Indexed(name = "_removeid_")
    private String             removeId;                         // 删除记录时的ID

    public String getRemoveId() {
        return removeId;
    }

    public void setRemoveId(String removeId) {
        this.removeId = removeId;
    }

    public String getFriendId() {
        return friendId;
    }

    public void setFriendId(String friendId) {
        this.friendId = friendId;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getLastId() {
        return lastId;
    }

    public void setLastId(String lastId) {
        this.lastId = lastId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getLastDate() {
        return lastDate;
    }

    public void setLastDate(Date lastDate) {
        this.lastDate = lastDate;
    }

    public int getHasMsg() {
        return hasMsg;
    }

    public void setHasMsg(int hasMsg) {
        this.hasMsg = hasMsg;
    }
}
