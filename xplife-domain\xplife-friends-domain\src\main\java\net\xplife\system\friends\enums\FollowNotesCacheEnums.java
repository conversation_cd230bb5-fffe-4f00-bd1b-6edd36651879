package net.xplife.system.friends.enums;

import net.xplife.system.tools.common.enums.ICacheEnums;
import java.util.LinkedHashMap;

public enum FollowNotesCacheEnums implements ICacheEnums {
    FOLLOW_NOTES_HAS_MSG_ID_LIST("fds:fo:no:hm:by:list:", "用户关注消息的ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (FollowNotesCacheEnums cacheEnums : FollowNotesCacheEnums.values()) {
            map.put(cacheEnums.getKey(), cacheEnums.getDesc());
        }
    }

    private FollowNotesCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
