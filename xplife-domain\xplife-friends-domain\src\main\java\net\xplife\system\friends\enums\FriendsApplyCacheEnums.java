package net.xplife.system.friends.enums;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum FriendsApplyCacheEnums implements ICacheEnums {
    FRIENDS_APPLY_BY_ID("fds:fdap:by:id:", "好友申请记录"),
    FRIENDS_APPLY_BY_UF_ID("fds:fdap:by:uf:id:", "好友申请记录"),
    FRIENDS_APPLY_ID_LIST("fds:fdap:by:list:", "好友申请数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (FriendsApplyCacheEnums friendsApplyCacheEnums : FriendsApplyCacheEnums.values()) {
            map.put(friendsApplyCacheEnums.getKey(), friendsApplyCacheEnums.getDesc());
        }
    } 
 
    private FriendsApplyCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
