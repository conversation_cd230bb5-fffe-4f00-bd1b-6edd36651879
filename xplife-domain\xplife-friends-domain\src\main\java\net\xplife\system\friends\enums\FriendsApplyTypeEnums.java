package net.xplife.system.friends.enums;
import java.util.LinkedHashMap;
public enum FriendsApplyTypeEnums {
    NO_FRIEND(0, "不是好友"),
    ALL_FRIEND(1, "相互好友"), 
    TIME_OUT(2, "已过期");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<Integer, String>();
        for (FriendsApplyTypeEnums friendsApplyTypeEnums : FriendsApplyTypeEnums.values()) {
            map.put(friendsApplyTypeEnums.getValue(), friendsApplyTypeEnums.getDesc());
        }
    } 

    private FriendsApplyTypeEnums(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
