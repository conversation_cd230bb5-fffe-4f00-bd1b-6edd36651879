package net.xplife.system.friends.enums;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum FriendsCacheEnums implements ICacheEnums {
    FRIENDS_BY_ID("fds:fd:by:id:", "好友记录"),
    FRIENDS_BY_UF_ID("fds:fd:by:uf:id:", "好友记录"),
    FRIENDS_ID_LIST("fds:fd:by:list:", "好友数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (FriendsCacheEnums friendsCacheEnums : FriendsCacheEnums.values()) {
            map.put(friendsCacheEnums.getKey(), friendsCacheEnums.getDesc());
        }
    }
 
    private FriendsCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
