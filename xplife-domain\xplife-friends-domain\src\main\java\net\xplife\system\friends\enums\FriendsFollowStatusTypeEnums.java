package net.xplife.system.friends.enums;

import java.util.LinkedHashMap;

/**
 * 用户好友关注状态枚举类
 */
public enum  FriendsFollowStatusTypeEnums {

    NONE(0, "未关注"),
    FOLLOW(1, "已关注"),
    MUTUAL_FOLLOW(2, "互相关注");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<Integer, String>();
        for (FriendsFollowStatusTypeEnums statusTypeEnums : FriendsFollowStatusTypeEnums.values()) {
            map.put(statusTypeEnums.getValue(), statusTypeEnums.getDesc());
        }
    }

    private FriendsFollowStatusTypeEnums(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }

}
