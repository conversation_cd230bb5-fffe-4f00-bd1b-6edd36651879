package net.xplife.system.friends.enums;

import java.util.LinkedHashMap;

/**
 * 用户好友关注状态枚举类
 */
public enum FriendsNotesMsgEnums {

    SEND_ONE_MSG("friend_notes_send_msg", "已传了小纸条"),
    RECEIVED_ONE_MSG("friend_notes_received_msg", "给你传了小纸条"),
    NO_MSG("friend_notes_no_msg", "暂无纸条消息"),
    OFFICIAL_MSG_HAS("friend_notes_official_has_new", "叮~您有一条新消息 请查收"),
    OFFICIAL_MSG_NOHAS("friend_notes_official_no_new","暂无新消息"),
    FEEDBACK_MSG_HAS("friend_notes_feedback_has_new", "叮~您有一条新消息 请查收"),
    FEEDBACK_MSG_NOHAS("friend_notes_feedback_no_new", "暂无新消息");


    private final String                                   value;
    private final String                                desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (FriendsNotesMsgEnums statusTypeEnums : FriendsNotesMsgEnums.values()) {
            map.put(statusTypeEnums.getValue(), statusTypeEnums.getDesc());
        }
    }

    private FriendsNotesMsgEnums(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }

}
