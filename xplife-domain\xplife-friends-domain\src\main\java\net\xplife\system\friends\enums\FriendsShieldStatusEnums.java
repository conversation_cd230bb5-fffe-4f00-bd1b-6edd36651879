package net.xplife.system.friends.enums;

import java.util.LinkedHashMap;

public enum FriendsShieldStatusEnums {
    NO_SHIELD(0, "未屏蔽"),
    SHIELD(1, "已屏蔽");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<Integer, String>();
        for (FriendsShieldStatusEnums friendsShieldStatusEnums : FriendsShieldStatusEnums.values()) {
            map.put(friendsShieldStatusEnums.getValue(), friendsShieldStatusEnums.getDesc());
        }
    }

    private FriendsShieldStatusEnums(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
