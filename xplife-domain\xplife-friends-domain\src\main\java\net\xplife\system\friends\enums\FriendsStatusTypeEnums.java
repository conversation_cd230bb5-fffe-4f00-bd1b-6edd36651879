package net.xplife.system.friends.enums;
import java.util.LinkedHashMap;
public enum FriendsStatusTypeEnums {
    ALL_FRIEND(1, "相互好友"), 
    NO_FRIEND(2, "不是好友");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<Integer, String>();
        for (FriendsStatusTypeEnums friendsStatusTypeEnums : FriendsStatusTypeEnums.values()) {
            map.put(friendsStatusTypeEnums.getValue(), friendsStatusTypeEnums.getDesc());
        }
    }

    private FriendsStatusTypeEnums(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
