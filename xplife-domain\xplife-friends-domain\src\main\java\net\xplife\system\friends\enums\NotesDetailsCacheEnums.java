package net.xplife.system.friends.enums;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum NotesDetailsCacheEnums implements ICacheEnums {
    NOTES_DETAILS_BY_ID("fds:no:de:by:id:", "小纸条详情对象"), 
    NOTES_DETAILS_ID_LIST("fds:no:de:by:list:", "小纸条详情数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (NotesDetailsCacheEnums notesDetailsCacheEnums : NotesDetailsCacheEnums.values()) {
            map.put(notesDetailsCacheEnums.getKey(), notesDetailsCacheEnums.getDesc());
        } 
    }  

    private NotesDetailsCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
