package net.xplife.system.friends.enums;
import java.util.LinkedHashMap;
public enum NotesDetailsTypeEnums {
    ALL(0, "公共显示"), 
    SINGLE(1, "单个显示");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<Integer, String>();
        for (NotesDetailsTypeEnums notesDetailsTypeEnums : NotesDetailsTypeEnums.values()) {
            map.put(notesDetailsTypeEnums.getValue(), notesDetailsTypeEnums.getDesc());
        }
    }

    private NotesDetailsTypeEnums(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
