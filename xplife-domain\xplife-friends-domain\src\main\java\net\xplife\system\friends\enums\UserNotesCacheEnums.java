package net.xplife.system.friends.enums;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum UserNotesCacheEnums implements ICacheEnums {
    USER_NOTES_BY_ID("fds:us:no:by:id:", "用户小纸条对象"), 
    USER_NOTES_BY_USER_ID("fds:us:no:by:uf:id:", "用户小纸条对象"), 
    USER_NOTES_ID_LIST("fds:us:no:by:list:", "用户小纸条数据ID集合"), 
    USER_NOTES_HAS_MSG_ID_LIST("fds:us:no:hm:by:list:", "用户小纸条有消息的ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserNotesCacheEnums userNotesCacheEnums : UserNotesCacheEnums.values()) {
            map.put(userNotesCacheEnums.getKey(), userNotesCacheEnums.getDesc());
        } 
    } 

    private UserNotesCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
