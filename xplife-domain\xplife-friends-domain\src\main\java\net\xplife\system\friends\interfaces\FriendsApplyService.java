package net.xplife.system.friends.interfaces;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import net.xplife.system.web.core.FeignConfiguration;
import net.xplife.system.friends.dto.FriendsApplyDto;
/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-friends", configuration = FeignConfiguration.class)
public interface FriendsApplyService {
    /**
     * 获取用户账号信息
     * 
     * @return
     */
    @RequestMapping(value = "/friend/v1/apply/getfriendapplylist", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<FriendsApplyDto> getFriendsApplyList(@RequestParam("userid") String userId, @RequestParam("pageno") int pageNo,
            @RequestParam("pagesize") int pageSize, @RequestParam("lastid") String lastId);
}
