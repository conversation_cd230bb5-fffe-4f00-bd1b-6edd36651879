package net.xplife.system.friends.interfaces;

import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 微服务对外提供api服务层
 *
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-friends", configuration = FeignConfiguration.class)
public interface FriendsFollowService {

    /**
     * 获取用户账号信息
     *
     * @return
     */
    @RequestMapping(value = "/friend/v1/follow/getFollowStatus", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Integer getFollowStatus(@RequestParam("userid") String userId, @RequestParam("friendid") String friendId);

    @RequestMapping(value = "/friend/v1/follow/addFollow", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void addFriendsFollow(@RequestParam("userid") String userId, @RequestParam("friendid") String friendId);

}
