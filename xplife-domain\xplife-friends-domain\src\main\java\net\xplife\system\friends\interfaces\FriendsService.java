package net.xplife.system.friends.interfaces;
import net.xplife.system.friends.dto.FriendsDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import net.xplife.system.friends.dto.FriendStatusDto;
import net.xplife.system.friends.dto.UserFriendsDto;

/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-friends", configuration = FeignConfiguration.class)
public interface FriendsService {
    /**
     * 获取用户账号信息
     * 
     * @return
     */
    @RequestMapping(value = "/friend/v1/friends/getfriendstatus", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public FriendStatusDto getFriendStatus(@RequestParam("userid") String userId, @RequestParam("friendid") String friendId);

    /**
     * 获取好友列表
     * 
     * @return
     */
    @RequestMapping(value = "/friend/v1/friends/findfriendspage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<UserFriendsDto> findFriendsPage(@RequestParam("codeid") String codeId, @RequestParam("pageno") int pageNo,
            @RequestParam("pagesize") int pageSize);

    /**
     * 删除好友
     * 
     * @return
     */
    @RequestMapping(value = "/friend/v1/friends/delfriend", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void delFriends(@RequestParam("userid") String userId, @RequestParam("friendid") String friendId);

    /***
     * 根据用户id查找用户
     * @return
     */
    @RequestMapping(value = "/friend/v1/friends/searchfriend", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public FriendsDto searchFriend(@RequestParam("otherid") String friendId);

    /***
     * 获取我的粉丝数
     * @param userId
     * @return
     */
    @RequestMapping(value = "/friend/v1/follow/getFansCount", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public int getFollowFriendsCount(@RequestParam("userid") String userId);

    /***
     * 获取我的关注数量
     * @param userId
     * @return
     */
    @RequestMapping(value = "/friend/v1/follow/getFollowsCount", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public int getFollowsCount(@RequestParam("userid") String userId);
}
