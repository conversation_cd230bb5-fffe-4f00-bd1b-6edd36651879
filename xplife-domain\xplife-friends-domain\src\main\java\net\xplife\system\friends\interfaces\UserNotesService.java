package net.xplife.system.friends.interfaces;
import net.xplife.system.friends.dto.UserNotesDto;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import net.xplife.system.friends.dto.MsgNotesDto;

import java.util.List;

/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-friends", configuration = FeignConfiguration.class)
public interface UserNotesService {
    /**
     * 获取用户账号信息
     * 
     * @return
     */
    @RequestMapping(value = "/friend/v1/notes/getmsgnoteslistsize", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public MsgNotesDto getMsgNotesListSize(@RequestParam("userid") String userId);
    /**
     * 获取未读消息数量
     *
     * @return
     */
    @RequestMapping(value = "/friend/v1/notes/getmsgnoteslistsize", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public MsgNotesDto getMsgNotesListSize(@RequestParam("userid") String userId, @RequestParam("version") String version);
    /**
     * 获取未读粉丝关注消息数量
     *
     * @return
     */
    @RequestMapping(value = "/friend/v1/notes/getfannoteslistsize", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public MsgNotesDto getFanNotesListSize(@RequestParam("userid") String userId, @RequestParam("version") String version);

    @RequestMapping(value = "/friend/v1/notes/getusernoteslist", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<UserNotesDto> getMsgNotesList(@RequestParam("userid") String userId);

    @RequestMapping(value = "/friend/v1/notes/getusernotesunreadcount", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public int getUserNotesUnreadCount(@RequestParam("userid") String userId, @RequestParam("version") String version);
}
