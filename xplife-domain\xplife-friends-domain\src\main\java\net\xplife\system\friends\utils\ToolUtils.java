package net.xplife.system.friends.utils;
import javax.servlet.http.HttpServletRequest;
import net.xplife.system.tools.util.core.ToolsKit;
public class ToolUtils {
    /**
     * 获取IP地址
     * 
     * @param request
     * @return
     */
    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ToolsKit.isEmpty(ip)) {
            ip = request.getHeader("X-Real-IP");
            if (ToolsKit.isEmpty(ip)) {
                ip = request.getRemoteHost();
            }
        }
        return ip.split(",")[0];
    }

    /**
     * 获取请求参数
     * 
     * @param request
     * @param key
     * @return
     */
    public static String getRequestValue(HttpServletRequest request, String key) {
        try {
            if (key.indexOf("[]") > -1) {
                String[] tmpArray = request.getParameterValues(key);
                StringBuilder sb = new StringBuilder();
                for (String str : tmpArray) {
                    sb.append(str + ",");
                }
                if (ToolsKit.isNotEmpty(sb)) sb.deleteCharAt(sb.length() - 1);
                return ToolsKit.isNotEmpty(sb) ? sb.toString() : "";
            }
            String values = request.getParameter(key);
            if (ToolsKit.isEmpty(values)) {
                values = ToolsKit.isEmpty(request.getAttribute(key)) ? "" : request.getAttribute(key).toString();
            }
            return values;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /***
     * 目标版本是否大于来源版本
     * @param source 来源版本
     * @param target 目标版本
     * @return true：大于等于
     * @throws Exception
     */
    public static boolean compareVersion(String source, String target){
        String[] sourceArr = source.split("\\.");
        String[] targetArr = target.split("\\.");
        int i=0;
        while (i<sourceArr.length && Integer.parseInt(sourceArr[i])==Integer.parseInt(targetArr[i])){
            i++;
        }
        if (i==sourceArr.length){
            return true;//属于大于等于
        } else {
            return Integer.parseInt(targetArr[i])>Integer.parseInt(sourceArr[i]);
        }
    }
}
