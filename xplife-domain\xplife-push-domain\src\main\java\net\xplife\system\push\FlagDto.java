package net.xplife.system.push;
import java.io.Serializable;
public class FlagDto implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            friendId;             // 好友ID
    private String            url;                  // 地址
    private int               msgType;              // 0社区消息 1好友消息 2系统消息 3纸条消息 4共享打印
    private String            title;                // 标题
    private String            name;                 // 名称
    private String            channelId;            // 渠道ID

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFriendId() {
        return friendId;
    }

    public void setFriendId(String friendId) {
        this.friendId = friendId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
