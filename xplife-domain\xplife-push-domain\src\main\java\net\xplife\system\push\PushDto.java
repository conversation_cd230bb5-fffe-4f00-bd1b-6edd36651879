package net.xplife.system.push;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
public class PushDto implements Serializable {
    /**
     * 推送信息配置dto--服务器返回
     */
    private static final long   serialVersionUID = 1L;
    private List<String>        tokenList;            // 推送的设备集合
    private String              content;              // 内容
    private Map<String, String> conetntMap;           // 推送内容参数集合
    private CustomPushParam     customPushParam;      // 参数对象

    public List<String> getTokenList() {
        return tokenList;
    }

    public void setTokenList(List<String> tokenList) {
        this.tokenList = tokenList;
    }

    public Map<String, String> getConetntMap() {
        return conetntMap;
    }

    public void setConetntMap(Map<String, String> conetntMap) {
        this.conetntMap = conetntMap;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public CustomPushParam getCustomPushParam() {
        return customPushParam;
    }

    public void setCustomPushParam(CustomPushParam customPushParam) {
        this.customPushParam = customPushParam;
    }
}
