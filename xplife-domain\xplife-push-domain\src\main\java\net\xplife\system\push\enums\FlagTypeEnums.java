package net.xplife.system.push.enums;
import java.util.LinkedHashMap;
/**
 * 
 *
 */
public enum FlagTypeEnums {
    FEED(0, "社区消息"), 
    FRIEND(1, "好友消息"), 
    SYSTEM(2, "系统消息");
    private final int                                   type;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<Integer, String>();
        for (FlagTypeEnums flagTypeEnums : FlagTypeEnums.values()) {
            map.put(flagTypeEnums.getType(), flagTypeEnums.getDesc());
        }
    }

    FlagTypeEnums(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
