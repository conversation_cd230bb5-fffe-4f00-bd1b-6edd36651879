package net.xplife.system.push.interfaces;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import net.xplife.system.push.PushDto;
import net.xplife.system.web.core.FeignConfiguration;
/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-push", configuration = FeignConfiguration.class)
public interface PushService {
    /**
     * 发送推送消息
     * 
     * @return
     */
    @RequestMapping(value = "/push/v1/push/sendpush", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void sendPush(@RequestBody PushDto pushDto);
}
