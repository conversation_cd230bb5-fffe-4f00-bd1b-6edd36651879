package net.xplife.system.quanpin.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/1 9:50
 * @description：
 * @modified By：
 * @version: $
 */
public class ChapterDto {
    private String chapterCode;
    private String catalogueType;
    private String parentCode;
    private String belongBook;
    private Boolean hasChild;
    private String chapterName;
    private Integer chapterLevel;
    private List<ChapterDto> children;

    public String getChapterCode() {
        return chapterCode;
    }

    public void setChapterCode(String chapterCode) {
        this.chapterCode = chapterCode;
    }

    public String getCatalogueType() {
        return catalogueType;
    }

    public void setCatalogueType(String catalogueType) {
        this.catalogueType = catalogueType;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getBelongBook() {
        return belongBook;
    }

    public void setBelongBook(String belongBook) {
        this.belongBook = belongBook;
    }

    public Boolean getHasChild() {
        return hasChild;
    }

    public void setHasChild(Boolean hasChild) {
        this.hasChild = hasChild;
    }

    public String getChapterName() {
        return chapterName;
    }

    public void setChapterName(String chapterName) {
        this.chapterName = chapterName;
    }

    public List<ChapterDto> getChildren() {
        return children;
    }

    public void setChildren(List<ChapterDto> children) {
        this.children = children;
    }

    public Integer getChapterLevel() {
        return chapterLevel;
    }

    public void setChapterLevel(Integer chapterLevel) {
        this.chapterLevel = chapterLevel;
    }
}
