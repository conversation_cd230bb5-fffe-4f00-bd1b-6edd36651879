package net.xplife.system.quanpin.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/1 17:54
 * @description：试卷预览对象
 * @modified By：
 * @version: $
 */
public class PaperDetailsDto {
    private String paperDesc;       //		试卷描述
    private String downCount;       //	Long	下载总数
    private String paperType;       //	String	试卷类型
    private String paperTypeCode;   //	String	试卷类型code码
    private String paperTotalCount; //	Integer	试卷题量
    private String paperName;       //	String	试卷名称
    private String latexPaperName;  //	String	试卷latex名称
    private String latexPaperDesc;  //	String	试卷latex描述
    private String grade;           //	String	年级code码
    private String gradeName;       //	String	年级名称
    private String creatorGuid;     //	Long	创建者云平台用户id
    private String viewCount;       //	Integer	浏览次数
    private String source;          //	Integer	来源：1-题舟，2-试卷管理平台
    private String province;        //	String	省份code
    private Integer timeModified;   //	Integer	修改时间
    private List<PaperStructureDto> paperStructure; //	-	试卷结构

    public String getPaperDesc() {
        return paperDesc;
    }

    public void setPaperDesc(String paperDesc) {
        this.paperDesc = paperDesc;
    }

    public String getDownCount() {
        return downCount;
    }

    public void setDownCount(String downCount) {
        this.downCount = downCount;
    }

    public String getPaperType() {
        return paperType;
    }

    public void setPaperType(String paperType) {
        this.paperType = paperType;
    }

    public String getPaperTypeCode() {
        return paperTypeCode;
    }

    public void setPaperTypeCode(String paperTypeCode) {
        this.paperTypeCode = paperTypeCode;
    }

    public String getPaperTotalCount() {
        return paperTotalCount;
    }

    public void setPaperTotalCount(String paperTotalCount) {
        this.paperTotalCount = paperTotalCount;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public String getLatexPaperName() {
        return latexPaperName;
    }

    public void setLatexPaperName(String latexPaperName) {
        this.latexPaperName = latexPaperName;
    }

    public String getLatexPaperDesc() {
        return latexPaperDesc;
    }

    public void setLatexPaperDesc(String latexPaperDesc) {
        this.latexPaperDesc = latexPaperDesc;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getGradeName() {
        return gradeName;
    }

    public void setGradeName(String gradeName) {
        this.gradeName = gradeName;
    }

    public String getCreatorGuid() {
        return creatorGuid;
    }

    public void setCreatorGuid(String creatorGuid) {
        this.creatorGuid = creatorGuid;
    }

    public String getViewCount() {
        return viewCount;
    }

    public void setViewCount(String viewCount) {
        this.viewCount = viewCount;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public Integer getTimeModified() {
        return timeModified;
    }

    public void setTimeModified(Integer timeModified) {
        this.timeModified = timeModified;
    }

    public List<PaperStructureDto> getPaperStructure() {
        return paperStructure;
    }

    public void setPaperStructure(List<PaperStructureDto> paperStructure) {
        this.paperStructure = paperStructure;
    }
}
