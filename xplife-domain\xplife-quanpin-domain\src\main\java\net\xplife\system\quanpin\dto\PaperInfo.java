package net.xplife.system.quanpin.dto;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/1 14:18
 * @description：
 * @modified By：
 * @version: $
 */
public class PaperInfo {
    private Long paperId;               //试卷id
    private String paperIdEnc;          //加密试卷id
    private String paperName;           //试卷名称
    private String latexPaperDesc;      //试卷latex属性描述
    private String latexPaperName;      //试卷latex属性名称
    private String period;              //学段code
    private String periodName;          //学段名称
    private String subjectCode;         //学科code
    private String subjectName;         //学科名称
    private String grade;               //年级code
    private String gradeName;           //年级名称
    private Integer totalCount;         //总题数
    private Integer status;             //状态:0-未入库 1-已入库
    private String typeCode;            //试卷类型code
    private String typeName;            //试卷类型名称
    private Integer timeCreate;         //创建时间
    private Integer timeModified;       //修改时间
    private Long creatorGuid;           //创建人用户id
    private Integer totalCountView;     //总浏览数
    private Integer totalCountDownload; //总下载数
    private Integer isCollected;        //是否收藏：0未收藏1收藏
    private String year;                //年份
    private String province;            //省份code
    private String provinceName;        //省份名称
    private Integer source;             //试卷来源：1-题舟，2-试卷管理平台

    public Long getPaperId() {
        return paperId;
    }

    public void setPaperId(Long paperId) {
        this.paperId = paperId;
    }

    public String getPaperIdEnc() {
        return paperIdEnc;
    }

    public void setPaperIdEnc(String paperIdEnc) {
        this.paperIdEnc = paperIdEnc;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public String getLatexPaperDesc() {
        return latexPaperDesc;
    }

    public void setLatexPaperDesc(String latexPaperDesc) {
        this.latexPaperDesc = latexPaperDesc;
    }

    public String getLatexPaperName() {
        return latexPaperName;
    }

    public void setLatexPaperName(String latexPaperName) {
        this.latexPaperName = latexPaperName;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getPeriodName() {
        return periodName;
    }

    public void setPeriodName(String periodName) {
        this.periodName = periodName;
    }

    public String getSubjectCode() {
        return subjectCode;
    }

    public void setSubjectCode(String subjectCode) {
        this.subjectCode = subjectCode;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getGradeName() {
        return gradeName;
    }

    public void setGradeName(String gradeName) {
        this.gradeName = gradeName;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getTimeCreate() {
        return timeCreate;
    }

    public void setTimeCreate(Integer timeCreate) {
        this.timeCreate = timeCreate;
    }

    public Integer getTimeModified() {
        return timeModified;
    }

    public void setTimeModified(Integer timeModified) {
        this.timeModified = timeModified;
    }

    public Long getCreatorGuid() {
        return creatorGuid;
    }

    public void setCreatorGuid(Long creatorGuid) {
        this.creatorGuid = creatorGuid;
    }

    public Integer getTotalCountView() {
        return totalCountView;
    }

    public void setTotalCountView(Integer totalCountView) {
        this.totalCountView = totalCountView;
    }

    public Integer getTotalCountDownload() {
        return totalCountDownload;
    }

    public void setTotalCountDownload(Integer totalCountDownload) {
        this.totalCountDownload = totalCountDownload;
    }

    public Integer getIsCollected() {
        return isCollected;
    }

    public void setIsCollected(Integer isCollected) {
        this.isCollected = isCollected;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }
}
