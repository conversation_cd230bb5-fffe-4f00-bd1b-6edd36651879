package net.xplife.system.quanpin.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/1 17:56
 * @description：
 * @modified By：
 * @version: $
 */
public class PaperStructureDto {
    private String segmentName;//	String	分卷名称
    private Integer isValid;//	Integer	分卷是否显示：0-不显示，1-显示
    private Long sectionId;//	Long	分卷id
    private String name;//	String	名称
    private String desc;//	String	描述
    private List<PaperStructureChildrenDto> children;

    public String getSegmentName() {
        return segmentName;
    }

    public void setSegmentName(String segmentName) {
        this.segmentName = segmentName;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Long getSectionId() {
        return sectionId;
    }

    public void setSectionId(Long sectionId) {
        this.sectionId = sectionId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<PaperStructureChildrenDto> getChildren() {
        return children;
    }

    public void setChildren(List<PaperStructureChildrenDto> children) {
        this.children = children;
    }
}

class PaperStructureChildrenDto {
    private Long bigExamQuestionId;//	Long	大题id
    private String bigExamQuestionName;//	String	大题名称
    private Long segmentId;//	Long	分卷id
    private Long parentId;//	Long	父id
    private String parentIds;//	String	多个父id
    private String desc;//	String	描述
    private Double score;//	Double	分数
    private Integer sort;//	Integer	排序
    private String questionId;//	String	试题id
    private QuestionInfoDto questionInfo;//o	Object	试题信息
    private Integer type;//	Integer	1：大题（结构） 2：小题
    private List<PaperStructureChildrenDto> children;

    public Long getBigExamQuestionId() {
        return bigExamQuestionId;
    }

    public void setBigExamQuestionId(Long bigExamQuestionId) {
        this.bigExamQuestionId = bigExamQuestionId;
    }

    public String getBigExamQuestionName() {
        return bigExamQuestionName;
    }

    public void setBigExamQuestionName(String bigExamQuestionName) {
        this.bigExamQuestionName = bigExamQuestionName;
    }

    public Long getSegmentId() {
        return segmentId;
    }

    public void setSegmentId(Long segmentId) {
        this.segmentId = segmentId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getParentIds() {
        return parentIds;
    }

    public void setParentIds(String parentIds) {
        this.parentIds = parentIds;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public QuestionInfoDto getQuestionInfo() {
        return questionInfo;
    }

    public void setQuestionInfo(QuestionInfoDto questionInfo) {
        this.questionInfo = questionInfo;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<PaperStructureChildrenDto> getChildren() {
        return children;
    }

    public void setChildren(List<PaperStructureChildrenDto> children) {
        this.children = children;
    }
}
