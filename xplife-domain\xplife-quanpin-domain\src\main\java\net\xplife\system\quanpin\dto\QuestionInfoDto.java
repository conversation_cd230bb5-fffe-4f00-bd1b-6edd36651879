package net.xplife.system.quanpin.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/2 8:48
 * @description：试卷中解析答案
 * @modified By：
 * @version: $
 */
public class QuestionInfoDto {
    private String questionId;          // 试题id
    private Long useCount;              // 用户组卷次数
    private Long updateTime;            // 修改时间
    private Integer isCollected;//	Integer	是否收藏：1-已收藏 0 -未收藏
    private Integer isAddedToBox;//	Integer	是否加入试题篮：1-已加入 0-未加入
    private Boolean canCollect;//	Boolean	能否收藏：true-是，false-否
    private Integer source;//	Integer	试题来源：1-题库，2-校本资源（题舟）

    private List<ExplainDto> explain;         // 解析
    private SubjectiveDto subjective;   // 主题
    private QuestMapDto grade;          // 年级对象
    private QuestContextDto context;    // 正文题目对象
    private QuestMapDto quesStruct;     // 试题模板对象
    private QuestMapDto quesType;       // 试题类型对象
    private QuestMapDto difficulty;     // 试题难度对象
    private List<QuestMapDto> knowledge;// 知识点集合
    private QuestMapDto year;           // 试题年份
    private QuestionStatusDto questionStatus;// 试题状态

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public Long getUseCount() {
        return useCount;
    }

    public void setUseCount(Long useCount) {
        this.useCount = useCount;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsCollected() {
        return isCollected;
    }

    public void setIsCollected(Integer isCollected) {
        this.isCollected = isCollected;
    }

    public Integer getIsAddedToBox() {
        return isAddedToBox;
    }

    public void setIsAddedToBox(Integer isAddedToBox) {
        this.isAddedToBox = isAddedToBox;
    }

    public Boolean getCanCollect() {
        return canCollect;
    }

    public void setCanCollect(Boolean canCollect) {
        this.canCollect = canCollect;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public List<ExplainDto> getExplain() {
        return explain;
    }

    public void setExplain(List<ExplainDto> explain) {
        this.explain = explain;
    }

    public SubjectiveDto getSubjective() {
        return subjective;
    }

    public void setSubjective(SubjectiveDto subjective) {
        this.subjective = subjective;
    }

    public QuestMapDto getGrade() {
        return grade;
    }

    public void setGrade(QuestMapDto grade) {
        this.grade = grade;
    }

    public QuestContextDto getContext() {
        return context;
    }

    public void setContext(QuestContextDto context) {
        this.context = context;
    }

    public QuestMapDto getQuesStruct() {
        return quesStruct;
    }

    public void setQuesStruct(QuestMapDto quesStruct) {
        this.quesStruct = quesStruct;
    }

    public QuestMapDto getQuesType() {
        return quesType;
    }

    public void setQuesType(QuestMapDto quesType) {
        this.quesType = quesType;
    }

    public QuestMapDto getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(QuestMapDto difficulty) {
        this.difficulty = difficulty;
    }

    public List<QuestMapDto> getKnowledge() {
        return knowledge;
    }

    public void setKnowledge(List<QuestMapDto> knowledge) {
        this.knowledge = knowledge;
    }

    public QuestMapDto getYear() {
        return year;
    }

    public void setYear(QuestMapDto year) {
        this.year = year;
    }

    public QuestionStatusDto getQuestionStatus() {
        return questionStatus;
    }

    public void setQuestionStatus(QuestionStatusDto questionStatus) {
        this.questionStatus = questionStatus;
    }
}

class ExplainDto {
    private String questionId;      // 试题id
    private List<List<String>> answers; // 答案
    private String analysis;    // 分析

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public List<List<String>> getAnswers() {
        return answers;
    }

    public void setAnswers(List<List<String>> answers) {
        this.answers = answers;
    }

    public String getAnalysis() {
        return analysis;
    }

    public void setAnalysis(String analysis) {
        this.analysis = analysis;
    }
}

class QuestMapDto {
    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

class SubjectiveDto {
    private Boolean code; // true：主观题； false客观题
    private String name;// 题型名称

    public Boolean getCode() {
        return code;
    }

    public void setCode(Boolean code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

class QuestContextDto {
    private String stem;//	String	正文题目
    private List<String> options;//	List<String>	选项
    private String audio;//	String	音频
    private String original_text;//	String	原文

    public String getStem() {
        return stem;
    }

    public void setStem(String stem) {
        this.stem = stem;
    }

    public List<String> getOptions() {
        return options;
    }

    public void setOptions(List<String> options) {
        this.options = options;
    }

    public String getAudio() {
        return audio;
    }

    public void setAudio(String audio) {
        this.audio = audio;
    }

    public String getOriginal_text() {
        return original_text;
    }

    public void setOriginal_text(String original_text) {
        this.original_text = original_text;
    }
}

class QuestionStatusDto {
    private Integer code;   // 状态code：2-禁用，1-启用
    private String name;    // 状态名称

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}