package net.xplife.system.quanpin.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/15 10:31
 * @description：试题册
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_ExamGroup")
public class ExamGroup extends IdEntity {
    private String userId;          // 所属用户
    private String title;           // 标题
    private String grade;           // 学年段id
    private String gradeName;       // 学年段名称
    private String iconUrl;         // 对应的icon的url地址

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getGradeName() {
        return gradeName;
    }

    public void setGradeName(String gradeName) {
        this.gradeName = gradeName;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
