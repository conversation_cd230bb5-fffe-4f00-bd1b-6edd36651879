package net.xplife.system.quanpin.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/15 10:32
 * @description：
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_ExamPaper")
public class ExamPaper extends IdEntity {
    private String examGroupId;     // 试题册id
    private String title;           // 标题
    private String grade;           // 学年段code
    private String gradeName;       // 学年段名称
    private int pageCount;       // 页数
    private List<ExamPaperItem> items;             // 图的url地址，多个以分号“;”分隔

    public String getExamGroupId() {
        return examGroupId;
    }

    public void setExamGroupId(String examGroupId) {
        this.examGroupId = examGroupId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getGradeName() {
        return gradeName;
    }

    public void setGradeName(String gradeName) {
        this.gradeName = gradeName;
    }

    public int getPageCount() {
        return pageCount;
    }

    public void setPageCount(int pageCount) {
        this.pageCount = pageCount;
    }

    public List<ExamPaperItem> getItems() {
        return items;
    }

    public void setItems(List<ExamPaperItem> items) {
        this.items = items;
    }
}
