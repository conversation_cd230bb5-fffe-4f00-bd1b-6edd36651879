package net.xplife.system.quanpin.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/21 9:29
 * @description：
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_MemberAnalysis")
public class MemberAnalysis  extends IdEntity {
    @Indexed(name = "_userid_")
    private String userId;          // 用户id
    private String userPic;         // 用户头像
    private String userName;        // 用户名称
    private String userAccount;     // 用户账号
    private String userCode;        // 用户code
    private Date limitDate;         // 会员到期日
    private int limitCount;         // 会员天数
    private String memberStatus;    // 会员状态
    private int printCount;         // 打印题卷数
    private int fatchCount;         // 获取题卷数

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public Date getLimitDate() {
        return limitDate;
    }

    public void setLimitDate(Date limitDate) {
        this.limitDate = limitDate;
    }

    public int getLimitCount() {
        return limitCount;
    }

    public void setLimitCount(int limitCount) {
        this.limitCount = limitCount;
    }

    public String getMemberStatus() {
        return memberStatus;
    }

    public void setMemberStatus(String memberStatus) {
        this.memberStatus = memberStatus;
    }

    public int getPrintCount() {
        return printCount;
    }

    public void setPrintCount(int printCount) {
        this.printCount = printCount;
    }

    public int getFatchCount() {
        return fatchCount;
    }

    public void setFatchCount(int fatchCount) {
        this.fatchCount = fatchCount;
    }

    public String getUserPic() {
        return userPic;
    }

    public void setUserPic(String userPic) {
        this.userPic = userPic;
    }
}
