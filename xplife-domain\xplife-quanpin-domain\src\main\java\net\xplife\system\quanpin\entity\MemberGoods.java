package net.xplife.system.quanpin.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/11 10:09
 * @description：全品会员商品
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_MemberGoods")
public class MemberGoods extends IdEntity {
    private boolean showLimitFlag;   // 是否展示“限时优惠”
    private String title;       // 主标题
    private String subTitle;    // 副标题
    private String unit;        // 单位： 年，月，日
    private int amount;      // 数量
    private String realPrice;   // 优惠后的真实价格
    private String price;       // 商品原价格
    private boolean onlyOnceFlag;   // 是否只允许购买一次
    private int sortNum;            // 排序字段
    private String appleProcId;     // 苹果商品id

    public boolean isShowLimitFlag() {
        return showLimitFlag;
    }

    public void setShowLimitFlag(boolean showLimitFlag) {
        this.showLimitFlag = showLimitFlag;
    }

    public boolean isOnlyOnceFlag() {
        return onlyOnceFlag;
    }

    public void setOnlyOnceFlag(boolean onlyOnceFlag) {
        this.onlyOnceFlag = onlyOnceFlag;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

    public String getRealPrice() {
        return realPrice;
    }

    public void setRealPrice(String realPrice) {
        this.realPrice = realPrice;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getAppleProcId() {
        return appleProcId;
    }

    public void setAppleProcId(String appleProcId) {
        this.appleProcId = appleProcId;
    }
}
