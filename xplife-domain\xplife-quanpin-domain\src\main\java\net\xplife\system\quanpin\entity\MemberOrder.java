package net.xplife.system.quanpin.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/11 10:45
 * @description：会员的订单
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_MemberOrder")
public class MemberOrder extends IdEntity {
    private String title;   // 标题
    private String goodsId; // 商品id
    private String goodsName;// 商品名称
    private String goodsPrice;      // 商品价格
    private String orderNo; // 订单编号
    private Date beginTime; // 会员开始时间
    private Date endTime;   // 会员结束时间
    private String orderStatus; // 订单状态
    private String paySource;// 微信：wx or支付宝：zfb; 苹果支付：apple;
    private String remarks; // 备注，当前用于苹果app发过来的票据信息，用于重复验证使用


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(String goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public String getPaySource() {
        return paySource;
    }

    public void setPaySource(String paySource) {
        this.paySource = paySource;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
