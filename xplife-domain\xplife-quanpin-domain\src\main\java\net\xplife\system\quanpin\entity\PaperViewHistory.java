package net.xplife.system.quanpin.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/16 13:49
 * @description：
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_PaperViewHistory")
public class PaperViewHistory extends IdEntity {
    @Indexed(name = "_userid_")
    private String userId;
    @Indexed(name = "_viewDate_")
    private String viewDate;
    @Indexed(name = "_paperIdEnc_")
    private String paperIdEnc;

    private int printCount;

    private int type;   // 0：为非会员点击，1：会员点击

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getViewDate() {
        return viewDate;
    }

    public void setViewDate(String viewDate) {
        this.viewDate = viewDate;
    }

    public String getPaperIdEnc() {
        return paperIdEnc;
    }

    public void setPaperIdEnc(String paperIdEnc) {
        this.paperIdEnc = paperIdEnc;
    }

    public int getPrintCount() {
        return printCount;
    }

    public void setPrintCount(int printCount) {
        this.printCount = printCount;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
