package net.xplife.system.quanpin.enums;
import java.util.LinkedHashMap;

/**
 * 试题册类型
 */
public enum ExamGroupIconEnums {
    YW("语文","/api/img/gam/common/exam/yuwen.png","小学语文、初中语文、高中语文、语文、語文、汉语、漢語、Chinese、chinese、中文"),
    SX("数学","/api/img/gam/common/exam/shuxue.png","小学数学、初中数学、高中数学、数学、數學、几何、幾何、函数、函數、Math、math、mathematics、Mathematics、Function、function、Geometry、geometry"),
    YY("英语","/api/img/gam/common/exam/yingyu.png","小学英语、初中英语、高中英语、英语、英語、英文、English、english、外语、外語"),
    DL("地理","/api/img/gam/common/exam/dili.png","小学地理、初中地理、高中地理、地理、Geography、geography"),
    LS("历史","/api/img/gam/common/exam/lishi.png","小学历史、初中历史、高中历史、历史、歷史、History、history"),
    HX("化学","/api/img/gam/common/exam/huaxue.png","小学化学、初中化学、高中化学、化学、化學、Chemistry、chemistry"),
    SW("生物","/api/img/gam/common/exam/shengwu.png","小学生物、初中生物、高中生物、生物、Biology、biology"),
    WL("物理","/api/img/gam/common/exam/wuli.png","小学物理、初中物理、高中物理、物理、Physics、physics"),
    ZZ("政治","/api/img/gam/common/exam/zhengzhi.png","小学政治、初中政治、高中政治、政治、Politics、politics"),
    XX("信息","/api/img/gam/common/exam/kexue.png","科技、科学、信息、information、info"),
    QT("其他","/api/img/gam/common/exam/qita.png",""),;

    private final String                                type;
    private final String                                icon;
    private final String                                keyWord;

    ExamGroupIconEnums(String type, String icon, String keyWord) {
        this.type = type;
        this.icon = icon;
        this.keyWord = keyWord;
    }


    public String getType() {
        return type;
    }
    
    public String getIcon() {
        return icon;
    }

    public String getKeyWord() {
        return keyWord;
    }

}
