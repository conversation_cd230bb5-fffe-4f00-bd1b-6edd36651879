package net.xplife.system.quanpin.enums;
import java.util.LinkedHashMap;

/**
 * 错题本筛选日期
 */
public enum ExamPaperSearchDateEnums {
    ONE_WEEK("近一周", "0"),
    ONE_MONTH("近一月", "1"),
    THREE_MONTH("近三月", "2"),
    HALF_YEAR("半年内","3"),
    ONE_YEAR("一年内", "4");
    private final String                                value;
    private final String                                type;
    private static final LinkedHashMap<String, String>  map;
    static {
        map = new LinkedHashMap<String, String>();
        for (ExamPaperSearchDateEnums errorBookSearchDateEnums : ExamPaperSearchDateEnums.values()) {
            map.put(errorBookSearchDateEnums.getValue(), errorBookSearchDateEnums.getType());
        }
    }

    ExamPaperSearchDateEnums(String value, String type) {
        this.value = value;
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public String getType() {
        return type;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
