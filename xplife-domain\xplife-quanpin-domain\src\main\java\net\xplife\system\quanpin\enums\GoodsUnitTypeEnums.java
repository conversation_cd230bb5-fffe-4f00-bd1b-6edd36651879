package net.xplife.system.quanpin.enums;

import java.util.LinkedHashMap;

public enum GoodsUnitTypeEnums {
    DAY("day", "日"),
    MONTH("month", "月"),
    YEAR("year", "年");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (GoodsUnitTypeEnums enums : GoodsUnitTypeEnums.values()) {
            map.put(enums.getKey(), enums.getDesc());
        }
    }

    private GoodsUnitTypeEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
