package net.xplife.system.quanpin.enums;

import java.util.LinkedHashMap;

public enum PayTypeEnums {
    WEIXIN("wx", "微信支付"),
    ZHIFUBAO("zfb", "支付宝"),
    APPLE("apple", "苹果内购");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (PayTypeEnums payTypeEnums : PayTypeEnums.values()) {
            map.put(payTypeEnums.getKey(), payTypeEnums.getDesc());
        }
    }

    private PayTypeEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
