package net.xplife.system.quanpin.interfaces;

import net.xplife.system.mongo.common.Page;
import net.xplife.system.quanpin.entity.MemberAnalysis;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 微服务对外提供api服务层
 * vip的购买订单记录表
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-quanpin", configuration = FeignConfiguration.class)
public interface MemberAnalysisService {
    // 获取列表
    @RequestMapping(value = "/quanpin/v1/memberanalysis/findpage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<MemberAnalysis> findPage(@RequestParam("pageno") int pageno,
                                         @RequestParam("pagesize") int pagesize,
                                         @RequestParam("createuserid") String createUserId,
                                         @RequestParam("userCodeId") String userCodeId,
                                         @RequestParam("memberStatus") String memberStatus,
                                         @RequestParam("payBeginDate") String payBeginDate,
                                         @RequestParam("payEndDate") String payEndDate,
                                         @RequestParam("sortColumn") String sortColumn,
                                         @RequestParam("sortDesc") String sortDesc);

}
