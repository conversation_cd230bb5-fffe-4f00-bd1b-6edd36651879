package net.xplife.system.quanpin.interfaces;

import net.xplife.system.mongo.common.Page;
import net.xplife.system.quanpin.entity.MemberGoods;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 微服务对外提供api服务层
 *
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-quanpin", configuration = FeignConfiguration.class)
public interface MemberGoodsService {
    /***
     * 获取分页
     * @param pageno
     * @param pagesize
     * @return
     */
    @RequestMapping(value = "/quanpin/v1/member/goodsPage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<MemberGoods> findPage(@RequestParam("pageno") int pageno, @RequestParam("pagesize") int pagesize);

    /***
     * 删除
     * @param id
     */
    @RequestMapping(value = "/quanpin/v1/member/deleteGoodsById", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void delete(@RequestParam("id") String id);

    /**
     * 新增或修改
     * @param memberGoods
     */
    @RequestMapping(value = "/quanpin/v1/member/addOrUpdateGoods", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public MemberGoods saveOrUpdate(@RequestBody MemberGoods memberGoods);
}
