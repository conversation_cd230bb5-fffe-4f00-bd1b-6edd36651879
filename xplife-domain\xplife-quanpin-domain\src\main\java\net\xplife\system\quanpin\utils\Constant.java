package net.xplife.system.quanpin.utils;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/28 9:23
 * @description：
 * @modified By：
 * @version: $
 */
public class Constant {
    /**
     * 接口Controller映射前缀常量
     **/
    public final static String PRE_MAPPING_URL    = "/quanpin/v1";

    /***
     * 配置信息查询
     */
    public final static String URL_CONFIG ="/paper-generator/external/api/config";
    public final static Long URL_CONFIG_API_ID = 101523l;

    /**
     * 基础数据查询
     */
    public final static String URL_GATEWAY ="/paper-generator/external/api/gateway";
    public final static Long URL_GATEWAY_API_ID = 101535l;

    /**
     * 试卷类型配置查询（同步作业&精品试卷）
     */
    public final static String URL_PAPERTYPE = "/paper-generator/external/api/paperType";
    public final static Long URL_PAPERTYPE_API_ID = 101528l;

    /***
     * 章节知识点选题
     */
    public final static String URL_QUESTION_LIST = "/paper-generator/external/api/question/list";
    public final static Long URL_QUESTION_LIST_API_ID = 101510l;

    /***
     * 章节知识点选题
     */
    public final static String URL_QUES_INFO = "/paper-generator/external/api/getQuesInfo";
    public final static Long URL_QUES_INFO_API_ID = 101512l;

    /***
     * 智能选题
     */
    public final static String URL_INTELLIGENT = "/paper-generator/external/api/intelligent";
    public final static Long URL_INTELLIGENT_API_ID = 101514l;

    /**
     * 试题音频兑换（听力）
     */
    public final static String URL_AUDIOS_BY_ID = "/paper-generator/external/api/audiosById";
    public final static Long URL_AUDIOS_BY_ID_API_ID = 101578l;

    /***
     * 变式题/相似题
     */
    public final static String URL_RELATION_QUESTION = "/paper-generator/external/api/getRelationQuestion";
    public final static Long URL_RELATION_QUESTION_API_ID = 101511l;

    /***
     * 试题搜索
     */
    public final static String URL_PAPER_QUESTIONS = "/paper-generator/external/api/getPaperQuestions";
    public final static Long URL_PAPER_QUESTIONS_API_ID = 101513l;

    /***
     * 试卷查询
     */
    public final static String URL_ALL_PAPER = "/paper-generator/external/api/getAllPaper";
    public final static Long URL_ALL_PAPER_API_ID = 101515l;


    /***
     * 同步试卷查询
     */
    public final static String URL_PAPERS_DATA = "/paper-generator/external/api/getPapersData";
    public final static Long URL_PAPERS_DATA_API_ID = 101536l;

    /***
     * 获取试卷详情/试卷预览
     */
    public final static String URL_PAPER_PREVIEW = "/paper-generator/external/api/noToken/paperPreview";
    public final static Long URL_PAPER_PREVIEW_API_ID = 101522l;

    /***
     * 试卷下载/打印文件预览
     */
    public final static String URL_PAPER_VIEW = "/paper-generator/external/api/paperView";
    public final static Long URL_PAPER_VIEW_API_ID = 101567l;

    /***
     * 试题下载
     */
    public final static String URL_DOWNLOAD_BY_QIDS = "/paper-generator/external/api/downloadByQids";
    public final static Long URL_DOWNLOAD_BY_QIDS_API_ID = 101529l;


    public final static int CONFIG_PARAMS_TYPE_DEFAULT = 1; //推荐默认学段学科查询
    public final static int CONFIG_PARAMS_TYPE_ALLSUBJECT = 2; //开放的学段学科列表查询
    public final static int CONFIG_PARAMS_TYPE_ALLVERSION = 4; //查询已开放的学段学科下教材版本列表
    public final static int CONFIG_PARAMS_TYPE_ALLVERSION_SUB = 5; //查询已开放的学段学科下教材版本对应的册别列表
    public final static int CONFIG_PARAMS_TYPE_ALLCHAPTER = 6; //查询已开放的学段学科下教材版本册别对应的章节


    public final static int PAPER_TYPE_HOMEWORK = 1;    // 试卷分类类型：1-作业
    public final static int PAPER_TYPE_EXAMINATION = 2; //试卷分类类型：2-试卷
}
