package net.xplife.system.quanpin.utils;

import net.xplife.system.account.enums.user.UserGradeLevelEnums;
import net.xplife.system.quanpin.enums.ExamGroupIconEnums;
import net.xplife.system.tools.util.core.ToolsKit;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/15 10:46
 * @description：
 * @modified By：
 * @version: $
 */
public class ExamGradeUtil {
    public static List<Map<String, Object>> getGradeMap(){
        List<Map<String, Object>> result = new ArrayList<>();
        for (UserGradeLevelEnums enums : UserGradeLevelEnums.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("grade", enums.getValue()+"");
            map.put("gradeName", enums.getDesc());
            result.add(map);
        }

        Map<String, Object> other = new HashMap<>();
        other.put("grade", "other");
        other.put("gradeName", "其他");
        result.add(other);
        return result;
    }

    public static String getGradeNameByGradeCode(String code){
        if (!ToolsKit.Number.isInteger(code)){
            return "其他";
        }
        return UserGradeLevelEnums.getByFrom(Integer.valueOf(code));
    }

    public static String getIcon(String name, String icon, String baseUrl) {
        if (ToolsKit.isEmpty(icon)) {
            String tmp = replaceSpecialtyStr(name);
            for (ExamGroupIconEnums enums : ExamGroupIconEnums.values()) {
                if (enums.getKeyWord().contains(tmp) || tmp.contains(enums.getKeyWord())) {
                    icon = baseUrl + enums.getIcon();
                    break;
                }
            }
        }
        icon = ToolsKit.isEmpty(icon) ? (baseUrl + ExamGroupIconEnums.QT.getIcon()) : icon;
        return icon;
    }

    private static String replaceSpecialtyStr(String str) {
        String pattern = "\\s*|\t|\r|\n|&|[\\uFE30-\\uFFA0]|‘’|“”|[^0-9|a-z|A-Z]";
        return Pattern.compile(pattern).matcher(str).replaceAll(StringUtils.EMPTY);
    }


}
