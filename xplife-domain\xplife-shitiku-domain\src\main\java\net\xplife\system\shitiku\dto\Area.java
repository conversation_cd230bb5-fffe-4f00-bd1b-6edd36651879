package net.xplife.system.shitiku.dto;

import com.alibaba.fastjson.annotation.JSONField;

public class Area {
    private static final long serialVersionUID = 1L;
    @JSONField(name = "ID")
    public String             id;                   // 教材标识
    @JSONField(name = "PID")
    public String             pid;                 // 教材名称
    @JSONField(name = "Name")
    public String             name;                 // 教材描述
    @JSONField(name = "SName")
    public String             sname;            // 版本标识
    @JSONField(name = "Type")
    public byte             type;            // 版本标识

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSname() {
        return sname;
    }

    public void setSname(String sname) {
        this.sname = sname;
    }

    public byte getType() {
        return type;
    }

    public void setType(byte type) {
        this.type = type;
    }
}
