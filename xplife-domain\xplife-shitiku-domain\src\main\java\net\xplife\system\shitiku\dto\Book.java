package net.xplife.system.shitiku.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

// 教材类
public class Book implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    @JSONField(name = "id")
    public String             ID;                   // 教材标识
    @JSONField(name = "name")
    public String             Name;                 // 教材名称
    @JSONField(name = "desc")
    public String             Desc;                 // 教材描述
    @JSONField(name = "editionId")
    public byte               EditionID;            // 版本标识
    @JSONField(name = "editionName")
    public String             EditionName;          // 版本名称
    @JSONField(name = "gradeId")
    public byte               GradeID;              // 年级标识
    @JSONField(name = "gradeName")
    public String             GradeName;            // 年级名称
    @JSONField(name = "termId")
    public byte               TermID;               // 学期标识
    @JSONField(name = "termName")
    public String             TermName;             // 学期名称
    @JSONField(name = "typeId")
    public byte               TypeID;               // 类别标识（选修/必修）
    @JSONField(name = "typeName")
    public String             TypeName;             // 类别名称
    @JSONField(name = "children")
    public List<Category>     Children;             // 章节信息
//    @JSONField(name = "quesCount")
//    public List<DegreeCount>  QuesCount;            // 难度题量

    public String getID() {
        return ID;
    }

    public void setID(String iD) {
        ID = iD;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getDesc() {
        return Desc;
    }

    public void setDesc(String desc) {
        Desc = desc;
    }

    public byte getEditionID() {
        return EditionID;
    }

    public void setEditionID(byte editionID) {
        EditionID = editionID;
    }

    public String getEditionName() {
        return EditionName;
    }

    public void setEditionName(String editionName) {
        EditionName = editionName;
    }

    public byte getGradeID() {
        return GradeID;
    }

    public void setGradeID(byte gradeID) {
        GradeID = gradeID;
    }

    public String getGradeName() {
        return GradeName;
    }

    public void setGradeName(String gradeName) {
        GradeName = gradeName;
    }

    public byte getTermID() {
        return TermID;
    }

    public void setTermID(byte termID) {
        TermID = termID;
    }

    public String getTermName() {
        return TermName;
    }

    public void setTermName(String termName) {
        TermName = termName;
    }

    public byte getTypeID() {
        return TypeID;
    }

    public void setTypeID(byte typeID) {
        TypeID = typeID;
    }

    public String getTypeName() {
        return TypeName;
    }

    public void setTypeName(String typeName) {
        TypeName = typeName;
    }

    public List<Category> getChildren() {
        return Children;
    }

    public void setChildren(List<Category> children) {
        Children = children;
    }

//    public List<DegreeCount> getQuesCount() {
//        return QuesCount;
//    }
//
//    public void setQuesCount(List<DegreeCount> quesCount) {
//        QuesCount = quesCount;
//    }
}
