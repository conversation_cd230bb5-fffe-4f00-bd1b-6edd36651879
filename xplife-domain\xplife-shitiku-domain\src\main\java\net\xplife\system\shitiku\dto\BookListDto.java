package net.xplife.system.shitiku.dto;
import java.io.Serializable;
import java.util.List;

public class BookListDto implements Serializable {
    /**
     * 教材列表
     */
    private static final long serialVersionUID = 1L;
    private String            name;                 // 名称
    private List<BookListDto> children;             // 子集

    public BookListDto() {
        super();
    }

    public BookListDto(String name) {
        super();
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<BookListDto> getChildren() {
        return children;
    }

    public void setChildren(List<BookListDto> children) {
        this.children = children;
    }
}
