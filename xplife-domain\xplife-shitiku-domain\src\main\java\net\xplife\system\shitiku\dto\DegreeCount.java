package net.xplife.system.shitiku.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

public class DegreeCount implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    @JSONField(name = "degreeId")
    public byte               DegreeID;             // 难度（1：基础题；2：中档题；3：难题）
    @JSONField(name = "cateId")
    public byte               CateID;               // 题型（1：选择题；2：填空题；...9：解答题。具体参考学科题型）
    @JSONField(name = "count")
    public long               Count;                // 试题数量

    public byte getDegreeID() {
        return DegreeID;
    }

    public void setDegreeID(byte degreeID) {
        DegreeID = degreeID;
    }

    public byte getCateID() {
        return CateID;
    }

    public void setCateID(byte cateID) {
        CateID = cateID;
    }

    public long getCount() {
        return Count;
    }

    public void setCount(long count) {
        Count = count;
    }
}
