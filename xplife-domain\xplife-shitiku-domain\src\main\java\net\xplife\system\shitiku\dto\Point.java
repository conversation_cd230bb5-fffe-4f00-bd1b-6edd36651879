package net.xplife.system.shitiku.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

public class Point implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    @JSONField(name = "no")
    public String             No;                   // 编号
    @J<PERSON><PERSON>ield(name = "name")
    public String             Name;                 // 名称
    @JSONField(name = "desc")
    public String             Desc;                 // 描述
    @JSONField(name = "children")
    public List<Point>        Children;             // 子集（嵌套考点）
    @JSONField(name = "quesCount")
    public List<DegreeCount>  QuesCount;            // 难度题量

    public String getNo() {
        return No;
    }

    public void setNo(String no) {
        No = no;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getDesc() {
        return Desc;
    }

    public void setDesc(String desc) {
        Desc = desc;
    }

    public List<Point> getChildren() {
        return Children;
    }

    public void setChildren(List<Point> children) {
        Children = children;
    }

    public List<DegreeCount> getQuesCount() {
        return QuesCount;
    }

    public void setQuesCount(List<DegreeCount> quesCount) {
        QuesCount = quesCount;
    }
}
