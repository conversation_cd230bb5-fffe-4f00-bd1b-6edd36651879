package net.xplife.system.shitiku.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

public class QuesChild implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    @JSONField(name = "seq")
    public byte               Seq;                  // 序号
    @JSO<PERSON>ield(name = "quesId")
    public String             QuesID;               // 试题标识
    @JSONField(name = "childId")
    public String             ChildID;              // 子题标识
    @JSONField(name = "score")
    public float              Score;                // 子题分值

    public byte getSeq() {
        return Seq;
    }

    public void setSeq(byte seq) {
        Seq = seq;
    }

    public String getQuesID() {
        return QuesID;
    }

    public void setQuesID(String quesID) {
        QuesID = quesID;
    }

    public String getChildID() {
        return ChildID;
    }

    public void setChildID(String childID) {
        ChildID = childID;
    }

    public float getScore() {
        return Score;
    }

    public void setScore(float score) {
        Score = score;
    }
}
