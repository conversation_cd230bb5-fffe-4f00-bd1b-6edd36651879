package net.xplife.system.shitiku.dto;
import com.alibaba.fastjson.annotation.JSONField;

/// 试题文件
public class QuesFile {
    @JSONField(name = "seq")
    public byte   Seq;     // 序号
    @JSONField(name = "quesId")
    public String QuesID;  // 试题标识
    @JSONField(name = "name")
    public String Name;    // 文件名称
    @JSONField(name = "audioUrl")
    public String AudioUrl;// 视频或音频URL

    public byte getSeq() {
        return Seq;
    }

    public void setSeq(byte seq) {
        Seq = seq;
    }

    public String getQuesID() {
        return QuesID;
    }

    public void setQuesID(String quesID) {
        QuesID = quesID;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getAudioUrl() {
        return AudioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        AudioUrl = audioUrl;
    }
}