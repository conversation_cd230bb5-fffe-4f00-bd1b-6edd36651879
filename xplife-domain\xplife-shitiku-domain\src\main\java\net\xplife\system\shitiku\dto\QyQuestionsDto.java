package net.xplife.system.shitiku.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/3/18 16:06
 * @description：箐优返回拍题搜题题型对象
 * @modified By：
 * @version: $
 */
public class QyQuestionsDto {
    private Integer RealCount;
    private String More;
    private String SID;
    private String ID;
    private String cateName;
    private String label;
    private String content;
    private List<String> options;
    private String date;
    private String Subject;
    private List<PointDto> Points;
    private String Analyse;
    private String Method;
    private String Discuss;

    public Integer getRealCount() {
        return RealCount;
    }

    public void setRealCount(Integer realCount) {
        RealCount = realCount;
    }

    public String getMore() {
        return More;
    }

    public void setMore(String more) {
        More = more;
    }

    public String getSID() {
        return SID;
    }

    public void setSID(String SID) {
        this.SID = SID;
    }

    public String getID() {
        return ID;
    }

    public void setID(String ID) {
        this.ID = ID;
    }

    public String getCateName() {
        return cateName;
    }

    public void setCateName(String cateName) {
        this.cateName = cateName;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getSubject() {
        return Subject;
    }

    public void setSubject(String subject) {
        Subject = subject;
    }

    public List<String> getOptions() {
        return options;
    }

    public void setOptions(List<String> options) {
        this.options = options;
    }

    public List<PointDto> getPoints() {
        return Points;
    }

    public void setPoints(List<PointDto> points) {
        Points = points;
    }

    public String getAnalyse() {
        return Analyse;
    }

    public void setAnalyse(String analyse) {
        Analyse = analyse;
    }

    public String getMethod() {
        return Method;
    }

    public void setMethod(String method) {
        Method = method;
    }

    public String getDiscuss() {
        return Discuss;
    }

    public void setDiscuss(String discuss) {
        Discuss = discuss;
    }
}
