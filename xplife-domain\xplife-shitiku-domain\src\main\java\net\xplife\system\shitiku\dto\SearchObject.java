package net.xplife.system.shitiku.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

/*
 * 搜索类
 */
public class SearchObject implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    @JSONField(name = "count")
    public int                Count;                // 总记录数
    @JSONField(name = "pageIndex")
    public int                PageIndex;            // 当前页码
    @JSONField(name = "pageSize")
    public int                PageSize;             // 每页记录数
    @JSONField(name = "data")
    public List<Ques>         Data;                 // 返回数据列表
    @JSONField(name = "keys")
    public List<String>       Keys;                 // 分词列表
    private String            template;             // 模板内容

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public int getCount() {
        return Count;
    }

    public void setCount(int count) {
        Count = count;
    }

    public int getPageIndex() {
        return PageIndex;
    }

    public void setPageIndex(int pageIndex) {
        PageIndex = pageIndex;
    }

    public int getPageSize() {
        return PageSize;
    }

    public void setPageSize(int pageSize) {
        PageSize = pageSize;
    }

    public List<Ques> getData() {
        return Data;
    }

    public void setData(List<Ques> data) {
        Data = data;
    }

    public List<String> getKeys() {
        return Keys;
    }

    public void setKeys(List<String> keys) {
        Keys = keys;
    }
}