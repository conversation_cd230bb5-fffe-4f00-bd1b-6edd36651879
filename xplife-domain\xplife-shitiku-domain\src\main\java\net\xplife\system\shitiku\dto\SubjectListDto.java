package net.xplife.system.shitiku.dto;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class SubjectListDto implements Serializable {
    /**
     * 学科列表dto
     */
    private static final long serialVersionUID = 1L;
    private List<SubjectsDto> xx = new ArrayList<>();                   // 小学
    private List<SubjectsDto> cz = new ArrayList<>();                   // 初中
    private List<SubjectsDto> gz = new ArrayList<>();                   // 高中

    public List<SubjectsDto> getXx() {
        return xx;
    }

    public void setXx(List<SubjectsDto> xx) {
        this.xx = xx;
    }

    public List<SubjectsDto> getCz() {
        return cz;
    }

    public void setCz(List<SubjectsDto> cz) {
        this.cz = cz;
    }

    public List<SubjectsDto> getGz() {
        return gz;
    }

    public void setGz(List<SubjectsDto> gz) {
        this.gz = gz;
    }
}
