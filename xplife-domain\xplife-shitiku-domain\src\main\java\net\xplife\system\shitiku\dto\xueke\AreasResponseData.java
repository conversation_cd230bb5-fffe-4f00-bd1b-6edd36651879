package net.xplife.system.shitiku.dto.xueke;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/14 15:36
 * @description：
 * @modified By：
 * @version: $
 */
public class AreasResponseData {
     private String code;       //	行政区标准代码(会随着国家行政区划调整而变化，不建议作为主键使用)	string
    private String create_time;	//创建时间	string(date-time)
    private String level;	    //行政区级别，可用值: PROVINCE、CITY、COUNTY，分别代表省级、地市级、区县级	string
    private String parent_id;	//所属行政区ID	string
    private String name;	    //行政区名称	string
    private String short_name;	//行政区简称	string
    private String id;	        //行政区ID(含数字和字母。一旦创建是不会变化的，作为主键使用)	string
    private Integer ordinal;	//排序值	integer(int32)

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getParent_id() {
        return parent_id;
    }

    public void setParent_id(String parent_id) {
        this.parent_id = parent_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShort_name() {
        return short_name;
    }

    public void setShort_name(String short_name) {
        this.short_name = short_name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Integer ordinal) {
        this.ordinal = ordinal;
    }
}
