package net.xplife.system.shitiku.dto.xueke;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/12 15:46
 * @description：
 * @modified By：
 * @version: $
 */
public class BaseResponseVO<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    private String msg;
    private String code;
    private String image_ocr_text;
    private String session_id;
    private T data;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getImage_ocr_text() {
        return image_ocr_text;
    }

    public void setImage_ocr_text(String image_ocr_text) {
        this.image_ocr_text = image_ocr_text;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getSession_id() {
        return session_id;
    }

    public void setSession_id(String session_id) {
        this.session_id = session_id;
    }
}
