package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/22 09:37
 * @description：
 * @modified By：
 * @version: $
 */
public class CourseKnowledgePoints {
    private String id; // 知识点id
    private String courseId;  //课程id
    private String roodId;
    private String parentId;    // 父节点id
    private String name;    // 名称标题
    private List<CourseKnowledgePoints> children;   // 所含下节点

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getRoodId() {
        return roodId;
    }

    public void setRoodId(String roodId) {
        this.roodId = roodId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<CourseKnowledgePoints> getChildren() {
        return children;
    }

    public void setChildren(List<CourseKnowledgePoints> children) {
        this.children = children;
    }
}
