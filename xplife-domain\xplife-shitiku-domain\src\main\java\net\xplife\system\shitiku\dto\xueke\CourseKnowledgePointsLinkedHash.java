package net.xplife.system.shitiku.dto.xueke;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/22 09:37
 * @description：
 * @modified By：
 * @version: $
 */
public class CourseKnowledgePointsLinkedHash {
    private Integer id; // 知识点id
    private Integer course_id;  //课程id
    private Integer roodId;
    private Integer parentId;    // 父节点id
    private String name;    // 名称标题
    private LinkedHashMap<Integer, CourseKnowledgePointsLinkedHash> children;   // 所含下节点

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Integer course_id) {
        this.course_id = course_id;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LinkedHashMap<Integer, CourseKnowledgePointsLinkedHash> getChildren() {
        return children;
    }

    public void setChildren(LinkedHashMap<Integer, CourseKnowledgePointsLinkedHash> children) {
        this.children = children;
    }

    public Integer getRoodId() {
        return roodId;
    }

    public void setRoodId(Integer roodId) {
        this.roodId = roodId;
    }
}
