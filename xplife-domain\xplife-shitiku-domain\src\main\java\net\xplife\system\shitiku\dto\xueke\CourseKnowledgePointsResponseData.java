package net.xplife.system.shitiku.dto.xueke;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/14 16:43
 * @description：
 * @modified By：
 * @version: $
 */
public class CourseKnowledgePointsResponseData {
    private Integer course_id;//	课程ID	integer(int32)
    private String update_time;//	修改时间	string(date-time)
    private Integer depth;//	节点深度，一级节点的深度为1，二级节点的深度为2，以此类推。	integer(int32)
    private String create_time;//	创建时间	string(date-time)
    private Boolean for_lite;//	适用于精简版	boolean
    private Integer parent_id;//	父节点ID	integer(int32)
    private String name;//	知识点名称	string
    private Integer root_id;//	root节点的ID	integer(int32)
    private Integer id;//	知识点ID	integer(int32)
    private String tag;//	一级知识点的标签，仅当节点为一级知识点时有效，分为：STANDARD|课标,CONTEST|竞赛,PREPARE|学段衔接,NEXT-GEN|新一代	string
    private String type;//	节点类型，可用值：NODE、KNOWLEDGE_POINT、TESTING_POINT，分别代表普通节点、知识点、考点。	string
    private Integer ordinal;//	排序值

    public Integer getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Integer course_id) {
        this.course_id = course_id;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public Integer getDepth() {
        return depth;
    }

    public void setDepth(Integer depth) {
        this.depth = depth;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public Boolean getFor_lite() {
        return for_lite;
    }

    public void setFor_lite(Boolean for_lite) {
        this.for_lite = for_lite;
    }

    public Integer getParent_id() {
        return parent_id;
    }

    public void setParent_id(Integer parent_id) {
        this.parent_id = parent_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getRoot_id() {
        return root_id;
    }

    public void setRoot_id(Integer root_id) {
        this.root_id = root_id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Integer ordinal) {
        this.ordinal = ordinal;
    }
}
