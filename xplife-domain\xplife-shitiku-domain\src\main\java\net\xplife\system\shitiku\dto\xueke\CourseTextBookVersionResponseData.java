package net.xplife.system.shitiku.dto.xueke;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/14 17:29
 * @description：
 * @modified By：
 * @version: $
 */
public class CourseTextBookVersionResponseData {
    private Integer course_id;//	课程ID	integer(int32)
    private Integer year;//	启用年份	integer(int32)
    private String name;//	教材版本名称	string
    private Integer id;//	教材版本ID	integer(int32)
    private Integer ordinal;//	排序值	integer(int32)

    public Integer getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Integer course_id) {
        this.course_id = course_id;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Integer ordinal) {
        this.ordinal = ordinal;
    }
}
