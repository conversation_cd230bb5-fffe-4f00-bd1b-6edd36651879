package net.xplife.system.shitiku.dto.xueke;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/14 15:59
 * @description：
 * @modified By：
 * @version: $
 */
public class CoursesResponseData {
    private Integer subject_id;//	学科ID	integer(int32)
    private String update_time;//	更新时间	string(date-time)
    private String create_time;//	创建时间	string(date-time)
    private Integer stage_id;//	学段ID	integer(int32)
    private String name;//	课程名称	string
    private Integer id;//	课程ID	integer(int32)
    private Integer ordinal;//	排序值	integer(int32)

    public Integer getSubject_id() {
        return subject_id;
    }

    public void setSubject_id(Integer subject_id) {
        this.subject_id = subject_id;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public Integer getStage_id() {
        return stage_id;
    }

    public void setStage_id(Integer stage_id) {
        this.stage_id = stage_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Integer ordinal) {
        this.ordinal = ordinal;
    }
}
