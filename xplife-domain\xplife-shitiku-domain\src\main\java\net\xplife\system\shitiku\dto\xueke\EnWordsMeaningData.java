package net.xplife.system.shitiku.dto.xueke;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16 13:37
 * @description：
 * @modified By：
 * @version: $
 */
public class EnWordsMeaningData {
    private Integer word_id;//	单词id	integer(int32)
    private String word_class;//	词性	string
    private String meaning;//	中文释义	string
    private Integer id;//	释义id	integer(int32)
    private String word;//	单词	string

    public Integer getWord_id() {
        return word_id;
    }

    public void setWord_id(Integer word_id) {
        this.word_id = word_id;
    }

    public String getWord_class() {
        return word_class;
    }

    public void setWord_class(String word_class) {
        this.word_class = word_class;
    }

    public String getMeaning() {
        return meaning;
    }

    public void setMeaning(String meaning) {
        this.meaning = meaning;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getWord() {
        return word;
    }

    public void setWord(String word) {
        this.word = word;
    }
}
