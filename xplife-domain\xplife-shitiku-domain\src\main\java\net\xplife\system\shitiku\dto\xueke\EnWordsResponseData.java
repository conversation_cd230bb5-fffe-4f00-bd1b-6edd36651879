package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16 13:36
 * @description：
 * @modified By：
 * @version: $
 */
public class EnWordsResponseData {
    private Integer id;//	单词清单id	integer(int32)
    private Integer type;//	英语单词类型：1=word，2=phrase,3=自定义短语	integer(int32)
    private List<EnWordsMeaningData> meanings;//	单词释义表	array
    private String word;//	单词	string
    private String figure_oss_path;//	图片在oss上的地址	string
    private String tags;//	英语单词标签：1=小学核心,2=小学基础,3=初中核心,4=初中基础,5=高考核心,6=高考基础,7=高频,8=CET-4	string

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<EnWordsMeaningData> getMeanings() {
        return meanings;
    }

    public void setMeanings(List<EnWordsMeaningData> meanings) {
        this.meanings = meanings;
    }

    public String getWord() {
        return word;
    }

    public void setWord(String word) {
        this.word = word;
    }

    public String getFigure_oss_path() {
        return figure_oss_path;
    }

    public void setFigure_oss_path(String figure_oss_path) {
        this.figure_oss_path = figure_oss_path;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }
}
