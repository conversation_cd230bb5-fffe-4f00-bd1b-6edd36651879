package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/12 17:57
 * @description：关键词搜题请求参数vo
 * @modified By：
 * @version: $
 */
public class KeywordSearchRequestVO {
    private Integer course_id;	//课程ID		false integer(int32)
    private boolean highlight=true; // 是否高亮
    private List<Integer>  area_ids; // 行政区ID列表（支持按省份、县市进行搜索）
    private String formula_pic_format="svg";	//公式图片格式，支持两种：png或svg，默认是svg		false string
    private String keywords;    //题干关键词(限200字符以内）
    private Integer year;   // 当前页码（从1开始，总计最多返回100条数据）
    private List<String> type_ids;	//试题类型ID集合		false array
    private Integer page_index; //当前页码（从1开始，总计最多返回100条数据）

    private List<Integer> difficulty_levels;	//试题难度等级ID集合（17 容易 18 较易 19 一般 20 较难 21 困难），最多传5个		false array
    private Integer page_size=10;//返回的最大试题数，默认为5道题		false integer(int32)

    public Integer getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Integer course_id) {
        this.course_id = course_id;
    }

    public String getFormula_pic_format() {
        return formula_pic_format;
    }

    public void setFormula_pic_format(String formula_pic_format) {
        this.formula_pic_format = formula_pic_format;
    }


    public List<String> getType_ids() {
        return type_ids;
    }

    public void setType_ids(List<String> type_ids) {
        this.type_ids = type_ids;
    }

    public List<Integer> getDifficulty_levels() {
        return difficulty_levels;
    }

    public void setDifficulty_levels(List<Integer> difficulty_levels) {
        this.difficulty_levels = difficulty_levels;
    }

    public boolean isHighlight() {
        return highlight;
    }

    public void setHighlight(boolean highlight) {
        this.highlight = highlight;
    }

    public List<Integer> getArea_ids() {
        return area_ids;
    }

    public void setArea_ids(List<Integer> area_ids) {
        this.area_ids = area_ids;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getPage_index() {
        return page_index;
    }

    public void setPage_index(Integer page_index) {
        this.page_index = page_index;
    }

    public Integer getPage_size() {
        return page_size;
    }

    public void setPage_size(Integer page_size) {
        this.page_size = page_size;
    }
}
