package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/13 15:35
 * @description：
 * @modified By：
 * @version: $
 */
public class KeywordSearchResponseData {
    private Integer page_index; //
    private Integer total_page;
    private Integer total_size;
    private Integer page_size;
    private List<KeywordSearchItemsData> items;

    public Integer getPage_index() {
        return page_index;
    }

    public void setPage_index(Integer page_index) {
        this.page_index = page_index;
    }

    public Integer getTotal_page() {
        return total_page;
    }

    public void setTotal_page(Integer total_page) {
        this.total_page = total_page;
    }

    public Integer getTotal_size() {
        return total_size;
    }

    public void setTotal_size(Integer total_size) {
        this.total_size = total_size;
    }

    public Integer getPage_size() {
        return page_size;
    }

    public void setPage_size(Integer page_size) {
        this.page_size = page_size;
    }

    public List<KeywordSearchItemsData> getItems() {
        return items;
    }

    public void setItems(List<KeywordSearchItemsData> items) {
        this.items = items;
    }
}
