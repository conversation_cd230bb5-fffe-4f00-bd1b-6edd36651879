package net.xplife.system.shitiku.dto.xueke;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/15 14:29
 * @description：
 * @modified By：
 * @version: $
 */
public class PaperTypeResponseData {
    private Integer parent_id;//	父节点Id，为0代表是一级节点（一级节点无父节点）	integer(int32)
    private Integer stage_id;//	适用的学段（如果为0则表示适用所有学段）	integer(int32)
    private String name;//	类型名称	string
    private String description;//	描述	string
    private Integer id;//	类型ID	integer(int32)

    public Integer getParent_id() {
        return parent_id;
    }

    public void setParent_id(Integer parent_id) {
        this.parent_id = parent_id;
    }

    public Integer getStage_id() {
        return stage_id;
    }

    public void setStage_id(Integer stage_id) {
        this.stage_id = stage_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
