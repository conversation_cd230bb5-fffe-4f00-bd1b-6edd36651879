package net.xplife.system.shitiku.dto.xueke;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/15 14:50
 * @description：
 * @modified By：
 * @version: $
 */
public class QuestionDifficultiesResponseData {
    private double ceiling;//	难度档上限值	number(double)
    private double flooring;//	难度档下限值	number(double)
    private String name;//	难度名称	string
    private Integer id;//	难度ID	integer(int32)

    public double getCeiling() {
        return ceiling;
    }

    public void setCeiling(double ceiling) {
        this.ceiling = ceiling;
    }

    public double getFlooring() {
        return flooring;
    }

    public void setFlooring(double flooring) {
        this.flooring = flooring;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
