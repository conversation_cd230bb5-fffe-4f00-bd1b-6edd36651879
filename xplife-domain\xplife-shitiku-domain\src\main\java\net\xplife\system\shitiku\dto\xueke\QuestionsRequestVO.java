package net.xplife.system.shitiku.dto.xueke;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/12 11:20
 * @description： 学科网-搜索条件
 * @modified By：
 * @version: $
 */
public class QuestionsRequestVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer course_id;                  // 课程ID
    private Integer year;                       // 年份（查询此年份及以后的试题）
    private List<Integer> kpoint_ids;           // 试题知识点ID集合，最多传10个，超过的部分会被截取掉；如果传知识点父节点，也会搜索出其子节点中的试题
    private List<String> type_ids;             // 试题类型ID集合，最多传10个，超过的部分会被截取掉；如果传试题类型父节点，也会搜索出其子节点中的试题
    private Integer count=10;                   // 返回数据条数（最小1，最大10）
    private List<Integer> paper_type_ids;       // 试卷类型ID集合，最多传10个（试卷类型包含期中、期末、一模、二模、三模、真题等20多种类型，详见基础数据API—获取试卷类型列表接口），超过的部分会被截取掉
    private String session_id;                  // 用户会话标识，用于同一个会话连续推题的去重；SessionId由接口生成，在返回结果中可获取该值；过期时间为24小时
    private Integer kpoint_match_type;          // 知识点匹配类型（0 至少包含一个知识点 1 包含全部的知识点），默认为0
    private List<Integer> catalog_ids;          // 章节ID集合，最多传10个，超过的部分会被截取掉；如果传章节父节点，也会搜索出子节点中的试题
    private List<Integer> difficulty_levels;    // 试题难度等级ID集合（17 容易 18 较易 19 一般 20 较难 21 困难），最多传5个
    private List<Integer> area_ids;             // 行政区ID列表，最多传10个，超过的部分会被截取掉
    private String formula_pic_format="svg";    // 公式图片格式，支持两种：png或svg，默认是svg
    private List<Integer> en_word_ids;          // 单词ID集合，最多传10个，超过的部分会被截取掉

    public Integer getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Integer course_id) {
        this.course_id = course_id;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public List<Integer> getKpoint_ids() {
        return kpoint_ids;
    }

    public void setKpoint_ids(List<Integer> kpoint_ids) {
        this.kpoint_ids = kpoint_ids;
    }

    public List<String> getType_ids() {
        return type_ids;
    }

    public void setType_ids(List<String> type_ids) {
        this.type_ids = type_ids;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public List<Integer> getPaper_type_ids() {
        return paper_type_ids;
    }

    public void setPaper_type_ids(List<Integer> paper_type_ids) {
        this.paper_type_ids = paper_type_ids;
    }

    public String getSession_id() {
        return session_id;
    }

    public void setSession_id(String session_id) {
        this.session_id = session_id;
    }

    public Integer getKpoint_match_type() {
        return kpoint_match_type;
    }

    public void setKpoint_match_type(Integer kpoint_match_type) {
        this.kpoint_match_type = kpoint_match_type;
    }

    public List<Integer> getCatalog_ids() {
        return catalog_ids;
    }

    public void setCatalog_ids(List<Integer> catalog_ids) {
        this.catalog_ids = catalog_ids;
    }

    public List<Integer> getDifficulty_levels() {
        return difficulty_levels;
    }

    public void setDifficulty_levels(List<Integer> difficulty_levels) {
        this.difficulty_levels = difficulty_levels;
    }

    public List<Integer> getArea_ids() {
        return area_ids;
    }

    public void setArea_ids(List<Integer> area_ids) {
        this.area_ids = area_ids;
    }

    public String getFormula_pic_format() {
        return formula_pic_format;
    }

    public void setFormula_pic_format(String formula_pic_format) {
        this.formula_pic_format = formula_pic_format;
    }

    public List<Integer> getEn_word_ids() {
        return en_word_ids;
    }

    public void setEn_word_ids(List<Integer> en_word_ids) {
        this.en_word_ids = en_word_ids;
    }
}
