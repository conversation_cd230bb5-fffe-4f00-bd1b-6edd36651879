package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16 11:05
 * @description：
 * @modified By：
 * @version: $
 */
public class ResourceTagListResponseData {
    private String code;//	标签编码	string
    private List<IdNamePair> stage_list;//	适用学段集合	array
    private String name;//	标签名称	string
    private String parent_code;//	标签父级编码	string

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<IdNamePair> getStage_list() {
        return stage_list;
    }

    public void setStage_list(List<IdNamePair> stage_list) {
        this.stage_list = stage_list;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParent_code() {
        return parent_code;
    }

    public void setParent_code(String parent_code) {
        this.parent_code = parent_code;
    }
}
