package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/12 17:57
 * @description：举一反三查询条件vo
 * @modified By：
 * @version: $
 */
public class SimilarRecommendRequestVO {
    private Integer course_id;	//课程ID		false integer(int32)
    private String formula_pic_format="svg";	//公式图片格式，支持两种：png或svg，默认是svg		false string
    private List<Integer> kpoint_ids;	//试题知识点ID集合		false array
    private List<String> type_ids;	//试题类型ID集合		false array
    private Integer count=10;	//返回的最大试题数，默认为5道题		false integer(int32)
    private String text;	//题干文本（不超过2000字）（题干文本和试题ID必传其中一个）		false string
    private String question_id;	//试题ID（题干文本和试题ID必传其中一个）		false string
    private List<Integer> difficulty_levels;	//试题难度等级ID集合（17 容易 18 较易 19 一般 20 较难 21 困难），最多传5个		false array

    public Integer getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Integer course_id) {
        this.course_id = course_id;
    }

    public String getFormula_pic_format() {
        return formula_pic_format;
    }

    public void setFormula_pic_format(String formula_pic_format) {
        this.formula_pic_format = formula_pic_format;
    }

    public List<Integer> getKpoint_ids() {
        return kpoint_ids;
    }

    public void setKpoint_ids(List<Integer> kpoint_ids) {
        this.kpoint_ids = kpoint_ids;
    }

    public List<String> getType_ids() {
        return type_ids;
    }

    public void setType_ids(List<String> type_ids) {
        this.type_ids = type_ids;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getQuestion_id() {
        return question_id;
    }

    public void setQuestion_id(String question_id) {
        this.question_id = question_id;
    }

    public List<Integer> getDifficulty_levels() {
        return difficulty_levels;
    }

    public void setDifficulty_levels(List<Integer> difficulty_levels) {
        this.difficulty_levels = difficulty_levels;
    }
}
