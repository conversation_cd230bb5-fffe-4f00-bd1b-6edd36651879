package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/12 16:44
 * @description：
 * @modified By：
 * @version: $
 */
public class SimilarRecommendResponseData {
    private List<Integer>    kpoint_ids;		//知识点ID列表	array
    private Integer             media;			//试题是否包含媒体内容（0 没有，1 有音频 2 有解题视频 3 音频和解题视频都有；解题视频不在试题中输出，请通过"获取解题视频"接口获取）	integer(int32)
    private List<String>        exp_video_posters;	//解题视频封面（一个解题视频对应一个封面，如果没有则为空；一般只有一个解题视频）	array
    private String              explanation;		//试题解析（HTML格式），请参考《试题结构和HTML渲染说明文档》	string
    private IdNamePair    type;			//试题类型	IdNamePair«string»
    private List<Integer>       years;			//试题出现在试卷中的年份，可能多个	array
    private Integer             recommend_mode;		//推题方式，0=算法推荐：指多维度算法分析后，程序推荐的试题； 1=人工推荐：指教研老师缜密分析后，手动推荐的试题		array
    private IdNamePair    course;			//课程	IdNamePair«int»
    private Integer             answer_scoreable;	//在线作答（0 不支持，1 支持）；选择题或者打标了机阅的试题	integer(int32)
    private String              id;			//试题ID	string
    private String              create_date;		//试题入库日期	string(date-time)
    private Integer             course_id;		//课程ID	integer(int32)
    private Integer             difficulty_level;	//试题难度等级（17 容易，18 较易，19 一般，20 较难，21 困难）	integer(int32)
    private List<Integer>       tag_ids;			//标签ID列表	array
    private String              type_id;			//试题类型ID	string
    private List<IdNamePair>    kpoints;			//知识点列表	array
    private List<Integer>       paper_type_ids;		//试卷类型ID列表	array
    private List<Integer>       catalog_ids;		//教材目录ID列表	array
    private List<IdNamePair>    tags;			//标签列表	array
    private Double              difficulty;		//试题难度，0~1之间的数字，值越小难度越大（(0.9,1] 容易，(0.8,0.9] 较易，(0.5,0.8] 一般，(0.3,0.5] 较难，[0, 0.3] 困难）	number(double)
    private List<IdNamePair>    en_words;		//单词列表，可通过"单词查询"接口获取单词的详细信息	array
    private String              answer;			//试题答案（HTML格式），请参考《试题结构和HTML渲染说明文档》	string
    private List<IdNamePair>    catalogs;		//教材目录列表	array
    private List<Integer>       en_word_ids;		//单词ID列表	array
    private String              stem;			//试题题干（HTML格式），请参考《试题结构和HTML渲染说明文档》	string

    public Integer getMedia() {
        return media;
    }

    public void setMedia(Integer media) {
        this.media = media;
    }

    public List<String> getExp_video_posters() {
        return exp_video_posters;
    }

    public void setExp_video_posters(List<String> exp_video_posters) {
        this.exp_video_posters = exp_video_posters;
    }

    public String getExplanation() {
        return explanation;
    }

    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }

    public List<Integer> getYears() {
        return years;
    }

    public void setYears(List<Integer> years) {
        this.years = years;
    }

    public Integer getRecommend_mode() {
        return recommend_mode;
    }

    public void setRecommend_mode(Integer recommend_mode) {
        this.recommend_mode = recommend_mode;
    }

    public Integer getAnswer_scoreable() {
        return answer_scoreable;
    }

    public void setAnswer_scoreable(Integer answer_scoreable) {
        this.answer_scoreable = answer_scoreable;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreate_date() {
        return create_date;
    }

    public void setCreate_date(String create_date) {
        this.create_date = create_date;
    }

    public Integer getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Integer course_id) {
        this.course_id = course_id;
    }

    public Integer getDifficulty_level() {
        return difficulty_level;
    }

    public void setDifficulty_level(Integer difficulty_level) {
        this.difficulty_level = difficulty_level;
    }

    public List<Integer> getTag_ids() {
        return tag_ids;
    }

    public void setTag_ids(List<Integer> tag_ids) {
        this.tag_ids = tag_ids;
    }

    public String getType_id() {
        return type_id;
    }

    public void setType_id(String type_id) {
        this.type_id = type_id;
    }

    public List<IdNamePair> getKpoints() {
        return kpoints;
    }

    public void setKpoints(List<IdNamePair> kpoints) {
        this.kpoints = kpoints;
    }

    public List<Integer> getPaper_type_ids() {
        return paper_type_ids;
    }

    public void setPaper_type_ids(List<Integer> paper_type_ids) {
        this.paper_type_ids = paper_type_ids;
    }

    public List<Integer> getCatalog_ids() {
        return catalog_ids;
    }

    public void setCatalog_ids(List<Integer> catalog_ids) {
        this.catalog_ids = catalog_ids;
    }

    public List<IdNamePair> getTags() {
        return tags;
    }

    public void setTags(List<IdNamePair> tags) {
        this.tags = tags;
    }

    public Double getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Double difficulty) {
        this.difficulty = difficulty;
    }

    public List<IdNamePair> getEn_words() {
        return en_words;
    }

    public void setEn_words(List<IdNamePair> en_words) {
        this.en_words = en_words;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public List<IdNamePair> getCatalogs() {
        return catalogs;
    }

    public void setCatalogs(List<IdNamePair> catalogs) {
        this.catalogs = catalogs;
    }

    public List<Integer> getEn_word_ids() {
        return en_word_ids;
    }

    public void setEn_word_ids(List<Integer> en_word_ids) {
        this.en_word_ids = en_word_ids;
    }

    public String getStem() {
        return stem;
    }

    public void setStem(String stem) {
        this.stem = stem;
    }

    public IdNamePair getCourse() {
        return course;
    }

    public void setCourse(IdNamePair course) {
        this.course = course;
    }

    public IdNamePair getType() {
        return type;
    }

    public void setType(IdNamePair type) {
        this.type = type;
    }

    public List<Integer> getKpoint_ids() {
        return kpoint_ids;
    }

    public void setKpoint_ids(List<Integer> kpoint_ids) {
        this.kpoint_ids = kpoint_ids;
    }
}
