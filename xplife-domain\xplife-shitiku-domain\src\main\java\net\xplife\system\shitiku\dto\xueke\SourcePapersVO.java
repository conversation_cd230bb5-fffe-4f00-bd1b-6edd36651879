package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/12 17:03
 * @description：来源试卷所在地区列表:不显示下线的试卷
 * @modified By：
 * @version: $
 */
public class SourcePapersVO {
    private List<IdNamePair> area;	//地区	IdNamePair«string»
    private Integer grade_id;	//年级ID	integer
    private Integer year;	//年份	integer
    private Integer type_id;	//试卷类型ID	integer
    private String term;	//学期,可用值:LAST,NEXT,ALL	string
    private String title;	//试卷名称	string

    public List<IdNamePair> getArea() {
        return area;
    }

    public void setArea(List<IdNamePair> area) {
        this.area = area;
    }

    public Integer getGrade_id() {
        return grade_id;
    }

    public void setGrade_id(Integer grade_id) {
        this.grade_id = grade_id;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getType_id() {
        return type_id;
    }

    public void setType_id(Integer type_id) {
        this.type_id = type_id;
    }

    public String getTerm() {
        return term;
    }

    public void setTerm(String term) {
        this.term = term;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
