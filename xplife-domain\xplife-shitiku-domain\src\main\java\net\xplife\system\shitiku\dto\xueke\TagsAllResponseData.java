package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16 11:37
 * @description：
 * @modified By：
 * @version: $
 */
public class TagsAllResponseData {
    private String category_name;//	标签分类名称	string
    List<Integer> course_ids;//	适用于哪些课程，0表示适用于全部课程	array
    private String name;//	标签名称	string
    private Integer id;//	标签ID	integer(int32)

    public String getCategory_name() {
        return category_name;
    }

    public void setCategory_name(String category_name) {
        this.category_name = category_name;
    }

    public List<Integer> getCourse_ids() {
        return course_ids;
    }

    public void setCourse_ids(List<Integer> course_ids) {
        this.course_ids = course_ids;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
