package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/20 14:41
 * @description：
 * @modified By：
 * @version: $
 */
public class TextBook {
    private String id;
    private String name;
    private List<TextBook> children;
    private String textbookId;
    private Boolean isLeaf;// 是否最终节点
    private String parentId;
    private String rootId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<TextBook> getChildren() {
        return children;
    }

    public void setChildren(List<TextBook> children) {
        this.children = children;
    }

    public Boolean getLeaf() {
        return isLeaf;
    }

    public void setLeaf(Boolean leaf) {
        isLeaf = leaf;
    }

    public String getTextbookId() {
        return textbookId;
    }

    public void setTextbookId(String textbookId) {
        this.textbookId = textbookId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getRootId() {
        return rootId;
    }

    public void setRootId(String rootId) {
        this.rootId = rootId;
    }
}
