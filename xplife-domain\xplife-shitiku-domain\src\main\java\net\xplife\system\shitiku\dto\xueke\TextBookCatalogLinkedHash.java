package net.xplife.system.shitiku.dto.xueke;

import java.util.LinkedHashMap;

/**
 * 课本章节
 * <AUTHOR>
 * @date ：Created in 2024/8/21 09:38
 * @description：
 * @modified By：
 * @version: $
 */
public class TextBookCatalogLinkedHash {
    private String textbookId;//	教材ID	integer(int32)
    private String parentId;//	父节点ID	integer(int32)
    private String name;//	节点名称	string
    private String id;//	节点ID	integer(int32)
    private String type;//	节点类型，分为实节点和虚节点（真实教材目录不存在的节点，例如：单元综合与测试）,可用值:VIRTUAL,REAL	string
    private Integer ordinal;//	排序值	integer(int32)
    private LinkedHashMap<String, TextBookCatalogLinkedHash> children;

    public String getTextbookId() {
        return textbookId;
    }

    public void setTextbookId(String textbookId) {
        this.textbookId = textbookId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Integer ordinal) {
        this.ordinal = ordinal;
    }

    public LinkedHashMap<String, TextBookCatalogLinkedHash> getChildren() {
        return children;
    }

    public void setChildren(LinkedHashMap<String, TextBookCatalogLinkedHash> children) {
        this.children = children;
    }
}
