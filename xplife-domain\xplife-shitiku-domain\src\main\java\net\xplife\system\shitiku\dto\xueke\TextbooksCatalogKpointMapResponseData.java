package net.xplife.system.shitiku.dto.xueke;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16 8:41
 * @description：
 * @modified By：
 * @version: $
 */
public class TextbooksCatalogKpointMapResponseData {
    private Integer catalog_id;//	目录节点ID	integer(int32)
    private Integer kpoint_id;//	知识点ID	integer(int32)
    private String kpoint_name;//	知识点名称	string
    private String direction;//	方向(EQUAL：等价, CONTAIN：包含, BELONG：属于, INTERSECT：交叉),可用值:EQUAL,CONTAIN,BELONG,INTERSECT	string

    public Integer getCatalog_id() {
        return catalog_id;
    }

    public void setCatalog_id(Integer catalog_id) {
        this.catalog_id = catalog_id;
    }

    public Integer getKpoint_id() {
        return kpoint_id;
    }

    public void setKpoint_id(Integer kpoint_id) {
        this.kpoint_id = kpoint_id;
    }

    public String getKpoint_name() {
        return kpoint_name;
    }

    public void setKpoint_name(String kpoint_name) {
        this.kpoint_name = kpoint_name;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }
}
