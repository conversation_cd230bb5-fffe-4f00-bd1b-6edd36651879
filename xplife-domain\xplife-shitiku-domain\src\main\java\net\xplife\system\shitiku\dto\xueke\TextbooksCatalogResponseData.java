package net.xplife.system.shitiku.dto.xueke;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/15 15:56
 * @description：
 * @modified By：
 * @version: $
 */
public class TextbooksCatalogResponseData {
    private String update_time;//	修改时间	string(date-time)
    private String create_time;//	创建时间	string(date-time)
    private Integer textbook_id;//	教材ID	integer(int32)
    private Integer parent_id;//	父节点ID	integer(int32)
    private String name;//	节点名称	string
    private Integer id;//	节点ID	integer(int32)
    private String type;//	节点类型，分为实节点和虚节点（真实教材目录不存在的节点，例如：单元综合与测试）,可用值:VIRTUAL,REAL	string
    private Integer ordinal;//	排序值	integer(int32)

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public Integer getTextbook_id() {
        return textbook_id;
    }

    public void setTextbook_id(Integer textbook_id) {
        this.textbook_id = textbook_id;
    }

    public Integer getParent_id() {
        return parent_id;
    }

    public void setParent_id(Integer parent_id) {
        this.parent_id = parent_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Integer ordinal) {
        this.ordinal = ordinal;
    }
}
