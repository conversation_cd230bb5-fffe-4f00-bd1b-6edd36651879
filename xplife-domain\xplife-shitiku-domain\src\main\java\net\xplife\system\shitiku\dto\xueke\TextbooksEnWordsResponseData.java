package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16 13:45
 * @description：
 * @modified By：
 * @version: $
 */
public class TextbooksEnWordsResponseData {
    private Integer course_id;//	课程ID	integer(int32)
    private String en_sentence;//	英文例句	string
    private String ch_sentence;//	英文例句对应的中文释义	string
    private List<EnWordsMeaningData> en_word_meanings;//	同一个单词的词性按照逗号分隔的含义	array
    private List<Integer> tags;//	单词标签：1，重点词	array
    private Integer catalog_id;//	章节ID	integer(int32)
    private List<Integer>meaning_ids;//	单词含义id	array
    private Integer page_number;//	单词所在课本的页码	integer(int32)
    private Integer word_id;//	单词id	integer(int32)
    private Integer textbook_id;//	课本ID	integer(int32)
    private Integer id;//	id	integer(int32)
    private String word;//	单词	string
    private Integer ordinal;//	单词排序	integer(int32)

    public Integer getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Integer course_id) {
        this.course_id = course_id;
    }

    public String getEn_sentence() {
        return en_sentence;
    }

    public void setEn_sentence(String en_sentence) {
        this.en_sentence = en_sentence;
    }

    public String getCh_sentence() {
        return ch_sentence;
    }

    public void setCh_sentence(String ch_sentence) {
        this.ch_sentence = ch_sentence;
    }

    public List<EnWordsMeaningData> getEn_word_meanings() {
        return en_word_meanings;
    }

    public void setEn_word_meanings(List<EnWordsMeaningData> en_word_meanings) {
        this.en_word_meanings = en_word_meanings;
    }

    public List<Integer> getTags() {
        return tags;
    }

    public void setTags(List<Integer> tags) {
        this.tags = tags;
    }

    public Integer getCatalog_id() {
        return catalog_id;
    }

    public void setCatalog_id(Integer catalog_id) {
        this.catalog_id = catalog_id;
    }

    public List<Integer> getMeaning_ids() {
        return meaning_ids;
    }

    public void setMeaning_ids(List<Integer> meaning_ids) {
        this.meaning_ids = meaning_ids;
    }

    public Integer getPage_number() {
        return page_number;
    }

    public void setPage_number(Integer page_number) {
        this.page_number = page_number;
    }

    public Integer getWord_id() {
        return word_id;
    }

    public void setWord_id(Integer word_id) {
        this.word_id = word_id;
    }

    public Integer getTextbook_id() {
        return textbook_id;
    }

    public void setTextbook_id(Integer textbook_id) {
        this.textbook_id = textbook_id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getWord() {
        return word;
    }

    public void setWord(String word) {
        this.word = word;
    }

    public Integer getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Integer ordinal) {
        this.ordinal = ordinal;
    }
}
