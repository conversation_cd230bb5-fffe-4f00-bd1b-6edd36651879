package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/15 15:28
 * @description：
 * @modified By：
 * @version: $
 */
public class TextbooksResponseData {
    private Integer page_index;//	当前页码（从1开始）	integer(int32)
    private Integer total_page;//	总页数	integer(int32)
    private Integer total_size;//	总记录数	integer(int32)
    private List<TextbooksResponseItem> items;//	本页数据	array
    private Integer page_size;//	每页记录数	integer(int32)

    public Integer getPage_index() {
        return page_index;
    }

    public void setPage_index(Integer page_index) {
        this.page_index = page_index;
    }

    public Integer getTotal_page() {
        return total_page;
    }

    public void setTotal_page(Integer total_page) {
        this.total_page = total_page;
    }

    public Integer getTotal_size() {
        return total_size;
    }

    public void setTotal_size(Integer total_size) {
        this.total_size = total_size;
    }

    public List<TextbooksResponseItem> getItems() {
        return items;
    }

    public void setItems(List<TextbooksResponseItem> items) {
        this.items = items;
    }

    public Integer getPage_size() {
        return page_size;
    }

    public void setPage_size(Integer page_size) {
        this.page_size = page_size;
    }
}
