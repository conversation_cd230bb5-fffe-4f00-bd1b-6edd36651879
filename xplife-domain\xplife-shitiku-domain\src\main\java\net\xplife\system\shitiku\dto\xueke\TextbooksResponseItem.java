package net.xplife.system.shitiku.dto.xueke;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/15 15:29
 * @description：
 * @modified By：
 * @version: $
 */
public class TextbooksResponseItem {
    private String volume;//	册别	string
    private Integer course_id;//	课程ID	integer(int32)
    private Integer grade_id;//	年级ID	integer(int32)
    private String name;//	教材名称	string
    private String term;//	学期,可用值:LAST,NEXT,ALL	string
    private Integer id;//	教材ID	integer(int32)
    private Integer version_id;//	教材版本ID	integer(int32)
    private Integer ordinal;//	排序值	integer(int32)

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }

    public Integer getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Integer course_id) {
        this.course_id = course_id;
    }

    public Integer getGrade_id() {
        return grade_id;
    }

    public void setGrade_id(Integer grade_id) {
        this.grade_id = grade_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTerm() {
        return term;
    }

    public void setTerm(String term) {
        this.term = term;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getVersion_id() {
        return version_id;
    }

    public void setVersion_id(Integer version_id) {
        this.version_id = version_id;
    }

    public Integer getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Integer ordinal) {
        this.ordinal = ordinal;
    }
}
