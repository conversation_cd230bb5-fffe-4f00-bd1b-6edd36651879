package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/7/10 10:23
 * @description：
 * @modified By：
 * @version: $
 */
public class TikuSearchData {
    /***
     * 课程ID
     */
    private Integer course_id;
    /**
     * 题目渲染所需的CSS文件
     */
    private List<String> css;
    /**
     *试题答案（HTML格式）
     */
    private String answer;
    /**
     *保留字段
     */
    private Integer level;
    /**
     *试题相似度，范围为0~100，值越大越相似，保留两位小数；该值基于文本相似度算法计算而来
     */
    private Double similarity;
    /**
     *题目渲染所需的JS文件
     */
    private List<String> js;
    /**
     *试题ID
     */
    private String id;
    /**
     *试题解析（HTML格式）
     */
    private String explanation;
    /**
     *试题题干（HTML格式）
     */
    private String stem;

    public Integer getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Integer course_id) {
        this.course_id = course_id;
    }

    public List<String> getCss() {
        return css;
    }

    public void setCss(List<String> css) {
        this.css = css;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Double getSimilarity() {
        return similarity;
    }

    public void setSimilarity(Double similarity) {
        this.similarity = similarity;
    }

    public List<String> getJs() {
        return js;
    }

    public void setJs(List<String> js) {
        this.js = js;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getExplanation() {
        return explanation;
    }

    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }

    public String getStem() {
        return stem;
    }

    public void setStem(String stem) {
        this.stem = stem;
    }
}
