package net.xplife.system.shitiku.dto.xueke;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/12 17:57
 * @description：解题方法推题vo
 * @modified By：
 * @version: $
 */
public class TrickRecommendRequestVO {
    private Integer course_id;	//课程ID		false integer(int32)
    private String formula_pic_format="svg";	//公式图片格式，支持两种：png或svg，默认是svg		false string
    private List<String> type_ids;	//试题类型ID集合		false array
    private Integer count=10;	//返回的最大试题数，默认为5道题		false integer(int32)
    private String session_id;  // 用户会话标识，用于同一个会话连续推题的去重；SessionId由接口生成，在返回结果中可获取该值；过期时间为24小时
    private List<Integer> trick_ids; // 必填  解题方法ID集合，最多传10个；如果传解题方法父节点，也会搜索出其子节点中的试题
    private List<Integer> difficulty_levels;	//试题难度等级ID集合（17 容易 18 较易 19 一般 20 较难 21 困难），最多传5个		false array

    public Integer getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Integer course_id) {
        this.course_id = course_id;
    }

    public String getFormula_pic_format() {
        return formula_pic_format;
    }

    public void setFormula_pic_format(String formula_pic_format) {
        this.formula_pic_format = formula_pic_format;
    }


    public List<String> getType_ids() {
        return type_ids;
    }

    public void setType_ids(List<String> type_ids) {
        this.type_ids = type_ids;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getSession_id() {
        return session_id;
    }

    public void setSession_id(String session_id) {
        this.session_id = session_id;
    }

    public List<Integer> getTrick_ids() {
        return trick_ids;
    }

    public void setTrick_ids(List<Integer> trick_ids) {
        this.trick_ids = trick_ids;
    }

    public List<Integer> getDifficulty_levels() {
        return difficulty_levels;
    }

    public void setDifficulty_levels(List<Integer> difficulty_levels) {
        this.difficulty_levels = difficulty_levels;
    }
}
