package net.xplife.system.shitiku.dto.xueke;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16 13:57
 * @description：
 * @modified By：
 * @version: $
 */
public class TricksResponseData {
    private Integer course_id;//	课程id	integer(int32)
    private Integer parent_id;//	父ID	integer(int32)
    private String name;//	名称	string
    private Integer id;//	ID	integer(int32)
    private String type;//	类型。包括目录节点（NODE）和方法节点（TRICK）和题型 (QUESTION_TYPE),可用值:NODE,TRICK,QUESTION_TYPE	string
    private Integer ordinal;//	排序值	integer(int32)

    public Integer getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Integer course_id) {
        this.course_id = course_id;
    }

    public Integer getParent_id() {
        return parent_id;
    }

    public void setParent_id(Integer parent_id) {
        this.parent_id = parent_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Integer ordinal) {
        this.ordinal = ordinal;
    }
}
