package net.xplife.system.shitiku.dto.xueke;

import com.xkw.xop.qbmsdk.model.Question;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/21 16:57
 * @description：
 * @modified By：
 * @version: $
 */
public class XueKeQues {
    private String id;  // 试题id
    private String courseId; // 课程id
    private Integer difficultyLevel;// 试题难度等级（17 容易，18 较易，19 一般，20 较难，21 困难）
    private IdNamePair type;// 试题类型
    private Questions question;
    private String title;
    private String collectId;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public Integer getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(Integer difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }

    public IdNamePair getType() {
        return type;
    }

    public void setType(IdNamePair type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCollectId() {
        return collectId;
    }

    public void setCollectId(String collectId) {
        this.collectId = collectId;
    }

    public Questions getQuestion() {
        return question;
    }

    public void setQuestion(Questions question) {
        this.question = question;
    }
}
