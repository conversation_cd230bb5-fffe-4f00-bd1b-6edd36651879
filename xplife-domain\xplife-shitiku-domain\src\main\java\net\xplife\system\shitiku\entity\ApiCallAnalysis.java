package net.xplife.system.shitiku.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2021/2/20 14:28
 * @description：
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_ApiCallAnalysis")
public class ApiCallAnalysis extends IdEntity {
    @Indexed(name = "_dayTime_")
    private String             dateTime;                              // 格式：yyyyMMdd
    @Indexed(name = "_businessType_")
    private String             businessType;                          // 接口类型
    private int                callCount;                             // 调用次数统计

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public int getCallCount() {
        return callCount;
    }

    public void setCallCount(int callCount) {
        this.callCount = callCount;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
}
