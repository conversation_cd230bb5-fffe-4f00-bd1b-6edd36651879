package net.xplife.system.shitiku.entity;

import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.shitiku.vo.FunctionVo;
import net.xplife.system.shitiku.vo.QuestionVo;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 首页功能信息
 */
@Document(collection = "V1_IndexFunction")
public class IndexFunction extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_IndexFunction";
    public static final String USER_ID_FIELD    = "userId";
    @Indexed(name = "_userid_")
    private String             userId;                               // 用户ID
    private List<FunctionVo>   typeList;                             // 我的首页集合
    private String             jyeooId;                              // 菁优教育用户记录ID
    private List<QuestionVo>   questionList;                         // 精选题库集合
    private List<Integer> funcAddList; // 当前用户使用过的菜单项列表

    public String getJyeooId() {
        return jyeooId;
    }

    public void setJyeooId(String jyeooId) {
        this.jyeooId = jyeooId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<FunctionVo> getTypeList() {
        return typeList;
    }

    public void setTypeList(List<FunctionVo> typeList) {
        this.typeList = typeList;
    }

    public List<QuestionVo> getQuestionList() {
        return questionList;
    }

    public void setQuestionList(List<QuestionVo> questionList) {
        this.questionList = questionList;
    }

    public List<Integer> getFuncAddList() {
        return funcAddList;
    }

    public void setFuncAddList(List<Integer> funcAddList) {
        this.funcAddList = funcAddList;
    }

}
