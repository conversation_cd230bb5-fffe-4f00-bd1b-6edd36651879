package net.xplife.system.shitiku.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 精选题库
 */
@Document(collection = "V1_SelectedQuestion")
public class SelectedQuestion extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_SelectedQuestion";
    public static final String USER_ID_FIELD    = "userId";
    public static final String JYEOO_ID_FIELD   = "jyeooId";
    public static final String SUBJECT_FIELD    = "subject";
    @Indexed(name = "_userid_")
    private String             userId;                                  // 用户ID
    @Indexed(name = "_jyeooid_")
    private String             jyeooId;                                 // 菁优教育试题ID
    private String             subject;                                 // 学科

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getJyeooId() {
        return jyeooId;
    }

    public void setJyeooId(String jyeooId) {
        this.jyeooId = jyeooId;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }
}
