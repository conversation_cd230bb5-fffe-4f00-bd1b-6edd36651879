package net.xplife.system.shitiku.entity;

import java.time.Instant;
import java.util.List;

public class VolcanoApiKeyInfo {
    private final String apiKey;
    private final String resourceType;
    private final List<String> resourceIds;
    private final Instant expirationTime;
    // 用于大模型的提示词
    private final List<String> prompts;

    public VolcanoApiKeyInfo(String apiKey, String resourceType, List<String> resourceIds, Instant expirationTime, List<String> prompts) {
        this.apiKey = apiKey;
        this.resourceType = resourceType;
        this.resourceIds = resourceIds;
        this.expirationTime = expirationTime;
        this.prompts = prompts;
    }

    public String getApiKey() {
        return apiKey;
    }

    public String getResourceType() {
        return resourceType;
    }

    public List<String> getResourceIds() {
        return resourceIds;
    }

    public Instant getExpirationTime() {
        return expirationTime;
    }

    public List<String> getPrompts() {
        return prompts;
    }

}
