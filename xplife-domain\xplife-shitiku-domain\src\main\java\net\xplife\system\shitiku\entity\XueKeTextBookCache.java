package net.xplife.system.shitiku.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2025/1/8 09:24
 * @description：保存学科网的教材缓存信息
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_XuekeTextBookCache")
public class XueKeTextBookCache extends IdEntity {
    private String courseName;  // 学科名称
    private String courseId;    // 学科id
    private String stageId;     //阶段id
    private String stageName;   //阶段名称
    private String contents;

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getStageId() {
        return stageId;
    }

    public void setStageId(String stageId) {
        this.stageId = stageId;
    }

    public String getStageName() {
        return stageName;
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }

    public String getContents() {
        return contents;
    }

    public void setContents(String contents) {
        this.contents = contents;
    }
}
