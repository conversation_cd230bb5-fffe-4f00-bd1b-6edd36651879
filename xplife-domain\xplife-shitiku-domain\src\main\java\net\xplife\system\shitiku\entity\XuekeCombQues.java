package net.xplife.system.shitiku.entity;

import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.shitiku.dto.xueke.XueKeQues;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 组题打印历史
 */
@Document(collection = "V1_XuekeCombQues")
public class XuekeCombQues extends IdEntity {
    public static final String USER_ID_FIELD    = "userId";
    private static final long  serialVersionUID = 1L;
    @Indexed(name = "_userid_")
    private String              userId;         // 用户ID
    private String              courseId;        // 課程Id
    private String              title;          // 标题
    private int                 placeType;      // 打印类型：0为竖向打印；1为横向打印
    private List<XueKeQues>          data;           // 试题内容

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getPlaceType() {
        return placeType;
    }

    public void setPlaceType(int placeType) {
        this.placeType = placeType;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public List<XueKeQues> getData() {
        return data;
    }

    public void setData(List<XueKeQues> data) {
        this.data = data;
    }
}
