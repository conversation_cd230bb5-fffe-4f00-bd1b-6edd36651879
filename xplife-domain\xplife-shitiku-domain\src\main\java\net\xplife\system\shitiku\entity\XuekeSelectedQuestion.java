package net.xplife.system.shitiku.entity;

import com.xkw.xop.qbmsdk.model.Question;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.shitiku.dto.xueke.IdNamePair;
import net.xplife.system.shitiku.dto.xueke.Questions;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/23 11:25
 * @description：
 * @modified By：
 * @version: $
 */
public class XuekeSelectedQuestion extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_XuekeSelectedQuestion";
    public static final String USER_ID_FIELD    = "userId";
    public static final String QUES_ID_FIELD   = "quesId";
    public static final String COURSE_ID_FIELD    = "courseId";
    @Indexed(name = "_userid_")
    private String             userId;                                  // 用户ID
    @Indexed(name = "_quesid_")
    private String             quesId;                                 // 试题ID
    @Indexed(name = "_courseid_")
    private String             courseId; // 课程id
    private Integer            difficultyLevel;// 试题难度等级（17 容易，18 较易，19 一般，20 较难，21 困难）
    private IdNamePair         type;// 试题类型
    private Questions           question; // 具体题目内容
    private String              title; // 试题标题
    private String              subjectId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getQuesId() {
        return quesId;
    }

    public void setQuesId(String quesId) {
        this.quesId = quesId;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public Integer getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(Integer difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }

    public IdNamePair getType() {
        return type;
    }

    public void setType(IdNamePair type) {
        this.type = type;
    }

    public Questions getQuestion() {
        return question;
    }

    public void setQuestion(Questions question) {
        this.question = question;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(String subjectId) {
        this.subjectId = subjectId;
    }
}
