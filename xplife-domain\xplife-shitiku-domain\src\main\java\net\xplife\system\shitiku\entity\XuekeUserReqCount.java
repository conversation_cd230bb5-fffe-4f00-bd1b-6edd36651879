package net.xplife.system.shitiku.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

@Document(collection = "V1_XuekeUserReqCount")
public class XuekeUserReqCount extends IdEntity {
    public static final String USER_ID_FIELD    = "userId";
    public static final String DAYDATE_FIELD   = "dayDate";
    private static final long  serialVersionUID = 1L;
    @Indexed(name = "_userId_")
    private String                      userId;
    @Indexed(name = "_dayDate_")
    private String                      dayDate;        // 格式yyyy-MM-dd
    private Map<String, Integer>        detailMap;      // 包含请求接口类型的次数

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDayDate() {
        return dayDate;
    }

    public void setDayDate(String dayDate) {
        this.dayDate = dayDate;
    }

    public Map<String, Integer> getDetailMap() {
        return detailMap;
    }

    public void setDetailMap(Map<String, Integer> detailMap) {
        this.detailMap = detailMap;
    }
}
