package net.xplife.system.shitiku.enums;
import java.util.LinkedHashMap;

/**
 * 首页功能信息枚举
 *
 * 更新记录：
 * 1.增加新的枚举项 TPDY，增加 maxVersion 字段处理版本过期 Update by RabyGao 2019-10-16
 */
public enum IndexFunctionEnums {
    PZST(1, "gnxz_photosearch","api/img/gam/common/index/big/print_ic_searchsubject.png","api/img/gam/common/index/small/print_ic_searchsubject.png","","1.7.0",4,1),
    WZSB(2, "gnxz_ocr","api/img/gam/common/index/big/print_ic_worddistinguish.png","api/img/gam/common/index/small/print_ic_worddistinguish.png","","1.7.0",5,1),
    XWY(3, "gnxz_learningenglish","api/img/gam/common/index/big/print_ic_languagexx.png","api/img/gam/common/index/small/print_ic_language.png","","1.7.0",6,1),
    DYMB(4, "gnxz_template","api/img/gam/common/index/big/print_ic_template.png","api/img/gam/common/index/small/print_ic_template.png","","1.7.0",7,1),
    WDDY(5, "gnxz_printdocument","api/img/gam/common/index/big/print_ic_folder.png","api/img/gam/common/index/small/print_ic_folder.png","","1.7.0",8,1),
    PDT(6, "gnxz_imagestitching","api/img/gam/common/index/big/print_ic_plicing.png","api/img/gam/common/index/small/print_ic_plicing.png","2.0.0","1.7.0",9,0),
    DZHF(7, "gnxz_banner","api/img/gam/common/index/big/print_ic_largebanner.png","api/img/gam/common/index/small/print_ic_largebanner.png","","1.7.0",10,0),
    WYDY(8, "gnxz_printweb","api/img/gam/common/index/big/print_ic_webpage.png","api/img/gam/common/index/small/print_ic_webpage.png","","1.7.0",11,0),
    HDDY(9, "gnxz_sharedevice","api/img/gam/common/index/big/print_ic_relativeprint.png","api/img/gam/common/index/small/print_ic_relativeprint.png","","1.7.0",12,0),
    JXTK(10, "gnxz_selectquestion","api/img/gam/common/index/big/print_ic_exercises.png","api/img/gam/common/index/small/print_ic_exercises.png","","1.9.0",3,1),
    XKTK(14, "gnxz_selectquestion","api/img/gam/common/index/big/print_ic_exercises.png","api/img/gam/common/index/small/print_ic_exercises.png","","1.9.0",3,1),
    TPDY(11, "gnxz_printpic","api/img/gam/common/index/big/print_ic_images.png","api/img/gam/common/index/small/print_ic_images.png","","2.0.0",2,1),
    XHY(12, "gnxz_studychinese","api/img/gam/common/index/big/print_ic_chinese.png","api/img/gam/common/index/small/print_ic_chinese.png","","2.5.0",1,1),
    BGDY(13, "gnxz_tableprint","api/img/gam/common/index/big/print_ic_form.png","api/img/gam/common/index/small/print_ic_form.png","","2.12.0",0,1),;
    private final int                                   type;
    private final String                                name;
    private final String                                bigIcon;
    private final String                                smallIcon;
    private final String                                maxVersion;
    private final String                                version;
    private final int                                   sort;
    private final int                                   isDefault;
    private static final LinkedHashMap<Integer, IndexFunctionEnums> map;
    static {
        map = new LinkedHashMap<>();
        for (IndexFunctionEnums indexFunctionEnums : IndexFunctionEnums.values()) {
            map.put(indexFunctionEnums.getType(), indexFunctionEnums);
        } 
    }

    IndexFunctionEnums(Integer type, String name, String bigIcon, String smallIcon, String maxVersion, String version, int sort, int isDefault) {
        this.type = type;
        this.name = name;
        this.bigIcon = bigIcon;
        this.smallIcon = smallIcon;
        this.maxVersion = maxVersion;
        this.version = version;
        this.sort = sort;
        this.isDefault = isDefault; 
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }
    
    public String getBigIcon() {
        return bigIcon;
    }

    public String getSmallIcon() {
        return smallIcon;
    }

    public String getMaxVersion() {
        return maxVersion;
    }

    public String getVersion() {
        return version;
    }
    
    public int getSort() {
        return sort;
    }
    
    public int getIsDefault() {
        return isDefault;
    }

    public static LinkedHashMap<Integer, IndexFunctionEnums> getMap() {
        return map;
    }
}
