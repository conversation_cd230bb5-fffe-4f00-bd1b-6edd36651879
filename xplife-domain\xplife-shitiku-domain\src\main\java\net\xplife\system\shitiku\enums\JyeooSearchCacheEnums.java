package net.xplife.system.shitiku.enums;

import java.util.LinkedHashMap;

public enum JyeooSearchCacheEnums {
    SEARCH_TITLE_BY_JYEOO_ID  ("comm:jyeoo:search:title:by:id:",     "题干id"),
    SEARCH_DETAIL_BY_JYEOO_ID     ("comm:jyeoo:search:detail:by:id:",     "习题解析id");
    private final String                               key;                 //key
    private final String                               desc;                //描述
    private static final LinkedHashMap<String, JyeooSearchCacheEnums> map;
    static {
        map = new LinkedHashMap<String, JyeooSearchCacheEnums>();
        for (JyeooSearchCacheEnums jyeooCacheEnums : JyeooSearchCacheEnums.values()) {
            map.put(jyeooCacheEnums.getKey(), jyeooCacheEnums);
        }
    }

    private JyeooSearchCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, JyeooSearchCacheEnums> getMap() {
        return map;
    }
}