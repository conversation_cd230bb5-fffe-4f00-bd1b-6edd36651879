package net.xplife.system.shitiku.enums;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum VolcanoCacheEnums implements ICacheEnums {
    VOLCANO_API_KEY_CACHE("comm:volcano:apikey", "火山引擎apikey缓存"),
    VOLCANO_API_KEY_CACHE_DURATION("comm:volcano:apikey:cache:duration", "火山引擎apikey缓存时长");

    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (VolcanoCacheEnums volcanoCacheEnums : VolcanoCacheEnums.values()) {
            map.put(volcanoCacheEnums.getKey(), volcanoCacheEnums.getDesc());
        }
    }

    private VolcanoCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
