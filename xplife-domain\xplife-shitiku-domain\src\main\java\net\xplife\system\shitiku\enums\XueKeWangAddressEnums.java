package net.xplife.system.shitiku.enums;

import java.util.LinkedHashMap;

/**
 * 记录调用次数的接口类型
 */
public enum XueKeWangAddressEnums {
    //    ,"shitiku:request:result:"
    QUESTIONS("/xopqbm/questions", "章节/知识点推题","shitiku:request:result:questions:%s:%s"),
    KEYWORD_SEARCH("/xopqbm/questions/keyword-search","关键词搜题","shitiku:request:result:keyword-search"),
    SIMILAR_RECOMMEND("/xopqbm/questions/similar-recommend","举一反三推题","shitiku:request:result:similar-recommend"),
    TEXT_SEARCH("/xopqbm/questions/text-search", "搜题（精品版）","shitiku:request:result:text-search:%s"),
    TIKU_SEARCH("/xopqbm/questions/tiku-search", "搜题（海量版）","shitiku:request:result:tiku-search:%s"),
    TRICK_RECOMMEND("/xopqbm/questions/trick-recommend", "解题方法推题","shitiku:request:result:trick-recommend"),
    AREAS("/xopqbm/areas","获取指定ID的行政区","shitiku:request:result:areas:%s"),
    AREAS_ALL("/xopqbm/areas/all","获取行政区列表","shitiku:request:result:areas-all"),
    COURSES("/xopqbm/courses","获取指定ID的课程","shitiku:request:result:courses:%s"),
    COURSES_ALL("/xopqbm/courses/all","获取课程列表","shitiku:request:result:coursesall"),
    COURSES_KNOWLEDGE_POINTS("/xopqbm/courses/knowledge-points","获取指定课程的知识树","shitiku:request:result:courses-knowledge-points:%s:%s"),
    COURSES_KNOWLEDGE_POINTS_LITE("/xopqbm/courses/knowledge-points/lite","获取指定课程的精简版知识树","shitiku:request:result:courses-knowledge-points-lite:%s:%s"),
    TEXTBOOK_VERSIONS("/xopqbm/courses/textbook-versions","获取指定课程ID的教材版本列表","shitiku:request:result:textbook-versions:%s"),
    GRADE_DIVISIONS("/xopqbm/grade-divisions","获取学制列表","shitiku:request:result:garde-divisions"),
    GRADES("/xopqbm/grades","获取年级列表","shitiku:request:result:grades"),
    KNOWLEDGE_POINTS("/xopqbm/knowledge-points","获取多个ID的知识点","shitiku:request:result:knowledge-points"),
    PAPER_TYPES("/xopqbm/paper-types","获取试卷类型列表","shitiku:request:result:paper-types"),
    QUESTION_DIFFICULITES("/xopqbm/question-difficulties","获取试题难度等级列表","shitiku:request:result:question-difficulties"),
    QUESTION_TYPES("/xopqbm/question-types","获取试题类型列表","shitiku:request:result:question-types:%s"),
    STAGES("/xopqbm/stages","获取学段列表","shitiku:request:result:stages"),
    SUBJECTS("/xopqbm/subjects","获取学科列表","shitiku:request:result:subjects"),
    TERMS("/xopqbm/terms","获取学期列表","shitiku:request:result:terms"),
    TEXTBOOKS("/xopqbm/textbooks","获取教材列表","shitiku:request:result:textbooks:%s:%s:%s:%s:%s"),
    TEXTBOOKS_RESULT("","获取教材列表结果","shitiku:request:result:textbooks2:%s:%s"),
    CATALOG("/xopqbm/textbooks/catalog","获取指定教材的章节列表","shitiku:request:result:catalog:%s"),
    CATALOG_KPOINT_MAP("/xopqbm/textbooks/catalog-kpoint-map","获取章节和知识点的映射关系","shitiku:request:result:catalog-kpoint-map"),
    FAMOUS_SCHOOLS("/xopqbm/orgs/famous-schools","名校列表","shitiku:request:result:famous-schools"),
    TAG_LIST("/xopzy/resource/tag-list","资源库基础标签","shitiku:request:result:tag-list"),
    TAG_ALL("/xopqbm/tags/all","获取标签列表","shitiku:request:result:tag-all"),
    EN_WORDS("/xopqbm/en-words","单词查询","shitiku:request:result:en-words"),
    TEXTBOOKS_EN_WORDS("/xopqbm/textbooks/en-words","获取指定教材的单词","shitiku:request:result:textbooks-en-words"),
    TRICKS("/xopqbm/tricks","获取解题方法目录","shitiku:request:result:tricks"),;

    /***
     * 章节/知识点推题: /xopqbm/questions
     * 关键词搜题: /xopqbm/questions/keyword-search
     * 举一反三推题: /xopqbm/questions/similar-recommend
     * 搜题（精品版）: /xopqbm/questions/text-search
     * 搜题（海量版）: /xopqbm/questions/tiku-search
     * 解题方法推题: /xopqbm/questions/trick-recommend
     *
     * 获取指定ID的行政区: /xopqbm/areas
     * 获取行政区列表: /xopqbm/areas/all
     * 获取指定ID的课程: /xopqbm/courses
     * 获取课程列表: /xopqbm/courses/all
     * 获取指定课程的知识树: /xopqbm/courses/knowledge-points
     * 获取指定课程的精简版知识树: /xopqbm/courses/knowledge-points/lite
     * 获取指定课程ID的教材版本列表: /xopqbm/courses/textbook-versions
     * 获取学制列表: /xopqbm/grade-divisions
     * 获取年级列表: /xopqbm/grades
     * 获取多个ID的知识点: /xopqbm/knowledge-points
     * 获取试卷类型列表: /xopqbm/paper-types
     * 获取试题难度等级列表: /xopqbm/question-difficulties
     * 获取试题类型列表: /xopqbm/question-types
     * 获取学段列表: /xopqbm/stages
     * 获取学科列表: /xopqbm/subjects
     * 获取学期列表: /xopqbm/terms
     * 获取教材列表: /xopqbm/textbooks
     * 获取指定教材的章节列表: /xopqbm/textbooks/catalog
     * 获取章节和知识点的映射关系: /xopqbm/textbooks/catalog-kpoint-map
     * 名校列表: /xopqbm/orgs/famous-schools
     * 资源库基础标签: /xopzy/resource/tag-list
     * 获取标签列表: /xopqbm/tags/all
     * 单词查询: /xopqbm/en-words
     * 获取指定教材的单词: /xopqbm/textbooks/en-words
     * 获取解题方法目录: /xopqbm/tricks
     */


    private final String                                url;
    private final String                                desc;
    private final String                                cacheKey;

    XueKeWangAddressEnums(String url, String desc, String cacheKey) {
        this.url = url;
        this.desc = desc;
        this.cacheKey = cacheKey;
    }

    public String getUrl() {
        return url;
    }

    public String getDesc() {
        return desc;
    }

    public String getCacheKey() {
        return cacheKey;
    }

}
