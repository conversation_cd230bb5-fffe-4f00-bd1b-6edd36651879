package net.xplife.system.shitiku.enums;

import java.util.LinkedHashMap;

public enum XuekeApiEnums {
    SEARCH_KEYWORD  ("XueKeKeyword",     "按关键字搜索试题","xueke:request:api:keyword:", 100,       "今日搜索次数用光了 明天再来吧"),
    SEARCH_COND     ("XueKeCond",        "按条件精选题库",   "xueke:request:api:searchcond:", 250,  "今日搜索次数用光了 明天再来吧"),
    SEARCH_SMILAR   ("XueKeSimilar",     "相似题",           "xueke:request:api:similar:", 50,       "相似题次数用光了 明天再来吧"),
    SEARCH_TRICK     ("XueKeTrick",       "解题方法推题",            "xueke:request:api:trick:", 50,       "解题方法推题次数用光了 明天再来吧"),
    TEXT_SEARCH     ("XueKeText",        "拍题搜题",          "xueke:request:api:text:", 50,       "拍题搜题次数用光了 明天再来吧"),
    TIKU_SEARCH     ("XueKeTiku",        "海量拍题搜题",          "xueke:request:api:tiku:", 50,       "拍题搜题次数用光了 明天再来吧"),
    XUEKE_DAY_COUNT     ("XueKeCount",        "每天调用次数",          "xueke:request:api:daycount:", 6000,       "每日6千条警戒线"),
    WHITE_LIST_USER  ("whitelist",     "白名单用户列表","jyeoo:request:api:white:list", 0,       ""),
    WHITE_USER  ("whiteuser",     "白名单用户","jyeoo:request:api:white:user:by:", 0,       "");


    private final String                               key;                 //key
    private final String                               desc;                //描述
    private final String                               cacheKey;            //缓存key
    private final Integer                              canReqCount;         //每日可请求次数
    private final String                               exceptionDesc;       //超过后,返回的文案
    private static final LinkedHashMap<String, XuekeApiEnums> map;
    static {
        map = new LinkedHashMap<String, XuekeApiEnums>();
        for (XuekeApiEnums enumsObj : XuekeApiEnums.values()) {
            map.put(enumsObj.getKey(), enumsObj);
        }
    }

    private XuekeApiEnums(String key, String desc, String cacheKey, Integer canReqCount, String exceptionDesc) {
        this.key = key;
        this.desc = desc;
        this.cacheKey = cacheKey;
        this.canReqCount = canReqCount;
        this.exceptionDesc = exceptionDesc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public String getCacheKey(){
        return cacheKey;
    }

    public Integer getCanReqCount() {
        return canReqCount;
    }

    public String getExceptionDesc() {
        return exceptionDesc;
    }

    public static LinkedHashMap<String, XuekeApiEnums> getMap() {
        return map;
    }
}