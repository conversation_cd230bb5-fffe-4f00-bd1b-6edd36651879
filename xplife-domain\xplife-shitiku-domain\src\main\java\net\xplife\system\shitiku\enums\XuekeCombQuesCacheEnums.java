package net.xplife.system.shitiku.enums;

import net.xplife.system.tools.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum XuekeCombQuesCacheEnums implements ICacheEnums {
    XUEKE_COMBQUES_BY_ID("comm:xueke:combques:by:id:", "组卷历史对象"),
    XUEKE_COMBQUES_temp_BY_USERID_COURSE_ID("comm:xueke:combques:temp:by:%s:%s", "临时组卷对象-用户和courseid"),
    XUEKE_COMBQUES_ID_LIST("comm:xueke:combques:by:list:", "组卷历史数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (XuekeCombQuesCacheEnums combQuesCacheEnums : XuekeCombQuesCacheEnums.values()) {
            map.put(combQuesCacheEnums.getKey(), combQuesCacheEnums.getDesc());
        }
    }

    private XuekeCombQuesCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
