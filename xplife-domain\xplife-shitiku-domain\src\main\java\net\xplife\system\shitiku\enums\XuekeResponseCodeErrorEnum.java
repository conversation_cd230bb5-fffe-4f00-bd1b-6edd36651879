package net.xplife.system.shitiku.enums;
import java.util.LinkedHashMap;

/**
 * 精选题库类型
 * 3.0.0版本后，取这里的english和icon的地址
 */
public enum XuekeResponseCodeErrorEnum {
    ERROR200(200,"OK"),
    ERROR2000000(2000000,"请求成功"),
    ERROR500151101(500151101,"参数错误"),
    ERROR500151102(500151102,"XOP业务参数错误"),
    ERROR500151103(500151103,"内部服务接口错误"),
    ERROR500151104(500151104,"业务错误"),
    ERROR500151105(500151105,"服务降级中(接口熔断)"),
    ERROR500151106(500151106,"接口限流"),
    ERROR500151203(500151203,"外部服务接口错误"),
    ERROR500151404(500151404,"资源不存在"),
    ERROR500151500(500151500,"服务器错误"),
    ERROR4000001(4000001,"参数错误"),
    ERROR4000003(4000003,"等待重试"),
    ERROR4010001(4010001,"请求未授权"),
    ERROR4010002(4010002,"授权失败"),
    ERROR4030001(4030001,"相应数据格式异常"),
    ERROR4040001(4040001,"数据不存在"),
    ERROR4040002(4040002,"API不存在"),
    ERROR4060001(4060001,"数据已存在"),
    ERROR5000000(5000000,"系统异常"),
    ERROR5000001(5000001,"熔断异常"),
    ERROR5000002(5000002,"其它错误"),
    ERROR900161214(900161214,"成功但是返回数为空，不计费"),
    ERROR900161400(900161400,"请求参数错误"),
    ERROR900161401(900161401,"未授权"),
    ERROR900161403(900161403,"禁止访问"),
    ERROR900161404(900161404,"访问的资源不存在"),
    ERROR900161500(900161500,"服务器端异常");
    private final int                                   code;
    private final String                                value;
    private static final LinkedHashMap<Integer, XuekeResponseCodeErrorEnum>  map;
    static {
        map = new LinkedHashMap<Integer, XuekeResponseCodeErrorEnum>();
        for (XuekeResponseCodeErrorEnum enumObj : XuekeResponseCodeErrorEnum.values()) {
            map.put(enumObj.getCode(), enumObj);
        }
    }

    XuekeResponseCodeErrorEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public int getCode() {
        return code;
    }
    
    public String getValue() {
        return value;
    }

    public static LinkedHashMap<Integer, XuekeResponseCodeErrorEnum> getMap() {
        return map;
    }
}
