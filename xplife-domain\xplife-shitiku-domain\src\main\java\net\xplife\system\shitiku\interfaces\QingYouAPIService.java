package net.xplife.system.shitiku.interfaces;//package net.xplife.system.qyapi.interfaces;
//
//import net.xplife.system.mongo.common.Page;
//import net.xplife.system.web.core.FeignConfiguration;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.http.MediaType;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RequestParam;
//
///**
// * 微服务对外提供api服务层
// * vip的购买订单记录表
// * <AUTHOR>
// */
//@FeignClient(name = "yoyin-qyapi", configuration = FeignConfiguration.class)
//public interface QingYouAPIService {
//    // 获取列表
//    @RequestMapping(value = "/quanpin/v1/order/findpage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    public Page<MemberOrder> findPage(@RequestParam("pageno") int pageno,
//                                      @RequestParam("pagesize") int pagesize,
//                                      @RequestParam("createuserid") String createUserId,
//                                      @RequestParam("goodsId") String goodsId,
//                                      @RequestParam("orderNo") String orderNo,
//                                      @RequestParam("status") String status,
//                                      @RequestParam("payBeginDate") String payBeginDate,
//                                      @RequestParam("payEndDate") String payEndDate,
//                                      @RequestParam("sortColumn") String sortColumn,
//                                      @RequestParam("sortDesc") String sortDesc);
//
//    // 重新获取订单状态
//    @RequestMapping(value = "/quanpin/v1/order/checkPayOrder", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    public MemberOrder checkPayOrder(@RequestParam("id") String id);
//
//    // 清除day天以前的未支付订单记录（直接物理删除）
//    @RequestMapping(value = "/quanpin/v1/order/cleanNoPayOrderRecordByDay", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    public void cleanNoPayOrderRecordByDay(@RequestParam("day") int day);
//
//}
