package net.xplife.system.shitiku.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import net.xplife.system.tools.util.core.ToolsKit;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.regex.Pattern;

import static net.xplife.system.shitiku.utils.Constant.APP_VERSION_491;

public class ToolUtils {
    /**
     * 设置 Header信息
     *
     * @param response
     * @param key
     * @param value
     */
    public static void setHeader(HttpServletResponse response, String key, String value) {
        response.setHeader(key, value);
    }

    /**
     * 获取IP地址
     * 
     * @param request
     * @return
     */
    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ToolsKit.isEmpty(ip)) {
            ip = request.getHeader("X-Real-IP");
            if (ToolsKit.isEmpty(ip)) {
                ip = request.getRemoteHost();
            }
        }
        return ip.split(",")[0];
    }

    /**
     * 获取请求参数
     * 
     * @param request
     * @param key
     * @return
     */
    public static String getRequestValue(HttpServletRequest request, String key) {
        try {
            if (key.indexOf("[]") > -1) {
                String[] tmpArray = request.getParameterValues(key);
                StringBuilder sb = new StringBuilder();
                for (String str : tmpArray) {
                    sb.append(str + ",");
                }
                if (ToolsKit.isNotEmpty(sb)) sb.deleteCharAt(sb.length() - 1);
                return ToolsKit.isNotEmpty(sb) ? sb.toString() : "";
            }
            String values = request.getParameter(key);
            if (ToolsKit.isEmpty(values)) {
                values = ToolsKit.isEmpty(request.getAttribute(key)) ? "" : request.getAttribute(key).toString();
            }
            return values;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 替换html代码
     * 
     * @param content
     * @return
     */
    public static String replaceHtml(String content) {
        if (ToolsKit.isNotEmpty(content)) {
            return content.replaceAll("\\&[a-zA-Z]{0,9};", "").replaceAll("<[^>]*>", "");
        }
        return StringUtils.EMPTY;
    }

    /***
     * 目标版本是否大于来源版本
     * @param source 来源版本
     * @param target 目标版本
     * @return true：大于等于
     * @throws Exception
     */
    public static boolean compareVersion(String source, String target){
        if (target == null || "".equals(target) || "null".equals(target)){
            return true;
        }
        String[] sourceArr = source.split("\\.");
        String[] targetArr = target.split("\\.");
        int i=0;
        while (i<sourceArr.length && Integer.parseInt(sourceArr[i])==Integer.parseInt(targetArr[i])){
            i++;
        }
        if (i==sourceArr.length){
            return true;//属于大于等于
        } else {
            return Integer.parseInt(targetArr[i])>Integer.parseInt(sourceArr[i]);
        }
    }

    public static String formatDate(Date date){
        if (date == null){
            return "";
        }
        Date curDate = new Date();
        //①当天：显示“时:分”   12:02
        DateTime yesterday = DateUtil.yesterday();
        if (DateUtil.format(date, "yyyy-MM-dd").equals(DateUtil.format(curDate, "yyyy-MM-dd"))){
            return DateUtil.format(date, "今天 HH:mm");
        } else if (DateUtil.format(date, "yyyy-MM-dd").equals(DateUtil.format(yesterday, "yyyy-MM-dd"))){
            return DateUtil.format(date, "昨天 HH:mm");
        } else if (DateUtil.format(date, "yyyy").equals(DateUtil.format(curDate, "yyyy"))){
            //②同年、非当天：显示“月-日 时:分”   08-20  12:02
            return DateUtil.format(date, "MM-dd HH:mm");
        } else {
            //③非同年非当天：显示“年-月-日 时:分   2019-06-13  12:02
            return DateUtil.format(date, "yyyy-MM-dd HH:mm");
        }
    }

    public static boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

    public static void main(String[] args) {
        System.out.println(ToolUtils.compareVersion(APP_VERSION_491, "4.9.0"));
    }
}
