package net.xplife.system.shitiku.vo;
import java.io.Serializable;

public class QuestionVo implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    private int               type;                 // 类型
    private int               sort;                 // 排序
    private int               count;                // 数量
    private String            subject;              // 学科标识
    private String            level;                // 小学 初中 高中
    private String            courseId;             // 课程id
    public QuestionVo() {
        super();
    }

    public QuestionVo(int type, int sort, int count, String subject, String level) {
        super();
        this.type = type;
        this.sort = sort;
        this.count = count;
        this.subject = subject;
        this.level = level;
    }

    public QuestionVo(int type, int sort, int count, String subject,String courseId, String level) {
        super();
        this.type = type;
        this.sort = sort;
        this.count = count;
        this.subject = subject;
        this.courseId = courseId;
        this.level = level;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }
}
