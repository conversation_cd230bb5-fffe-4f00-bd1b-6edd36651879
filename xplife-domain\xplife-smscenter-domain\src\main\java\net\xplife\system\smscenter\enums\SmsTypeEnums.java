package net.xplife.system.smscenter.enums;
/**
 * 短信验证码类型
 */

import java.util.LinkedHashMap;

public enum SmsTypeEnums {
    REGISTER("0", "注册"),
    BINDING("1", "绑定手机");
    public String value;
    public String desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (SmsTypeEnums smsTypeEnums : SmsTypeEnums.values()) {
            map.put(smsTypeEnums.getValue(), smsTypeEnums.getDesc());
        }
    }

    private SmsTypeEnums(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }

}
