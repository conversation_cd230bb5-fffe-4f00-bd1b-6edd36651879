package net.xplife.system.smscenter.interfaces;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import net.xplife.system.smscenter.dto.SmsStatusDto;
import net.xplife.system.web.core.FeignConfiguration;
/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-smscenter", configuration = FeignConfiguration.class)
public interface SmsCenterService {
    /**
     * 验证短信验证码
     * 
     * @return
     */
    @RequestMapping(value = "/smscenter/v1/sms/checkcode", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public SmsStatusDto checkCode(@RequestParam("phone") String phone, @RequestParam("code") String code);
}
