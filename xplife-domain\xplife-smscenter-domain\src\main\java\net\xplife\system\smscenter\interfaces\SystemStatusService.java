package net.xplife.system.smscenter.interfaces;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import net.xplife.system.smscenter.dto.SystemStatusDto;
import net.xplife.system.web.core.FeignConfiguration;
/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-smscenter", configuration = FeignConfiguration.class)
public interface SystemStatusService {
    /**
     * 保存状态信息
     * 
     * @return
     */
    @RequestMapping(value = "/smscenter/v1/status/savestatus", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void saveAccountStatus(@RequestParam("key") String key, @RequestParam("account") String account, @RequestParam("seconds") String seconds);

    /**
     * 获取状态信息
     * 
     * @return
     */
    @RequestMapping(value = "/smscenter/v1/status/getstatuscount", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public SystemStatusDto getStatusCount(@RequestParam("key") String key, @RequestParam("account") String account);
}
