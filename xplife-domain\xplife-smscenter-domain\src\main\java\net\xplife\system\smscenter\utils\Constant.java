package net.xplife.system.smscenter.utils;
public class Constant {
    /**
     * 接口Controller映射前缀常量
     **/
    public final static String PRE_MAPPING_URL              = "/smscenter/v1";
    /**
     * 验证码图片URI
     */
    public static String       APTCHA_IMG_URI               = PRE_MAPPING_URL + "/sms/getcaptcha";
    /**
     * 单日手机验证码上限
     **/
    public final static int    DAY_SMS_MAX_TOTAL_NUM        = 50;
    /**
     * 检查验证码， 重试超过三次即弹出图片验证码窗
     */
    public static int          SMS_ACCOUNT_MAX_RETRY        = 3;
    /**
     * ip验证码次数，同一个IP一天内获取短信的次数
     */
    public static int          SMS_IP_MAX_RETRY             = 100;
    /**
     * 手机登录密码错误限制次数
     */
    public static int          SMS_PWD_ERROR_MAX_RETRY      = 5;
    /**
     * 重设密码验证码限制次数
     */
    public static int          SMS_RESET_PWD_MAX_RETRY      = 5;
    /**
     * 注册新用户验证码限制次数
     */
    public static int          SMS_REGISTER_COUNT_MAX_RETRY = 10;
    /**
     * 登录注册相关IP次数，同一个IP内登录、注册、找回密码三个业务的总和次数
     */
    public static int          SMS_LOGIN_IP_COUNT_MAX_RETRY = 150;
    /**
     * 图片验证码-宽
     */
    public static int          WIDTH                        = 80;
    /**
     * 图片验证码-高
     */
    public static int          HEIGHT                       = 26;
    /**
     * 图片验证码-数字
     */
    public static String[]     strArr                       = new String[] { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};
    public static final String RANDOM_CODE_KEY              = "SadaisCaptcha";

    /***
     * appName，sadais-agent的请求头第一个参数值，A4版本
     */
    public static final String APP_NAME_A4 = "YOYIN4APRINTER";
    /***
     * appName，sadais-agent的请求头第一个参数值，普通版本
     */
    public static final String APP_NAME = "YOYINPRINTER";
}
