package net.xplife.system.sysmsg.dto;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import com.alibaba.fastjson.annotation.JSONField;
public class MsgCenterDto implements Serializable {
    /**
     * 消息中心dto--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            msgTitle;             // 消息标题
    private String            msgContent;           // 消息内容
    private int               msgType;              // 消息主类型
    private int               msgSubType;           // 消息副类型
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              msgTime;              // 消息时间
    private int               isRead;               // 是否已读
    private String            senderUserId;         // 消息发起者ID
    private String            senderNickName;       // 消息发起者昵称
    private String            senderSex;            // 消息发起者性别
    private String            senderPic;            // 消息发起者头像
    private String            pic;                  // 图片地址
    private MsgCenterParamDto param;                // 参数
    private String            msgFmtTime;           // 消息格式化后时间
    private int               isOfficial;      // 消息发起者是否官方认证
    private Map<String, Object> userTitleObj;       // 用户头衔对象，id，name，nameUrl，borderUrl
    private String              paramAndroid;                  // 自定义组装的json格式，用于andriod前端调用
    private String              paramIos;                      // 自定义组装的json格式，用于ios前端调用

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getMsgTitle() {
        return msgTitle;
    }

    public void setMsgTitle(String msgTitle) {
        this.msgTitle = msgTitle;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public int getMsgSubType() {
        return msgSubType;
    }

    public void setMsgSubType(int msgSubType) {
        this.msgSubType = msgSubType;
    }

    public Date getMsgTime() {
        return msgTime;
    }

    public void setMsgTime(Date msgTime) {
        this.msgTime = msgTime;
    }

    public int getIsRead() {
        return isRead;
    }

    public void setIsRead(int isRead) {
        this.isRead = isRead;
    }

    public String getSenderUserId() {
        return senderUserId;
    }

    public void setSenderUserId(String senderUserId) {
        this.senderUserId = senderUserId;
    }

    public String getSenderNickName() {
        return senderNickName;
    }

    public void setSenderNickName(String senderNickName) {
        this.senderNickName = senderNickName;
    }

    public String getSenderSex() {
        return senderSex;
    }

    public void setSenderSex(String senderSex) {
        this.senderSex = senderSex;
    }

    public String getSenderPic() {
        return senderPic;
    }

    public void setSenderPic(String senderPic) {
        this.senderPic = senderPic;
    }

    public MsgCenterParamDto getParam() {
        return param;
    }

    public void setParam(MsgCenterParamDto param) {
        this.param = param;
    }

    public String getMsgFmtTime() {
        return msgFmtTime;
    }

    public void setMsgFmtTime(String msgFmtTime) {
        this.msgFmtTime = msgFmtTime;
    }

    public int getIsOfficial() {
        return isOfficial;
    }

    public void setIsOfficial(int isOfficial) {
        this.isOfficial = isOfficial;
    }
    public Map<String, Object> getUserTitleObj() {
        return userTitleObj;
    }

    public void setUserTitleObj(Map<String, Object> userTitleObj) {
        this.userTitleObj = userTitleObj;
    }

    public String getParamAndroid() {
        return paramAndroid;
    }

    public void setParamAndroid(String paramAndroid) {
        this.paramAndroid = paramAndroid;
    }

    public String getParamIos() {
        return paramIos;
    }

    public void setParamIos(String paramIos) {
        this.paramIos = paramIos;
    }
}
