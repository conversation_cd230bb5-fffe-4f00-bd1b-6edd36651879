package net.xplife.system.sysmsg.dto;
import java.io.Serializable;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
public class MsgColumnDto implements Serializable {
    /**
     * 消息栏目dto--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private String            name;                 // 栏目名称
    private int               type;                 // 栏目类型
    private String            pic;                  // 图片地址
    private int               sort;                 // 排序
    private int               count;                // 消息数
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              lastDate;             // 最后更新时间--用户点击进入列表阅读记录的时间标识

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public Date getLastDate() {
        return lastDate;
    }

    public void setLastDate(Date lastDate) {
        this.lastDate = lastDate;
    }
}
