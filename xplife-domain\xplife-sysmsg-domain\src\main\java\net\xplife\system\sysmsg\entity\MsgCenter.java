package net.xplife.system.sysmsg.entity;
import java.util.Date;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.sysmsg.vo.MsgCenterParamVo;
/**
 * 消息中心表
 */
@Document(collection = "V1_MsgCenter")
public class MsgCenter extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_MsgCenter";
    public static final String USERID_FIELD     = "userId";
    public static final String MSG_TYPE_FIELD   = "msgType";
    public static final String READ_FIELD   = "read";
    @Indexed(name = "_userid_")
    private String             userId;                           // 用户ID
    private String             msgTitle;                         // 消息标题
    private String             msgContent;                       // 消息内容
    @Indexed(name = "_msgtype_")
    private int                msgType;                          // 消息主类型
    private int                msgSubType;                       // 消息副类型
    private Date               msgTime;                          // 消息时间
    private String             pic;                              // 图片地址
    private String             senderUserId;                     // 消息发起者ID
    private MsgCenterParamVo   param;                            // 参数
    private int            read;                             // 消息已读状态

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public int getMsgSubType() {
        return msgSubType;
    }

    public void setMsgSubType(int msgSubType) {
        this.msgSubType = msgSubType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMsgTitle() {
        return msgTitle;
    }

    public void setMsgTitle(String msgTitle) {
        this.msgTitle = msgTitle;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public Date getMsgTime() {
        return msgTime;
    }

    public void setMsgTime(Date msgTime) {
        this.msgTime = msgTime;
    }

    public String getSenderUserId() {
        return senderUserId;
    }

    public void setSenderUserId(String senderUserId) {
        this.senderUserId = senderUserId;
    }

    public MsgCenterParamVo getParam() {
        return param;
    }

    public void setParam(MsgCenterParamVo param) {
        this.param = param;
    }

    public int getRead() {
        return read;
    }

    public void setRead(int read) {
        this.read = read;
    }

}
