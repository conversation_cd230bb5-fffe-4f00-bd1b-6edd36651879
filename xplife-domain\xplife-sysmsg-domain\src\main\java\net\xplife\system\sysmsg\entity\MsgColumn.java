package net.xplife.system.sysmsg.entity;
import java.util.Map;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.sysmsg.vo.ColumnVo;
/**
 * 消息栏目表
 */
@Document(collection = "V1_MsgColumn")
public class MsgColumn extends IdEntity {
    private static final long      serialVersionUID = 1L;
    public static final String     COLL             = "V1_MsgColumn";
    public static final String     USERID_FIELD     = "userId";
    @Indexed(name = "_userid_")
    private String                 userId;                           // 用户ID
    private Map<Integer, ColumnVo> columnMap;                        // 栏目vo

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Map<Integer, ColumnVo> getColumnMap() {
        return columnMap;
    }

    public void setColumnMap(Map<Integer, ColumnVo> columnMap) {
        this.columnMap = columnMap;
    }
}
