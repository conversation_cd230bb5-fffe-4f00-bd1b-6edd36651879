package net.xplife.system.sysmsg.entity;
import java.util.Date;
import java.util.Map;
import org.springframework.data.mongodb.core.mapping.Document;
import net.xplife.system.mongo.common.IdEntity;
/**
 * 系统消息表
 */
@Document(collection = "V1_SystemMsg")
public class SystemMsg extends IdEntity {
    private static final long   serialVersionUID = 1L;
    public static final String  COLL             = "V1_SystemMsg";
    public static final String  START_DATE_FIELD = "startDate";
    public static final String  END_DATE_FIELD   = "endDate";
    private String              msgTitle;                         // 消息标题
    private String              msgContent;                       // 消息内容
    private int                 msgType;                          // 消息主类型
    private Map<String, String> picMap;                           // 消息图片
    private int                 msgSubType;                       // 消息副类型 跳转类型 100表示无跳转 101表示h5页 102表示社区动态 103表示申请好友 104表示好友纸条 200官方消息
    private String              jumpVal;                          // 消息跳转值
    private Date                startDate;                        // 开始时间
    private Date                endDate;                          // 结束时间

    private String              version;                          // 版本号后显示
    private String              paramAndroid;                  // 自定义组装的json格式，用于andriod前端调用
    private String              paramIos;                      // 自定义组装的json格式，用于ios前端调用

    public String getMsgTitle() {
        return msgTitle;
    }

    public void setMsgTitle(String msgTitle) {
        this.msgTitle = msgTitle;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public Map<String, String> getPicMap() {
        return picMap;
    }

    public void setPicMap(Map<String, String> picMap) {
        this.picMap = picMap;
    }

    public int getMsgSubType() {
        return msgSubType;
    }

    public void setMsgSubType(int msgSubType) {
        this.msgSubType = msgSubType;
    }

    public String getJumpVal() {
        return jumpVal;
    }

    public void setJumpVal(String jumpVal) {
        this.jumpVal = jumpVal;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getParamAndroid() {
        return paramAndroid;
    }

    public void setParamAndroid(String paramAndroid) {
        this.paramAndroid = paramAndroid;
    }

    public String getParamIos() {
        return paramIos;
    }

    public void setParamIos(String paramIos) {
        this.paramIos = paramIos;
    }
}
