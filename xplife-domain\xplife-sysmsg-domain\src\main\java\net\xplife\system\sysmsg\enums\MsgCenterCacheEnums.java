package net.xplife.system.sysmsg.enums;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum MsgCenterCacheEnums implements ICacheEnums {
    MSG_CENTER_BY_ID("sysmsg:msgc:by:id:", "消息数据记录"), 
    MSG_CENTER_ID_LIST("sysmsg:msg:by:list:", "消息数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (MsgCenterCacheEnums msgCenterCacheEnums : MsgCenterCacheEnums.values()) {
            map.put(msgCenterCacheEnums.getKey(), msgCenterCacheEnums.getDesc());
        }
    }

    private MsgCenterCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
