package net.xplife.system.sysmsg.enums;
/**
 * 消息栏目类型枚举
 */
import java.util.LinkedHashMap;
public enum MsgColumnTypeEnums {
    COMMUNITY(0, "msg_communitymessage", "https://m.yoyin.net/api/img/gam/common/me_ic_communitynotice.png", "", "", 4, "社区动态"),
    FRIENDS(1, "msg_friendmessage", "https://m.yoyin.net/api/img/gam/common/me_ic_friendnotice.png", "", "2.0.0", 3, ""),
    SYSTEM(2, "msg_systemmessage", "https://m.yoyin.net/api/img/gam/common/me_ic_xx_official.png", "", "", 2, "系统消息"),
    SHARE(3, "msg_sharedevicemessage", "https://m.yoyin.net/api/img/gam/common/me_ic_relativenotice.png", "", "", 1, "互动打印"),
    FOLLOWS(4, "msg_fansmessage", "https://m.yoyin.net/api/img/gam/common/me_ic_friendnotice.png", "2.1.0", "", 3, ""),
    FEEDBACK(5, "msg_feedbackmessage", "https://m.yoyin.net/api/img/gam/common/me_ic_xx_feedback.png", "2.10.0", "", 3, "反馈回复"),
    COMMENT(6, "msg_commentmessage", "https://m.yoyin.net/api/img/gam/common/me_ic_xx_comment.png", "3.1.0", "", 1, "评论"),
    GIVELIKE(7, "msg_givelikemessage", "https://m.yoyin.net/api/img/gam/common/me_ic_xx_like.png", "3.1.0", "", 2, "获赞"),
    OFFICIAL(8, "msg_officialmessage", "https://m.yoyin.net/api/img/gam/common/me_gf_logo.png", "4.1.0", "", 9, "官方消息");

    public int                                          type;
    public String                                       desc;
    public String                                       pic;
    private final String                                minVersion;
    private final String                                maxVersion;
    public int                                          sort;
    private String                                      name;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<Integer, String>();
        for (MsgColumnTypeEnums msgColumnTypeEnums : MsgColumnTypeEnums.values()) {
            map.put(msgColumnTypeEnums.getType(), msgColumnTypeEnums.getDesc());
        }
    }

    private MsgColumnTypeEnums(int type, String desc, String pic, String minVersion, String maxVersion, int sort, String name) {
        this.type = type;
        this.desc = desc;
        this.pic = pic;
        this.minVersion = minVersion;
        this.maxVersion = maxVersion;
        this.sort = sort;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public String getPic() {
        return pic;
    }

    public String getMinVersion() {
        return minVersion;
    }

    public String getMaxVersion() {
        return maxVersion;
    }

    public int getSort() {
        return sort;
    }

    public String getName(){ return name; }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }

    public static MsgColumnTypeEnums getMsgColumnMap(int type) {
        for (MsgColumnTypeEnums msgColumnTypeEnums : MsgColumnTypeEnums.values()) {
            if (msgColumnTypeEnums.getType()==type){
                return msgColumnTypeEnums;
            }
        }
        return null;
    }
}
