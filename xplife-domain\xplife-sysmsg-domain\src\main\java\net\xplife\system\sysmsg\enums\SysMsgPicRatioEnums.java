package net.xplife.system.sysmsg.enums;
/**
 * 系统消息图片规格枚举
 */
import java.util.LinkedHashMap;
public enum SysMsgPicRatioEnums {
    P702x200("702x200", "系统消息列表");
    public String                                      value;
    public String                                      desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (SysMsgPicRatioEnums sysMsgPicRatioEnums : SysMsgPicRatioEnums.values()) {
            map.put(sysMsgPicRatioEnums.getValue(), sysMsgPicRatioEnums.getDesc());
        }
    }

    private SysMsgPicRatioEnums(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
