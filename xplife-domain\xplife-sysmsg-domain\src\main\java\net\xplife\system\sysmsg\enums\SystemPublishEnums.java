package net.xplife.system.sysmsg.enums;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;

public enum SystemPublishEnums implements ICacheEnums {
	SYS_MSG_UPDATE_PUB_SUB("sysmsg:sys:msg:update:pub:sub", "系统消息更新广播"),;
    private final String key;
    private final String desc;
    private static final LinkedHashMap<String, String> map;

    private SystemPublishEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
    
    

    static {
        map = new LinkedHashMap<>();  
        for (SystemPublishEnums systemCacheEnums : SystemPublishEnums.values()) {
            map.put(systemCacheEnums.getKey(), systemCacheEnums.getDesc());
        }
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
    
    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
