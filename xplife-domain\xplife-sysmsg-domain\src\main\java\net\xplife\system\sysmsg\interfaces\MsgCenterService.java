package net.xplife.system.sysmsg.interfaces;
import java.util.List;

import net.xplife.system.sysmsg.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-sysmsg", configuration = FeignConfiguration.class)
public interface MsgCenterService {
    /**
     * 发送消息
     * 
     * @return
     */
    @RequestMapping(value = "/sysmsg/v1/msgcenter/sendmsg", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void sendMsg(@RequestBody SendMsgDto sendMsgDto);

    /**
     * 根据类型获取消息栏目信息
     * 
     * @return
     */
    @RequestMapping(value = "/sysmsg/v1/msgcenter/getmsgcolumnbytype", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<MsgColumnDto> getMsgColumnByType(@RequestBody GetMsgColumnDto getMsgColumnDto);


    /**
     * 根据类型获取消息栏目信息
     *
     * @return
     */
    @RequestMapping(value = "/sysmsg/v1/msgcenter/getmsgcenterlist", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<MsgCenterDto> getMsgListByType(@RequestParam(value = "userid") String userid, @RequestParam(value = "msgtype") int msgtype, @RequestParam(value = "pageno") int pageno, @RequestParam(value = "pagesize") int pagesize);

    /**
     * 根据类型获取消息栏目信息(官方反馈)
     *
     * @return
     */
    @RequestMapping(value = "/sysmsg/v1/msgcenter/getmsgcolumnfeedback", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public MsgColumnDto getMsgColumnFeedback();

    /**
     * 保存消息栏目信息
     * 
     * @return
     */
    @RequestMapping(value = "/sysmsg/v1/msgcenter/savemsgcolumnbytype", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void saveMsgColumnByType(@RequestBody SaveMsgColumnDto saveMsgColumnDto);

    /**
     * 删除消息
     *
     * @return
     */
    @RequestMapping(value = "/sysmsg/v1/msgcenter/del", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void del(@RequestParam("id") String id);

    /**
     * 标识消息为已读状态
     *
     * @return
     */
    @RequestMapping(value = "/sysmsg/v1/msgcenter/read", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void read(@RequestParam("id") String id);

    /**
     * 根据类型获取消息栏目信息(官方反馈)
     *
     * @return
     */
    @RequestMapping(value = "/sysmsg/v1/msgcenter/getmsgcolumnoffical", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public MsgColumnDto getMsgColumnOfficial();
}
