package net.xplife.system.sysmsg.interfaces;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.sysmsg.dto.SystemMsgDto;
import net.xplife.system.web.core.FeignConfiguration;
/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-sysmsg", configuration = FeignConfiguration.class)
public interface SystemMsgService {
    /**
     * 保存或更新系统消息
     * 
     * @return
     */
    @RequestMapping(value = "/sysmsg/v1/sysmsg/saveorupdate", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void saveOrUpdate(@RequestBody SystemMsgDto systemMsgDto);

    /**
     * 删除系统消息
     * 
     * @return
     */
    @RequestMapping(value = "/sysmsg/v1/sysmsg/del", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void del(@RequestParam("id") String id);

    /**
     * 获取系统消息列表
     * 
     * @return
     */
    @RequestMapping(value = "/sysmsg/v1/sysmsg/findsystemmsgpage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<SystemMsgDto> findSystemMsgPage(@RequestParam("startdate") String startDate, @RequestParam("enddate") String endDate,
            @RequestParam("pageno") int pageNo, @RequestParam("pagesize") int pageSize);
}
