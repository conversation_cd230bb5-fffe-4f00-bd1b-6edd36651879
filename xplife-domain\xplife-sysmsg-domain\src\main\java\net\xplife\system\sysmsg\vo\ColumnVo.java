package net.xplife.system.sysmsg.vo;
import java.io.Serializable;
import java.util.Date;
/**
 * 栏目VO
 */
public class ColumnVo implements Serializable {
    private static final long serialVersionUID = 1L;
    private String            name;                 // 栏目名称
    private int               type;                 // 栏目类型
    private int               count;                // 消息数
    private Date              lastDate;             // 最后更新时间--用户点击进入列表阅读记录的时间标识
    private Date              refreshDate;          // 最后刷新时间--刷新系统消息数据标识

    public Date getRefreshDate() {
        return refreshDate;
    }

    public void setRefreshDate(Date refreshDate) {
        this.refreshDate = refreshDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public Date getLastDate() {
        return lastDate;
    }

    public void setLastDate(Date lastDate) {
        this.lastDate = lastDate;
    }
}
