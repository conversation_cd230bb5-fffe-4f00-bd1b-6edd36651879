package net.xplife.system.xeasylabel.dto;

import com.alibaba.fastjson.annotation.JSONField;
import net.xplife.system.xeasylabel.vo.PicVo;

import java.io.Serializable;
import java.util.Date;

public class DraftsDto implements Serializable {
    /**
     * 草稿箱Dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            pic;                  // 资源图片地址
    private String            picPrint;             // 直接打印的地址
    private String            data;                 // 资源数据地址
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createTime;           // 创建 时间
    private Object            param;                // 草稿箱参数
    private int               type;                 // 草稿箱类型 0--草稿箱 1--我的历史
    private int               placeType;            // 设置
    private PaperInfoDto      paperObj;
    private String            fmtTime;
    private PicVo             picVo;
    private String            printerType;            // 打印机型号
    private String            codeId;               // 用户的codeId
    private int forbid;   // 是否禁止：0：否； 1：是

    public String getCodeId() {
        return codeId;
    }

    public void setCodeId(String codeId) {
        this.codeId = codeId;
    }

    public DraftsDto() {
    }

    public String getFmtTime() {
        return fmtTime;
    }

    public void setFmtTime(String fmtTime) {
        this.fmtTime = fmtTime;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Object getParam() {
        return param;
    }

    public void setParam(Object param) {
        this.param = param;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getPlaceType() {return placeType; }

    public void setPlaceType(int placeType) { this.placeType = placeType; }

    public PaperInfoDto getPaperObj() {
        return paperObj;
    }

    public void setPaperObj(PaperInfoDto paperObj) {
        this.paperObj = paperObj;
    }

    public PicVo getPicVo() {
        return picVo;
    }

    public void setPicVo(PicVo picVo) {
        this.picVo = picVo;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    public String getPicPrint() {
        return picPrint;
    }

    public void setPicPrint(String picPrint) {
        this.picPrint = picPrint;
    }

    public int getForbid() {
        return forbid;
    }

    public void setForbid(int forbid) {
        this.forbid = forbid;
    }
}
