package net.xplife.system.xeasylabel.dto;

import java.io.Serializable;
import java.util.Map;

public class MaterialResourceDto implements Serializable {
    /**
     * 素材库Dto
     */
    private static final long   serialVersionUID = 1L;
    private String              mId;                  // 素材库ID
    private int                 length;               // 纸长度
    private Map<String, PicDto> resMap;               // 资源集合
    private int                 placeType;            // 0：竖向；1：横向
    private int                 isNew;                // 是否标识new
    private String              label;                // 标签
    private String              localeCode;           // 所属语言
    private int                 sortNum;

    public String getmId() {
        return mId;
    }

    public void setmId(String mId) {
        this.mId = mId;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public Map<String, PicDto> getResMap() {
        return resMap;
    }

    public void setResMap(Map<String, PicDto> resMap) {
        this.resMap = resMap;
    }

    public int getPlaceType() {
        return placeType;
    }

    public void setPlaceType(int placeType) {
        this.placeType = placeType;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getLocaleCode() {
        return localeCode;
    }

    public void setLocaleCode(String localeCode) {
        this.localeCode = localeCode;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }
}
