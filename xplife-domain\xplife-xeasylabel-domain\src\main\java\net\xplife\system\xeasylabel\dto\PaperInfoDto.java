package net.xplife.system.xeasylabel.dto;
import java.io.Serializable;

public class PaperInfoDto implements Serializable {
    /**
     * 纸张信息dto
     */
    private static final long serialVersionUID = 1L;
    private String            name;                 // 名称
    private int               width;                 // 长
    private int               height;               // 宽
    private String            lengthType;               // 打印纸类型

    private int paperType;   // 打印纸类型
    private float paperLength; // 打印纸长度
    private float paperWidth;  // 打印纸宽度
    private int paperColor;// 纸张颜色

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public String getLengthType() {
        return lengthType;
    }

    public void setLengthType(String lengthType) {
        this.lengthType = lengthType;
    }

    public int getPaperType() {
        return paperType;
    }

    public void setPaperType(int paperType) {
        this.paperType = paperType;
    }

    public float getPaperLength() {
        return paperLength;
    }

    public void setPaperLength(float paperLength) {
        this.paperLength = paperLength;
    }

    public float getPaperWidth() {
        return paperWidth;
    }

    public void setPaperWidth(float paperWidth) {
        this.paperWidth = paperWidth;
    }

    public int getPaperColor() {
        return paperColor;
    }

    public void setPaperColor(int paperColor) {
        this.paperColor = paperColor;
    }
}
