package net.xplife.system.xeasylabel.dto;
import java.io.Serializable;
import java.util.Date;

/***
 * 芯意贴记录最新使用的一次标签纸信息
 */
public class RecentPaperInfoDto implements Serializable {
    /**
     * 纸张信息dto
     */
    private static final long serialVersionUID = 1L;
    private int paperType;   // 打印纸类型
    private float paperLength; // 打印纸长度
    private float paperWidth;  // 打印纸宽度
    private int paperColor;// 纸张颜色
    private String printerType;// 打印机类型
    protected Date createtime;

    public int getPaperType() {
        return paperType;
    }

    public void setPaperType(int paperType) {
        this.paperType = paperType;
    }

    public float getPaperLength() {
        return paperLength;
    }

    public void setPaperLength(float paperLength) {
        this.paperLength = paperLength;
    }

    public float getPaperWidth() {
        return paperWidth;
    }

    public void setPaperWidth(float paperWidth) {
        this.paperWidth = paperWidth;
    }

    public int getPaperColor() {
        return paperColor;
    }

    public void setPaperColor(int paperColor) {
        this.paperColor = paperColor;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj instanceof RecentPaperInfoDto) {
            RecentPaperInfoDto s = (RecentPaperInfoDto) obj;
            return this.paperType == s.paperType
                    && this.paperLength == s.paperLength
                    && this.paperWidth==s.paperWidth
                    && this.paperColor==s.paperColor;
        } else {
            return false;
        }
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }
}
