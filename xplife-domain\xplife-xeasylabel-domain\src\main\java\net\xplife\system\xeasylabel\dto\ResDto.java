package net.xplife.system.xeasylabel.dto;

import java.io.Serializable;

public class ResDto implements Serializable {
    /**
     * 素材资源dto
     */
    private static final long serialVersionUID = 1L;
    private PicDto            resUrl;               // 图片资源地址
    private PicDto            downloadUrl;          // 字体下载地址
    private PicDto            topUrl;               // 上图地址
    private PicDto            downUrl;              // 下图地址
    private PicDto            addLeftUrl;           // 添加左图地址
    private PicDto            leftUrl;              // 左图地址
    private PicDto            rightUrl;             // 右图地址
    private PicDto            listUrl;              // 列表地址
    private PicDto            bgUrl;                // 背景地址
    private PicDto            leftTopUrl;           //左上
    private PicDto            rightTopUrl;          //左上
    private PicDto            leftDownUrl;          //左上
    private PicDto            rightDownUrl;         //左上

    public PicDto getListUrl() {
        return listUrl;
    }

    public void setListUrl(PicDto listUrl) {
        this.listUrl = listUrl;
    }

    public PicDto getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(PicDto downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public PicDto getResUrl() {
        return resUrl;
    }

    public void setResUrl(PicDto resUrl) {
        this.resUrl = resUrl;
    }

    public PicDto getTopUrl() {
        return topUrl;
    }

    public void setTopUrl(PicDto topUrl) {
        this.topUrl = topUrl;
    }

    public PicDto getDownUrl() {
        return downUrl;
    }

    public void setDownUrl(PicDto downUrl) {
        this.downUrl = downUrl;
    }

    public PicDto getAddLeftUrl() {
        return addLeftUrl;
    }

    public void setAddLeftUrl(PicDto addLeftUrl) {
        this.addLeftUrl = addLeftUrl;
    }

    public PicDto getLeftUrl() {
        return leftUrl;
    }

    public void setLeftUrl(PicDto leftUrl) {
        this.leftUrl = leftUrl;
    }

    public PicDto getRightUrl() {
        return rightUrl;
    }

    public void setRightUrl(PicDto rightUrl) {
        this.rightUrl = rightUrl;
    }

    public PicDto getBgUrl() {
        return bgUrl;
    }

    public void setBgUrl(PicDto bgUrl) {
        this.bgUrl = bgUrl;
    }

    public PicDto getLeftTopUrl() {
        return leftTopUrl;
    }

    public void setLeftTopUrl(PicDto leftTopUrl) {
        this.leftTopUrl = leftTopUrl;
    }

    public PicDto getRightTopUrl() {
        return rightTopUrl;
    }

    public void setRightTopUrl(PicDto rightTopUrl) {
        this.rightTopUrl = rightTopUrl;
    }

    public PicDto getLeftDownUrl() {
        return leftDownUrl;
    }

    public void setLeftDownUrl(PicDto leftDownUrl) {
        this.leftDownUrl = leftDownUrl;
    }

    public PicDto getRightDownUrl() {
        return rightDownUrl;
    }

    public void setRightDownUrl(PicDto rightDownUrl) {
        this.rightDownUrl = rightDownUrl;
    }
}
