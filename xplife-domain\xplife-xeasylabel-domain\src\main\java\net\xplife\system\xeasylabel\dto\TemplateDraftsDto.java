package net.xplife.system.xeasylabel.dto;

import net.xplife.system.xeasylabel.vo.PicVo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/27 16:38
 * @description：编辑模板，由用户的打印记录中产生
 * @modified By：
 * @version: $
 */
public class TemplateDraftsDto implements Serializable {
    private String id;
    private String name;            // 名称
    private String content;         // 描述
//    private String recommend;       // 推荐描述
    private DraftsDto draftsDto;    // 图的具体信息，参考打印记录里面的数据结构
    private String pic;             // 缩略图地址
    private PicVo picVo;
    private String type;            // 类型：居家收纳：living；厨房收纳：kitchen；办公收纳：office
    private int isHot;              // 是否热门。0：非热门； 1：热门
    private int sortNum;            // 排序字段
    private int hadCollected;       // 是否已收藏，0：否，1：已收藏
    private String localeCode;      // 所属语言种类
    private String printerType;     // 打印机类型
    private String paperType;          // 纸张类型
    private String paperSize;           // 纸张尺寸

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public DraftsDto getDraftsDto() {
        return draftsDto;
    }

    public void setDraftsDto(DraftsDto draftsDto) {
        this.draftsDto = draftsDto;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getIsHot() {
        return isHot;
    }

    public void setIsHot(int isHot) {
        this.isHot = isHot;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getHadCollected() {
        return hadCollected;
    }

    public void setHadCollected(int hadCollected) {
        this.hadCollected = hadCollected;
    }
//    public String getRecommend() {
//        return recommend;
//    }
//
//    public void setRecommend(String recommend) {
//        this.recommend = recommend;
//    }

    public PicVo getPicVo() {
        return picVo;
    }

    public String getLocaleCode() {
        return localeCode;
    }

    public void setLocaleCode(String localeCode) {
        this.localeCode = localeCode;
    }

    public void setPicVo(PicVo picVo) {
        this.picVo = picVo;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    public String getPaperType() {
        return paperType;
    }

    public void setPaperType(String paperType) {
        this.paperType = paperType;
    }

    public String getPaperSize() {
        return paperSize;
    }

    public void setPaperSize(String paperSize) {
        this.paperSize = paperSize;
    }
}
