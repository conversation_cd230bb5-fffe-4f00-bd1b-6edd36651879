package net.xplife.system.xeasylabel.entity;

import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.xeasylabel.vo.DraftsParamVo;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 草稿箱
 */
@Document(collection = "V1_Drafts")
public class Drafts extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_Drafts";
    public static final String USER_ID_FIELD    = "userId";
    public static final String TYPE_FIELD       = "type";
    public static final String SUB_TYPE_FIELD   = "subType";
    public static final String LENGTH_FIELD     = "length";
    private String             resPicId;                      // 资源图片ID
    private String             resDataId;                     // 资源数据ID
    @Indexed(name = "_userid_")
    private String             userId;                        // 用户ID
    private int                type;                          // 草稿箱类型 0--草稿箱 1--我的历史
    private int                subType;                       // 草稿箱副类型 0编辑纸条 1清单 2大字横幅 3便利贴 4网页打印
    private int                length;                        // 纸张长度
    private DraftsParamVo draftsParam;                   // 草稿箱参数
    private int                placeType;                     // 编辑方向，可选，默认0，打竖，1为横向

    private int                 paperType;              // 纸张类型
    private float                 paperLength;            // 纸张长度
    private float                 paperWidth;             // 纸张宽度
    private int              paperColor;                // 纸张颜色

    private String              printerType;            // 打印机型号
    private String              picPrintUrl;            // 直接打印的url地址

    public String getPicPrintUrl() {
        return picPrintUrl;
    }

    public void setPicPrintUrl(String picPrintUrl) {
        this.picPrintUrl = picPrintUrl;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    public String getResPicId() {
        return resPicId;
    }

    public void setResPicId(String resPicId) {
        this.resPicId = resPicId;
    }

    public String getResDataId() {
        return resDataId;
    }

    public void setResDataId(String resDataId) {
        this.resDataId = resDataId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public DraftsParamVo getDraftsParam() {
        return draftsParam;
    }

    public void setDraftsParam(DraftsParamVo draftsParam) {
        this.draftsParam = draftsParam;
    }

    public int getPlaceType() { return placeType; }

    public void setPlaceType(int placeType) { this.placeType = placeType; }

    public int getPaperType() {
        return paperType;
    }

    public void setPaperType(int paperType) {
        this.paperType = paperType;
    }

    public float getPaperLength() {
        return paperLength;
    }

    public void setPaperLength(float paperLength) {
        this.paperLength = paperLength;
    }

    public float getPaperWidth() {
        return paperWidth;
    }

    public void setPaperWidth(float paperWidth) {
        this.paperWidth = paperWidth;
    }

    public int getPaperColor() {
        return paperColor;
    }

    public void setPaperColor(int paperColor) {
        this.paperColor = paperColor;
    }
}
