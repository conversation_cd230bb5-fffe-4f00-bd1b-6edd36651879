package net.xplife.system.xeasylabel.entity;

import net.xplife.system.mongo.common.IdEntity;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

/**
 * 素材库
 */
@Document(collection = "V1_Material")
public class Material extends IdEntity {
    private static final long   serialVersionUID = 1L;
    public static final String  COLL             = "V1_Material";
    public static final String  SORT_FIELD       = "sort";
    private String              pId;                             // 父ID
    private String              name;                            // 名称
    private int                 sort;                            // 排序
    private int                 type;                            // 类型
    private int                 subType;                         // 副类型
    private int                 hasContent;                      // 是否有内容
    private String              label;                           // 标签，用分号隔开
    private Map<String, String> icon;                            // 图标地址
    private List<String>        position;                        // 方位
    private String com; // 模板组件名
    private String zhTitle;
    private String enTitle;

    public List<String> getPosition() {
        return position;
    }

    public void setPosition(List<String> position) {
        this.position = position;
    }

    public Map<String, String> getIcon() {
        return icon;
    }

    public void setIcon(Map<String, String> icon) {
        this.icon = icon;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public int getHasContent() {
        return hasContent;
    }

    public void setHasContent(int hasContent) {
        this.hasContent = hasContent;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getpId() {
        return pId;
    }

    public void setpId(String pId) {
        this.pId = pId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCom() {
        return com;
    }

    public void setCom(String com) {
        this.com = com;
    }

    public String getZhTitle() {
        return zhTitle;
    }

    public void setZhTitle(String zhTitle) {
        this.zhTitle = zhTitle;
    }

    public String getEnTitle() {
        return enTitle;
    }

    public void setEnTitle(String enTitle) {
        this.enTitle = enTitle;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
