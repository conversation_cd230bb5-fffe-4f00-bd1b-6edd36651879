package net.xplife.system.xeasylabel.entity;

import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.xeasylabel.vo.PicVo;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * 素材库资源
 */
@Document(collection = "V1_MaterialResource")
public class MaterialResource extends IdEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_MaterialResource";
    public static final String M_ID_FIELD       = "mId";
    public static final String LOCALE_CODE_FIELD       = "localeCode";
    private String             mId;                                     // 素材库ID
    private int                length;                                  // 纸张长度
    private Map<String, PicVo> resMap;                                  // 资源集合
    private int                type;                                    // 主类型
    private int                subType;                                 // 副类型
    private int                placeType;                               // 0：竖向；1：横向
    private int                isNew;                                   // 是否标识new
    private String             label;                                   // 标签
    private int                sortNum;                                 // 排序字段

    private String localeCode;  // 语种

    public String getmId() {
        return mId;
    }

    public void setmId(String mId) {
        this.mId = mId;
    }

    public Map<String, PicVo> getResMap() {
        return resMap;
    }

    public void setResMap(Map<String, PicVo> resMap) {
        this.resMap = resMap;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public int getPlaceType() {
        return placeType;
    }

    public void setPlaceType(int placeType) {
        this.placeType = placeType;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getLocaleCode() {
        return localeCode;
    }

    public void setLocaleCode(String localeCode) {
        this.localeCode = localeCode;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }
}
