package net.xplife.system.xeasylabel.entity;

import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.xeasylabel.dto.DraftsDto;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/27 16:38
 * @description：编辑模板，由用户的打印记录中产生
 * @modified By：
 * @version: $
 */
@Document(collection = "V1_TemplateDrafts")
public class TemplateDrafts extends IdEntity {

    public static final String TYPE_FIELD       = "type";
    public static final String LOCALE_CODE_FIELD       = "localeCode";
    public static final String SORT_NUM_FIELD       = "sortNum";
    public static final String IS_HOT = "isHot";
    public static final String RECOMMEND_COLUMN = "recommend";
    public static final String NAME_COLUMN = "name";
    public static final String CONTENT_COLUMN = "content";

    @Indexed(name = "_name_")
    private String name;            // 名称
    @Indexed(name = "_content_")
    private String content;         // 描述
    @Indexed(name = "_recommend_")
    private String recommend;       // 推荐描述
    private DraftsDto draftsDto;    // 图的具体信息，参考打印记录里面的数据结构
    private String pic;             // 缩略图地址
    @Indexed(name = "_type_")
    private String type;            // 类型：居家收纳：living；厨房收纳：kitchen；办公收纳：office
    private int isHot;              // 是否热门。0：非热门； 1：热门
    private int sortNum;            // 排序字段
    @Indexed(name = "_localeCode_")
    private String localeCode;      // 语言国际

    @Indexed(name = "_printerType_")
    private String printerType;     // 符合打印机的类型
    @Indexed(name = "_paperType_")
    private String paperType;     // 符合打印机的类型
    @Indexed(name = "_paperSize_")
    private String paperSize;           // 纸张尺寸开；

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public DraftsDto getDraftsDto() {
        return draftsDto;
    }

    public void setDraftsDto(DraftsDto draftsDto) {
        this.draftsDto = draftsDto;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getIsHot() {
        return isHot;
    }

    public void setIsHot(int isHot) {
        this.isHot = isHot;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getRecommend() {
        return recommend;
    }

    public String getLocaleCode() {
        return localeCode;
    }

    public void setLocaleCode(String localeCode) {
        this.localeCode = localeCode;
    }

    public void setRecommend(String recommend) {
        this.recommend = recommend;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    public String getPaperType() {
        return paperType;
    }

    public void setPaperType(String paperType) {
        this.paperType = paperType;
    }

    public String getPaperSize() {
        return paperSize;
    }

    public void setPaperSize(String paperSize) {
        this.paperSize = paperSize;
    }
}
