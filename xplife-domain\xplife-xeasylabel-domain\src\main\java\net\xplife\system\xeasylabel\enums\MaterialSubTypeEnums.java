package net.xplife.system.xeasylabel.enums;
import java.util.LinkedHashMap;

/**
 * 素材类型
 */
public enum MaterialSubTypeEnums {
    // 边框---热门、简约、可爱、花纹、中国风、古典
    BORDER_HOT(0, "hot", "热门", "material_sub_type_hot"),
    BORDER_SIMPLE(0, "simple", "简约", "material_sub_type_simple"),
    BORDER_LOVELY(0, "lovely", "可爱", "material_sub_type_lovely"),
    BORDER_FIGURE(0, "figure", "花纹", "material_sub_type_figure"),
    BORDER_CHINESE(0, "chinese", "中国风", "material_sub_type_chinese"),
    BORDER_CLASSICAL(0, "classical", "古典", "material_sub_type_classical"),

    // 装饰--- 热门、收纳、护肤、厨房、宠物、符号、货币、表情
    DECORATE_HOT(1,"hot","热门", "material_sub_type_hot"),
    DECORATE_STORAGE(1,"storage","收纳", "material_sub_type_storage"),
    DECORATE_BUBBLE(1,"bubble","气泡", "material_sub_type_bubble"),
    DECORATE_FRUITS(1,"fruits","食物", "material_sub_type_fruits"),
    DECORATE_SYMBOL(1,"symbol","符号", "material_sub_type_symbol"),
    DECORATE_KITCHEN(1,"kitchen","厨房", "material_sub_type_kitchen"),
    DECORATE_SKIN(1,"skin","护肤", "material_sub_type_skin"),
    DECORATE_OFFICE(1,"office","办公", "material_sub_type_office"),
    DECORATE_STUDY(1,"study","学习", "material_sub_type_study"),
    DECORATE_TRAVEL(1,"travel","旅游", "material_sub_type_travel"),
    DECORATE_FACE(1,"face","表情", "material_sub_type_face"),
    DECORATE_PET(1,"pet","动物", "material_sub_type_pet"),
    DECORATE_PLANT(1,"plant","植物", "material_sub_type_plant"),
    DECORATE_CARTOON(1,"cartoon","卡通", "material_sub_type_cartoon"),
//    DECORATE_CURRENCY(1,"currency","货币"),
//    DECORATE_WATER(1,"water","水产"),
    ;
    //, { id: 'plant', name: '植物' }, { id: 'water', name: '水产' }, { id: 'cartoon', name: '卡通' }, { id: 'travel', name: '旅游' }, { id: 'study', name: '学习' }
    private final int                                   subType;
    private final String                                value;
    private final String                                desc;
    private final String                                languageCode;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (MaterialSubTypeEnums materialSubTypeEnums : MaterialSubTypeEnums.values()) {
            map.put(materialSubTypeEnums.getDesc(), materialSubTypeEnums.getValue());
        }
    }

    MaterialSubTypeEnums(Integer subType, String value, String desc, String languageCode) {
        this.subType = subType;
        this.value = value;
        this.desc = desc;
        this.languageCode = languageCode;
    }

    public int getSubType() {
        return subType;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }

    public String getLanguageCode() {
        return languageCode;
    }
}
