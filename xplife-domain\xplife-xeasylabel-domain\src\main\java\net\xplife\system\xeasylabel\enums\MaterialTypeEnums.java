package net.xplife.system.xeasylabel.enums;
import java.util.LinkedHashMap;

/**
 * 素材类型
 */
public enum MaterialTypeEnums {
    EDIT(1, "字体"),
    DETAIL(2, "装饰和边框");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (MaterialTypeEnums materialTypeEnums : MaterialTypeEnums.values()) {
            map.put(materialTypeEnums.getValue(), materialTypeEnums.getDesc());
        } 
    }

    MaterialTypeEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
