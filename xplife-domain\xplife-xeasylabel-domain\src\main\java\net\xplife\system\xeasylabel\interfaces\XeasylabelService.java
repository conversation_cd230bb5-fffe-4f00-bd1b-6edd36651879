package net.xplife.system.xeasylabel.interfaces;

import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import net.xplife.system.xeasylabel.dto.DraftsDto;
import net.xplife.system.xeasylabel.dto.ResourceDataDto;
import net.xplife.system.xeasylabel.entity.Drafts;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date ：Created in 2024/3/27 10:57
 * @description：
 * @modified By：
 * @version: $
 */
@FeignClient(name = "yoyin-xeasylabel", configuration = FeignConfiguration.class)
public interface XeasylabelService {
    @RequestMapping(value = "/xeasylabel/v1/drafts/getdraftshistorypage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<Drafts> findDraftsPagefindPage(@RequestParam("pageno") int pageno,
                                               @RequestParam("pagesize") int pagesize,
                                               @RequestParam("otherid") String otherid,
                                               @RequestParam("subtype") String subtype,
                                               @RequestParam("length") String length,
                                               @RequestParam("printertype") String printertype,
                                               @RequestParam("begintime") String begintime,
                                               @RequestParam("endtime") String endtime);

    @RequestMapping(value = "/xeasylabel/v1/drafts/getdraftspage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<DraftsDto> findDraftsPagePage(@RequestParam("pageno") int pageno,
                                              @RequestParam("pagesize") int pagesize,
                                              @RequestParam("createUserId") String createUserId,
                                              @RequestParam("type") String type,
                                              @RequestParam("subtype") String subtype,
                                              @RequestParam("length") String length);

    /**
     * 获取资源数据信息
     *
     * @return
     */
    @RequestMapping(value = "/xeasylabel/v1/drafts/getresourcedata", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResourceDataDto getResourceDataById(@RequestParam("resid") String resId);
}
