package net.xplife.system.xeasylabel.vo;
import java.io.Serializable;

public class PicVo implements Serializable {
    /**
     * 图片信息vo
     */
    private static final long serialVersionUID = 1L;
    private String            pic;                  // 图片地址
    private int               height;               // 高
    private int               width;                // 宽
    private double            size;                 // 大小
    private String            startPoint;           // 开始点
    private String            endPoint;             // 结束点

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public double getSize() {
        return size;
    }

    public void setSize(double size) {
        this.size = size;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }
}
