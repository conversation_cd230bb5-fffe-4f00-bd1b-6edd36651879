package net.xplife.system.cache.common;
import java.lang.reflect.Type;
import java.util.Date;
import net.xplife.system.cache.enums.CommonCacheEnums;
import net.xplife.system.cache.kit.CacheKit;
import net.xplife.system.cache.utils.JsonKit;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.util.core.ToolsKit;
import cn.hutool.core.date.DatePattern;
/**
 * 统计公共服务类
 * 
 * <AUTHOR> 2018年11月28日
 */
public class BaseStatisticsCacheService {
    /**
     * 获取月统计数据
     * 
     * @param month
     * @param suffix
     * @param type
     * @return
     */
    public <T> T getMonthStatisticsDto(Date month, String suffix, Type type) {
        String value = CacheKit.cache().get(getCacheFlagKey(suffix) + ToolsKit.Date.format(month, "yyyyMM"), String.class);
        if (ToolsKit.isEmpty(value)) {
            return null;
        }
        return JsonKit.jsonParseObject(value, type);
    }

    /**
     * 保存月统计数据
     * 
     * @param statisticsDto
     * @param month
     * @param suffix
     */
    public void setMonthStatisticsDto(Object statisticsDto, Date month, String suffix) {
        CacheKit.cache().set(getCacheFlagKey(suffix) + ToolsKit.Date.format(month, "yyyyMM"), statisticsDto, ToolsConst.WEEK_SECOND);
    }

    /**
     * 获取天统计数据
     * 
     * @param day
     * @param suffix
     * @param type
     * @return
     */
    public <T> T getDayStatisticsDto(Date day, String suffix, Type type) {
        String value = CacheKit.cache().get(getCacheFlagKey(suffix) + ToolsKit.Date.format(day, DatePattern.PURE_DATE_PATTERN), String.class);
        if (ToolsKit.isEmpty(value)) {
            return null;
        }
        return JsonKit.jsonParseObject(value, type);
    }

    /**
     * 保存天统计数据
     * 
     * @param statisticsDto
     * @param day
     * @param suffix
     */
    public void setDayStatisticsDto(Object statisticsDto, Date day, String suffix) {
        CacheKit.cache().set(getCacheFlagKey(suffix) + ToolsKit.Date.format(day, DatePattern.PURE_DATE_PATTERN), statisticsDto, ToolsConst.WEEK_SECOND);
    }

    /**
     * 获取缓存标识key
     * 
     * @param suffix
     *            后缀
     * @return
     */
    private String getCacheFlagKey(String suffix) {
        return CommonCacheEnums.COMMON_FLAG_BY.getKey() + suffix;
    }
}
