package net.xplife.system.cache.common;

import java.util.Date;
import net.xplife.system.cache.enums.CommonCacheEnums;
import net.xplife.system.cache.kit.CacheKit;
import net.xplife.system.tools.util.core.ToolsKit;

/**
 * 公共服务类
 * 
 * <AUTHOR> 2018年11月28日
 */
public class CacheCommonTools {
    /**
     * 保存缓存标识
     * 
     * @param key
     * @param value
     * @param seconds
     */
    public void saveCacheFlagNoDate(String key, String value, int seconds) {
        CacheKit.cache().incr(getCacheFlagKey(key) + ":" + value, seconds);
    }

    /**
     * 获取缓存标识
     * 
     * @param key
     * @param value
     */
    public String getCacheFlagNoDate(String key, String value) {
        return CacheKit.cache().get(getCacheFlagKey(key) + ":" + value, String.class);
    }

    /**
     * 保存缓存标识
     * 
     * @param key
     * @param value
     * @param seconds
     */
    public void saveCacheFlag(String key, String value, int seconds) {
        String date = ToolsKit.Date.format(new Date(), "yyyyMMdd");
        CacheKit.cache().incr(getCacheFlagKey(key) + date + ":" + value, seconds);
    }

    /**
     * 获取缓存标识
     * 
     * @param key
     * @param value
     * @return
     */
    public String getCacheFlag(String key, String value) {
        String date = ToolsKit.Date.format(new Date(), "yyyyMMdd");
        return CacheKit.cache().get(getCacheFlagKey(key) + date + ":" + value, String.class);
    }

    /**
     * 获取缓存标识key
     * 
     * @param suffix
     *            后缀
     * @return
     */
    private String getCacheFlagKey(String suffix) {
        return CommonCacheEnums.COMMON_FLAG_BY.getKey() + suffix;
    }

    /**
     * 保存缓存数据
     * 
     * @param key
     * @param value
     * @param seconds
     */
    public void saveCacheValue(String key, Object value, int seconds) {
        String date = ToolsKit.Date.format(new Date(), "yyyyMMdd");
        CacheKit.cache().set(getCacheFlagKey(key) + ":" + date, value, seconds);
    }

    /**
     * 获取缓存数据
     * 
     * @param key
     * @param typeReference
     *            类型
     * @return
     */
    public <T> T getCacheValue(String key, Class<T> typeReference) {
        String date = ToolsKit.Date.format(new Date(), "yyyyMMdd");
        return CacheKit.cache().get(getCacheFlagKey(key) + ":" + date, typeReference);
    }
}
