package net.xplife.system.cache.core;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import net.xplife.system.cache.common.CacheConfig;
import net.xplife.system.cache.common.ExecuteAction;
import net.xplife.system.cache.common.RedisListener;
import net.xplife.system.cache.model.TupleDto;
import net.xplife.system.cache.utils.JedisClusterPoolUtils;
import net.xplife.system.cache.utils.JedisPoolUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.alibaba.fastjson.TypeReference;
import net.xplife.system.cache.model.RedisMessage;
import net.xplife.system.cache.utils.SerializableUtils;
import net.xplife.system.tools.util.core.ToolsKit;
import redis.clients.jedis.GeoCoordinate;
import redis.clients.jedis.GeoRadiusResponse;
import redis.clients.jedis.GeoUnit;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;
import redis.clients.jedis.Tuple;
import redis.clients.jedis.exceptions.JedisConnectionException;
import redis.clients.jedis.exceptions.JedisException;
import redis.clients.jedis.params.geo.GeoRadiusParam;
import redis.clients.util.SafeEncoder;

public class RedisClient implements CacheInterface {
    private final static Logger logger       = LoggerFactory.getLogger(RedisClient.class);
    private static RedisClient  ourInstance;
    private static Lock         cacheKitLock = new ReentrantLock();

    public static RedisClient getInstance() {
        try {
            cacheKitLock.lock();
            if (null == ourInstance) {
                ourInstance = new RedisClient();
            }
        } catch (Exception e) {
            logger.info(e.getMessage(), e);
        } finally {
            cacheKitLock.unlock();
        }
        return ourInstance;
    }

    private RedisClient() {
    }

    private static <T> T call(ExecuteAction cacheAction) {
        T result = null;
        if (!CacheConfig.isCluster()) {
            Jedis jedis = JedisPoolUtils.getJedis();
            try {
                result = (T) cacheAction.execute(jedis);
            } catch (Exception e) {
                JedisPoolUtils.returnBrokenResource(jedis);
                logger.info(e.getMessage(), e);
            } finally {
                JedisPoolUtils.returnResource(jedis);
            }
        } else {
            JedisCluster jedisCluster = JedisClusterPoolUtils.getJedisCluster();
            result = (T) cacheAction.execute(jedisCluster);
        }
        return result;
    }

    /**
     * 是否集群对象
     * 
     * @param jedisObj
     *            jedis对象
     * @return 是集群返回true
     */
    private boolean isCluster(Object jedisObj) {
        return (jedisObj instanceof JedisCluster) ? true : false;
    }

    private Jedis c2j(Object jedisObj) {
        return (Jedis) jedisObj;
    }

    private JedisCluster c2jc(Object jedisObj) {
        return (JedisCluster) jedisObj;
    }

    private byte[] getBytes4Cluster(String value) {
        return ToolsKit.isEmpty(value) ? null : value.getBytes();
    }

    /**
     * 根据key取值
     * 
     * @param key
     * @return
     */
    public <T> T get(final String key, final Class<T> typeReference) {
        return call(new ExecuteAction<T>() {
            @Override
            public T execute(Object jedisObj) {
                try {
                    byte[] bytes = (isCluster(jedisObj)) ? getBytes4Cluster(c2jc(jedisObj).get(key)) : c2j(jedisObj).get(SafeEncoder.encode(key));
                    if (ToolsKit.isNotEmpty(bytes)) {
                        String str = new String(bytes, "UTF-8");
                        if (typeReference.equals(String.class)) {
                            return (T) str;
                        } else if (typeReference.equals(Integer.class) || typeReference.equals(int.class)) {
                            return (T) new Integer(str);
                        } else if (typeReference.equals(Long.class) || typeReference.equals(long.class)) {
                            return (T) new Long(str);
                        } else if (typeReference.equals(Double.class) || typeReference.equals(double.class)) {
                            return (T) new Double(str);
                        }
                        return (T) SerializableUtils.deserialize(bytes, typeReference);
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 取值
     * 
     * @param key
     * @param type
     *            泛型
     * @return
     */
    public <T> T get(final String key, final TypeReference<T> type) {
        return call(new ExecuteAction<T>() {
            @Override
            public T execute(Object jedisObj) {
                try {
                    byte[] bytes = (isCluster(jedisObj)) ? getBytes4Cluster(c2jc(jedisObj).get(key)) : c2j(jedisObj).get(SafeEncoder.encode(key));
                    if (ToolsKit.isNotEmpty(bytes)) {
                        return (T) SerializableUtils.deserialize(bytes, type);
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    public <T> List<T> getArray(final String key, final Class<T> typeReference) {
        return call(new ExecuteAction<List<T>>() {
            @Override
            public List<T> execute(Object jedisObj) {
                try {
                    byte[] bytes = (!isCluster(jedisObj)) ? c2j(jedisObj).get(SafeEncoder.encode(key)) : getBytes4Cluster(c2jc(jedisObj).get(key));
                    if (ToolsKit.isNotEmpty(bytes)) {
                        return (List<T>) SerializableUtils.deserializeArray(bytes, typeReference);
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                    return null;
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 按key-value方式将值保存到redis, 缓存时间为seconds, 过期后会自动将该key指向的value删除
     * 
     * @param key
     *            关键字
     * @param value
     *            值
     * @param seconds
     *            缓存时间，秒作单位
     * @return
     */
    public boolean set(final String key, final Object value, final int seconds) {
        return call(new ExecuteAction<Boolean>() {
            @Override
            public Boolean execute(Object jedisObj) {
                String result = null;
                try {
                    if (value instanceof String) {
                        result = isCluster(jedisObj) ? c2jc(jedisObj).setex(key, seconds, (String) value) : c2j(jedisObj).setex(key, seconds, (String) value);
                    } else {
                        result = isCluster(jedisObj) ? c2jc(jedisObj).setex(key, seconds, SerializableUtils.serializeString(value))
                                : c2j(jedisObj).setex(SafeEncoder.encode(key), seconds, SerializableUtils.serialize(value));
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return "OK".equalsIgnoreCase(result);
            }
        });
    }

    /**
     * 根据key删除指定的内容
     * 
     * @param keys
     * @return
     */
    public long del(final String... keys) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    if (isCluster(jedisObj)) {
                        try {
                            for (String keyItem : keys) {
                                c2jc(jedisObj).del(keyItem);
                            }
                            return 1L;
                        } catch (Exception e) {
                            return 0L;
                        }
                    } else {
                        return c2j(jedisObj).del(keys);
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 将内容添加到list里的第一位
     * 
     * @param key
     *            关键字
     * @param value
     *            内容
     * @return
     */
    public long lpush(final String key, final Object value) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    if (value instanceof String) {
                        return isCluster(jedisObj) ? c2jc(jedisObj).lpush(key, (String) value) : c2j(jedisObj).lpush(key, (String) value);
                    } else {
                        return isCluster(jedisObj) ? c2jc(jedisObj).lpush(key, SerializableUtils.serializeString(value))
                                : c2j(jedisObj).lpush(SafeEncoder.encode(key), SerializableUtils.serialize(value));
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 将内容添加到list里的最后一位
     * 
     * @param key
     *            关键字
     * @param value
     *            内容
     * @return
     */
    public long rpush(final String key, final Object value) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    if (value instanceof String) {
                        return isCluster(jedisObj) ? c2jc(jedisObj).rpush(key, SerializableUtils.serializeString(value))
                                : c2j(jedisObj).rpush(key, (String) value);
                    } else {
                        return isCluster(jedisObj) ? c2jc(jedisObj).rpush(key, SerializableUtils.serializeString(value))
                                : c2j(jedisObj).rpush(SafeEncoder.encode(key), SerializableUtils.serialize(value));
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 根据key取出list集合
     * 
     * @param key
     *            关键字
     * @param start
     *            开始位置(0表示第一个元素)
     * @param end
     *            结束位置(-1表示最后一个元素)
     * @return
     */
    public List<String> lrange(final String key, final int start, final int end) {
        return call(new ExecuteAction<List<String>>() {
            @Override
            public List<String> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).lrange(key, start, end) : c2j(jedisObj).lrange(key, start, end);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    public long lrem(final String key, final int count, final Object value) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    if (value instanceof String) {
                        return isCluster(jedisObj) ? c2jc(jedisObj).lrem(key, count, (String) value) : c2j(jedisObj).lrem(key, count, (String) value);
                    } else {
                        return isCluster(jedisObj) ? c2jc(jedisObj).lrem(key, count, SerializableUtils.serializeString(value))
                                : c2j(jedisObj).lrem(SafeEncoder.encode(key), count, SerializableUtils.serialize(value));
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 向名称为key的hash中添加元素(map)
     * 
     * @param key
     * @param values
     *            map<String,?>
     * @return
     */
    public boolean hmset(final String key, final Map<String, String> values, final int seconds) {
        return call(new ExecuteAction<Boolean>() {
            @Override
            public Boolean execute(Object jedisObj) {
                try {
                    boolean isCluster = isCluster(jedisObj);
                    String isok = null;
                    if (null != values) {
                        if (!isCluster) {
                            Map<byte[], byte[]> map = new HashMap<byte[], byte[]>(values.size());
                            for (Iterator<Map.Entry<String, String>> it = values.entrySet().iterator(); it.hasNext();) {
                                Map.Entry<String, String> entry = it.next();
                                map.put(SafeEncoder.encode(entry.getKey()), SafeEncoder.encode(entry.getValue()));
                            }
                            isok = c2j(jedisObj).hmset(SafeEncoder.encode(key), map);
                        } else {
                            isok = c2jc(jedisObj).hmset(key, values);
                        }
                        boolean isOk = "OK".equalsIgnoreCase(isok);
                        if (isOk) {
                            expire(key, seconds);
                        }
                        return isOk;
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return false;
            }
        });
    }

    /**
     * 返回名称为key在hash中fields对应的value
     * 
     * @param key
     *            关键字
     * @param fields
     *            hash中的field
     * @return
     */
    public List<String> hmget(final String key, final String... fields) {
        return call(new ExecuteAction<List<String>>() {
            @Override
            public List<String> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).hmget(key, fields) : c2j(jedisObj).hmget(key, fields);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 返回名称为key的hash中fields对应的value
     * 
     * @param key
     *            关键字
     * @param fields
     *            hash中的field
     * @return
     */
    public Map<String, String> hmgetToMap(final String key, final String... fields) {
        return call(new ExecuteAction<Map<String, String>>() {
            @Override
            public Map<String, String> execute(Object jedisObj) {
                try {
                    Map<String, String> map = new HashMap<String, String>();
                    List<String> byteList = isCluster(jedisObj) ? c2jc(jedisObj).hmget(key, fields) : c2j(jedisObj).hmget(key, fields);
                    int size = byteList.size();
                    for (int i = 0; i < size; i++) {
                        if (ToolsKit.isNotEmpty(byteList.get(i))) {
                            map.put(fields[i], byteList.get(i));
                        }
                    }
                    return map;
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 删除指定hash里的field
     * 
     * @param key
     * @param fields
     * @return
     */
    public long hdel(final String key, final String... fields) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    if (isCluster(jedisObj)) {
                        return c2jc(jedisObj).hdel(key, fields);
                    } else {
                        byte[][] byteFields = new byte[fields.length][];
                        for (int i = 0; i < fields.length; i++) {
                            byteFields[i] = SafeEncoder.encode(fields[i]);
                        }
                        return c2j(jedisObj).hdel(SafeEncoder.encode(key), byteFields);
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 取出指定hash里的所有field
     * 
     * @param key
     * @return
     */
    public Set<String> hkeys(final String key) {
        return call(new ExecuteAction<Set<String>>() {
            @Override
            public Set<String> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).hkeys(key) : c2j(jedisObj).hkeys(key);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 判断hashmap里面是否存在field的key
     * 
     * @param key
     * @param field
     * @return
     */
    public boolean hexists(final String key, final String field) {
        return call(new ExecuteAction<Boolean>() {
            @Override
            public Boolean execute(Object jedisObj) {
                try {
                    if (null != field) {
                        return isCluster(jedisObj) ? c2jc(jedisObj).hexists(key, field) : c2j(jedisObj).hexists(key, field);
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return false;
            }
        });
    }

    /**
     * 返回名称为key的hash中fields对应的value
     * 
     * @param key
     *            关键字
     * @param field
     *            hash中的field
     * @return
     */
    public String hget(final String key, final String field) {
        return call(new ExecuteAction<String>() {
            @Override
            public String execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).hget(key, field) : c2j(jedisObj).hget(key, field);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * key返回哈希表key中，所有的域和值
     * 
     * @param key
     * @return
     */
    public Map<String, String> hgetAll(final String key) {
        return call(new ExecuteAction<Map<String, String>>() {
            @Override
            public Map<String, String> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).hgetAll(key) : c2j(jedisObj).hgetAll(key);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 向有序set里添加元素
     * 
     * @param key
     *            set的key
     * @param value
     *            对应的value
     * @return
     */
    public boolean sadd(final String key, final Object value, final int seconds) {
        return call(new ExecuteAction<Boolean>() {
            @Override
            public Boolean execute(Object jedisObj) {
                try {
                    long isok = 0;
                    if (value instanceof String) {
                        isok = isCluster(jedisObj) ? c2jc(jedisObj).sadd(key, (String) value) : c2j(jedisObj).sadd(key, (String) value);
                    } else {
                        isok = isCluster(jedisObj) ? c2jc(jedisObj).sadd(key, SerializableUtils.serializeString(value))
                                : c2j(jedisObj).sadd(SafeEncoder.encode(key), SerializableUtils.serialize(value));
                    }
                    if (isok > 0) {
                        expire(key, seconds);
                    }
                    return isok == 1 ? true : false;
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return false;
            }
        });
    }

    /**
     * 返回名称为key的set的基数
     * 
     * @param key
     *            set的key
     * @return
     */
    public Long scard(final String key) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).scard(key) : c2j(jedisObj).scard(key);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 测试member是否是名称为key的set的元素
     * 
     * @param key
     *            Set集合的key
     * @param value
     *            值
     * @return
     */
    public boolean sismember(final String key, final Object value) {
        return call(new ExecuteAction<Boolean>() {
            @Override
            public Boolean execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).sismember(key, (String) value) : c2j(jedisObj).sismember(key, (String) value);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return false;
            }
        });
    }

    /**
     * 慎用，会导致redis等待结果返回，若是集群模式则直接返回null
     * 
     * @param pattern
     *            正则表达式
     * @return
     */
    public Set<String> keys(final String pattern) {
        return call(new ExecuteAction<Set<String>>() {
            @Override
            public Set<String> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? null : c2j(jedisObj).keys(pattern);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 根据标识取出redis里的集合size
     * 
     * @param type
     *            标识("list", "hash", "set")
     * @param key
     *            关键字
     * @return
     */
    public long size(final String type, final String key) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    if ("list".equalsIgnoreCase(type)) {
                        return isCluster(jedisObj) ? c2jc(jedisObj).llen(key) : c2j(jedisObj).llen(key);
                    } else if ("hash".equalsIgnoreCase(type)) {
                        return isCluster(jedisObj) ? c2jc(jedisObj).hlen(key) : c2j(jedisObj).hlen(key);
                    } else if ("set".equalsIgnoreCase(type)) {
                        return isCluster(jedisObj) ? c2jc(jedisObj).scard(key) : c2j(jedisObj).scard(key);
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 根据key判断值类型
     * 
     * @param key
     *            关键字
     * @return 类型名称
     */
    public String type(final String key) {
        return call(new ExecuteAction<String>() {
            @Override
            public String execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).type(key) : c2j(jedisObj).type(key);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 判断KEY是否存在
     * 
     * @param key
     *            关键字
     * @return 存在返回true
     */
    public boolean exists(final String key) {
        return call(new ExecuteAction<Boolean>() {
            @Override
            public Boolean execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).exists(key) : c2j(jedisObj).exists(key);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return false;
            }
        });
    }

    /**
     * 根据key设置过期时间
     * 
     * @param key
     * @param seconds
     * @return
     */
    public Long expire(final String key, final Integer seconds) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).expire(key, seconds) : c2j(jedisObj).expire(key, seconds);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 保存ZSet<String>
     * 
     * @param key
     * @param sort
     * @param value
     * @return
     */
    public Boolean zadd(final String key, final double sort, final String value, final int seconds) {
        return call(new ExecuteAction<Boolean>() {
            @Override
            public Boolean execute(Object jedisObj) {
                try {
                    long isok = 0L;
                    if (isCluster(jedisObj)) {
                        isok = c2jc(jedisObj).zadd(key, sort, value);
                    } else {
                        isok = c2j(jedisObj).zadd(key, sort, value);
                    }
                    if (isok > 0) {
                        expire(key, seconds);
                    }
                    return true;
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return false;
            }
        });
    }

    /**
     * 删除ZSet元素
     * 
     * @param key
     * @param value
     * @return
     */
    public Long zrem(final String key, final String value) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).zrem(key, value) : c2j(jedisObj).zrem(key, value);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 由小到大获取member成员在该key的位置
     * 
     * @param key
     * @param member
     * @return
     */
    public Long zrank(final String key, final String member) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).zrank(key, member) : c2j(jedisObj).zrank(key, member);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 由大到小获取member成员在该key的位置
     * 
     * @param key
     * @param member
     * @return
     */
    public Long zrevrank(final String key, final String member) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).zrevrank(key, member) : c2j(jedisObj).zrevrank(key, member);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 升序获取zset元素
     * 
     * @param key
     * @return
     */
    public List<String> zrevrank(final String key) {
        return call(new ExecuteAction<List<String>>() {
            @Override
            public List<String> execute(Object jedisObj) {
                try {
                    if (isCluster(jedisObj)) {
                        return new ArrayList<String>(c2jc(jedisObj).zrange(key, 0, -1));
                    } else {
                        return new ArrayList<String>(((Jedis) jedisObj).zrange(key, 0, -1));
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 升序获取zset元素
     * 
     * @param key
     * @param start
     * @param end
     * @return
     */
    public List<String> zrevrank(final String key, final int start, final int end) {
        return call(new ExecuteAction<List<String>>() {
            @Override
            public List<String> execute(Object jedisObj) {
                try {
                    int e = end;
                    if (e > 0) {
                        e--;
                    }
                    if (isCluster(jedisObj)) {
                        return new ArrayList<String>(c2jc(jedisObj).zrange(key, start, e));
                    } else {
                        return new ArrayList<String>(c2j(jedisObj).zrange(key, start, e));
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 降序获取zset元素
     * 
     * @param key
     * @return
     */
    public List<String> zrevrange(final String key) {
        return call(new ExecuteAction<List<String>>() {
            @Override
            public List<String> execute(Object jedisObj) {
                try {
                    if (isCluster(jedisObj)) {
                        return new ArrayList<String>(c2jc(jedisObj).zrevrange(key, 0, -1));
                    } else {
                        return new ArrayList<String>(c2j(jedisObj).zrevrange(key, 0, -1));
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 降序获取zset元素
     * 
     * @param key
     * @param start
     * @param end
     * @return
     */
    public List<String> zrevrange(final String key, final int start, final int end) {
        return call(new ExecuteAction<List<String>>() {
            @Override
            public List<String> execute(Object jedisObj) {
                try {
                    int e = end;
                    if (e > 0) {
                        e--;
                    }
                    if (isCluster(jedisObj)) {
                        return new ArrayList<String>(c2jc(jedisObj).zrevrange(key, start, e));
                    } else {
                        return new ArrayList<String>(c2j(jedisObj).zrevrange(key, start, e));
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    public List<String> zrange(final String key, final int start, final int end) {
        return call(new ExecuteAction<List<String>>() {
            @Override
            public List<String> execute(Object jedisObj) {
                try {
                    if (isCluster(jedisObj)) {
                        return new ArrayList<String>(c2jc(jedisObj).zrange(key, start, end));
                    } else {
                        return new ArrayList<String>(c2j(jedisObj).zrange(key, start, end));
                    }
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 根据区间段获取集合内排名成员--倒序
     * 
     * @param key
     *            分组key
     * @param start
     *            开始位
     * @param end
     *            结束位 当为-1时，为取所有值
     * @return
     */
    public Set<Tuple> zrevrangeWithScores(final String key, final int start, final int end) {
        return call(new ExecuteAction<Set<Tuple>>() {
            @Override
            public Set<Tuple> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).zrevrangeWithScores(key, start, end) : c2j(jedisObj).zrevrangeWithScores(key, start, end);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 根据key获取list长度
     * 
     * @param key
     * @return
     */
    public Long llen(final String key) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).llen(key) : c2j(jedisObj).llen(key);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 根据key删除并返回list尾元素
     * 
     * @param key
     * @returnrpop
     */
    public String rpop(final String key) {
        return call(new ExecuteAction<String>() {
            @Override
            public String execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).rpop(key) : c2j(jedisObj).rpop(key);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 将名称为key的hash中field的value增加integer
     * 
     * @param key
     * @param field
     * @param integer
     * @return
     */
    public Long hincrby(final String key, final String field, final Integer integer) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).hincrBy(key, field, integer) : c2j(jedisObj).hincrBy(key, field, integer);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 向名称为key的hash中添加元素field<—>value
     * 
     * @param key
     * @param field
     * @param value
     * @return
     */
    public Long hset(final String key, final String field, final Object value, final int seconds) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    long isok = 0L;
                    if (value instanceof String) {
                        if (isCluster(jedisObj)) {
                            isok = c2jc(jedisObj).hset(key, field, (String) value);
                        } else {
                            isok = c2j(jedisObj).hset(key, field, (String) value);
                        }
                    } else {
                        if (isCluster(jedisObj)) {
                            isok = c2jc(jedisObj).hset(key, field, SerializableUtils.serializeString(value));
                        } else {
                            isok = c2j(jedisObj).hset(SafeEncoder.encode(key), SafeEncoder.encode(field), SerializableUtils.serialize(value));
                        }
                    }
                    if (isok > 0) {
                        expire(key, seconds);
                    }
                    return isok;
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 返回名称为key的zset中score>=min且score<=max的所有元素
     *
     * @param key
     * @param min
     * @param max
     * @return
     */
    public Set<String> zrangebyscore(final String key, final double min, final double max) {
        return call(new ExecuteAction<Set<String>>() {
            @Override
            public Set<String> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).zrangeByScore(key, min, max) : c2j(jedisObj).zrangeByScore(key, min, max);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 返回名称为key的zset中score>=min且score<=max结果之间的区间数据 <br/>
     * offset, count就相当于sql中limit的用法 <br/>
     * select * from table where score >=min and score <=max limit offset count
     *
     * @param key
     * @param min
     * @param max
     * @param offset
     * @param count
     * @return
     */
    public Set<String> zrangebyscore(final String key, final double min, final double max, final int offset, final int count) {
        return call(new ExecuteAction<Set<String>>() {
            @Override
            public Set<String> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).zrangeByScore(key, min, max, offset, count)
                            : c2j(jedisObj).zrangeByScore(key, min, max, offset, count);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 返回名称为key的zset中score>=min且score<=max结果之间的区间数据 <br/>
     * offset, count就相当于sql中limit的用法 <br/>
     * select * from table where score >=min and score <=max limit offset count
     *
     * @param key
     * @param min
     * @param max
     * @param offset
     * @param count
     * @return
     */
    public List<TupleDto> zrangeByScoreWithScores(final String key, final double min, final double max, final int offset, final int count) {
        return call(new ExecuteAction<List<TupleDto>>() {
            @Override
            public List<TupleDto> execute(Object jedisObj) {
                try {
                    Set<Tuple> tupleSet = isCluster(jedisObj) ? c2jc(jedisObj).zrangeByScoreWithScores(key, min, max, offset, count)
                            : c2j(jedisObj).zrangeByScoreWithScores(key, min, max, offset, count);
                    List<TupleDto> tupleDtoList = new ArrayList<>();
                    if (ToolsKit.isNotEmpty(tupleSet)) {
                        for (Tuple tuple : tupleSet) {
                            tupleDtoList.add(new TupleDto(tuple.getElement(), BigDecimal.valueOf(tuple.getScore()).doubleValue()));
                        }
                    }
                    return tupleDtoList;
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 删除名称为key的zset中score>=min且score<=max的所有元素
     */
    public Long zremrangebyscore(final String key, final double min, final double max) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).zremrangeByScore(key, min, max) : c2j(jedisObj).zremrangeByScore(key, min, max);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 删除名称为KEY的zeset中rank>=min且rank<=max的所有元素
     * 
     * @param key
     * @param start
     * @param max
     * @return
     */
    public Long zremrangebyrank(final String key, final int start, final int max) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).zremrangeByRank(key, start, max)
                            : c2j(jedisObj).zremrangeByRank(SafeEncoder.encode(key), start, max);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 为某个key自增1
     * 
     * @param key
     * @return
     */
    public Long incr(final String key, final int seconds) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    long count = isCluster(jedisObj) ? c2jc(jedisObj).incr(key) : c2j(jedisObj).incr(SafeEncoder.encode(key));
                    if (count > 0) {
                        expire(key, seconds);
                    }
                    return count;
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 为某个key自减1
     * 
     * @param key
     * @return
     */
    public Long decr(final String key, final int seconds) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    long count = isCluster(jedisObj) ? c2jc(jedisObj).decr(key) : c2j(jedisObj).decr(SafeEncoder.encode(key));
                    if (count > 0) {
                        expire(key, seconds);
                    }
                    return count;
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 获取set的基数
     * 
     * @param key
     * @return
     */
    public Long zcard(final String key) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).zcard(key) : c2j(jedisObj).zcard(SafeEncoder.encode(key));
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 返回key的有效时间
     * 
     * @param key
     * @return
     */
    public Long ttl(final String key) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).ttl(key) : c2j(jedisObj).ttl(SafeEncoder.encode(key));
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 删除set里面和member相同的元素
     * 
     * @param key
     * @param member
     * @return
     */
    public Long srem(final String key, final String member) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).srem(key, member) : c2j(jedisObj).srem(key, member);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 获取set对象
     * 
     * @param key
     * @return
     */
    public Set<String> smembers(final String key) {
        return call(new ExecuteAction<Set<String>>() {
            @Override
            public Set<String> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).smembers(key) : c2j(jedisObj).smembers(key);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 获取zset里面元素的soce
     * 
     * @param key
     * @return
     */
    public Double zscore(final String key, final String member) {
        return call(new ExecuteAction<Double>() {
            @Override
            public Double execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).zscore(key, member) : c2j(jedisObj).zscore(key, member);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0.0;
            }
        });
    }

    /**
     * 返回 key 指定的哈希集中所有字段的值
     * 
     * @param key
     * @return
     */
    public List<String> hvals(final String key) {
        return call(new ExecuteAction<List<String>>() {
            @Override
            public List<String> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).hvals(key) : c2j(jedisObj).hvals(key);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    @SuppressWarnings("unused")
    private <T> T batctGet(final Set<String> keys) {
        return call(new ExecuteAction<T>() {
            @Override
            @SuppressWarnings("unchecked")
            public T execute(Object jedisObj) {
                try {
                    Map<String, String> result = new HashMap<String, String>(keys.size());
                    if (isCluster(jedisObj)) {
                        for (String key : keys) {
                            result.put(key, c2jc(jedisObj).get(key));
                        }
                    } else {
                        Pipeline p = c2j(jedisObj).pipelined();
                        Map<String, Response<Map<String, String>>> responses = new HashMap<String, Response<Map<String, String>>>(keys.size());
                        for (String key : keys) {
                            responses.put(key, p.hgetAll(key));
                        }
                        for (Iterator<String> it = responses.keySet().iterator(); it.hasNext();) {
                            String key = it.next();
                            result.put(key, responses.get(key).get().get(key));
                        }
                    }
                    return (T) result;
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 添加地理位置
     * 
     * @param key
     *            地理位置集合KEY
     * @param longitude
     *            经度
     * @param latitude
     *            纬度
     * @param member
     *            集合成员值
     * @return
     */
    public long geoadd(final String key, final double longitude, final double latitude, final String member, final int seconds) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    long isok = 0L;
                    if (isCluster(jedisObj)) {
                        isok = c2jc(jedisObj).geoadd(key, longitude, latitude, member);
                    } else {
                        isok = c2j(jedisObj).geoadd(key, longitude, latitude, member);
                    }
                    if (isok > 0) {
                        expire(key, seconds);
                    }
                    return isok;
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 添加地理位置
     * 
     * @param key
     *            地理位置集合KEY
     * @param memberCoordinateMap
     *            成员集合值
     * @return
     */
    public long geoadd(final String key, final Map<String, GeoCoordinate> memberCoordinateMap, final int seconds) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    long isok = 0L;
                    if (isCluster(jedisObj)) {
                        isok = c2jc(jedisObj).geoadd(key, memberCoordinateMap);
                    } else {
                        isok = c2j(jedisObj).geoadd(key, memberCoordinateMap);
                    }
                    if (isok > 0) {
                        expire(key, seconds);
                    }
                    return isok;
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }

    /**
     * 根据名称获取地理位置信息
     * 
     * @param key
     *            地理位置集合KEY
     * @param members
     *            成员值
     * @return
     */
    public List<GeoCoordinate> geopos(final String key, final String... members) {
        return call(new ExecuteAction<List<GeoCoordinate>>() {
            @Override
            public List<GeoCoordinate> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).geopos(key, members) : c2j(jedisObj).geopos(key, members);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 计算两个位置之间的距离
     * 
     * @param key
     *            地理位置集合KEY
     * @param member1
     *            成员值
     * @param member2
     *            成员值
     * @parma unit 单位(M/KM)
     * @return
     */
    public Double geodist(final String key, final String member1, final String member2, final GeoUnit unit) {
        return call(new ExecuteAction<Double>() {
            @Override
            public Double execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).geodist(key, member1, member2, unit) : c2j(jedisObj).geodist(key, member1, member2, unit);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0.0;
            }
        });
    }

    /**
     * 获取指定范围内的位置信息
     * 
     * @param key
     *            地理位置集合KEY
     * @param longitude
     *            经度
     * @param latitude
     *            纬度
     * @param radius
     *            半径范围
     * @param unit
     *            单位(M/KM)
     * @return
     */
    public List<GeoRadiusResponse> georadius(final String key, final double longitude, final double latitude, final double radius, final GeoUnit unit) {
        return call(new ExecuteAction<List<GeoRadiusResponse>>() {
            @Override
            public List<GeoRadiusResponse> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).georadius(key, longitude, latitude, radius, unit)
                            : c2j(jedisObj).georadius(key, longitude, latitude, radius, unit);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 获取指定范围内的位置信息
     * 
     * @param key
     *            地理位置集合KEY
     * @param longitude
     *            经度
     * @param latitude
     *            纬度
     * @param radius
     *            半径范围
     * @param unit
     *            单位(M/KM)
     * @param param
     *            查询条件参数
     * @return
     */
    public List<GeoRadiusResponse> georadius(final String key, final double longitude, final double latitude, final double radius, final GeoUnit unit,
            final GeoRadiusParam param) {
        return call(new ExecuteAction<List<GeoRadiusResponse>>() {
            @Override
            public List<GeoRadiusResponse> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).georadius(key, longitude, latitude, radius, unit, param)
                            : c2j(jedisObj).georadius(key, longitude, latitude, radius, unit, param);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 获取存储集合范围内的位置信息
     * 
     * @param key
     *            地理位置集合KEY
     * @param member
     *            成员名称
     * @param radius
     *            半径范围
     * @param unit
     *            单位(M/KM)
     * @return
     */
    public List<GeoRadiusResponse> georadiusByMember(final String key, final String member, final double radius, final GeoUnit unit) {
        return call(new ExecuteAction<List<GeoRadiusResponse>>() {
            @Override
            public List<GeoRadiusResponse> execute(Object jedisObj) {
                try {
                    return isCluster(jedisObj) ? c2jc(jedisObj).georadiusByMember(key, member, radius, unit)
                            : c2j(jedisObj).georadiusByMember(key, member, radius, unit);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return null;
            }
        });
    }

    /**
     * 订阅消息
     * 
     * @param listener
     *            订阅监听器
     * @param channels
     *            订阅渠道
     */
    public void subscribe(final RedisListener listener, final List<String> channels) {
        if (channels.isEmpty()) {
            throw new NullPointerException("channels is null");
        }
        try {
            final String[] channelsArray = channels.toArray(new String[] {});
            ToolsKit.Thread.execAsync(new Runnable() {
                @Override
                public void run() {
                    if (!CacheConfig.isCluster()) {
                        Jedis jedis = JedisPoolUtils.getJedis();
                        jedis.subscribe(listener, channelsArray);
                    } else {
                        JedisCluster jedisCluster = JedisClusterPoolUtils.getJedisCluster();
                        jedisCluster.subscribe(listener, channelsArray);
                    }
                }
            });
        } catch (Exception e1) {
            e1.printStackTrace();
        } finally {
            logger.info("#############: subscribe " + channels + " done!");
        }
    }

    /**
     * 模式匹配方式订阅消息
     * 
     * @param listener
     *            订阅监听器
     * @param channels
     *            订阅渠道
     * @return
     */
    public void psubscribe(final RedisListener listener, final List<String> channels) {
        if (channels.isEmpty()) {
            throw new NullPointerException("channels is null");
        }
        try {
            final String[] channelsArray = channels.toArray(new String[] {});
            ToolsKit.Thread.execAsync(new Runnable() {
                @Override
                public void run() {
                    if (!CacheConfig.isCluster()) {
                        Jedis jedis = JedisPoolUtils.getJedis();
                        jedis.psubscribe(listener, channelsArray);
                    } else {
                        JedisCluster jedisCluster = JedisClusterPoolUtils.getJedisCluster();
                        jedisCluster.psubscribe(listener, channelsArray);
                    }
                }
            });
        } catch (Exception e1) {
            e1.printStackTrace();
        } finally {
            logger.info("#############: psubscribe " + channels + " done!");
        }
    }

    /**
     * 发布消息
     * 
     * @param message
     * @return
     */
    public long publish(final RedisMessage message) {
        return call(new ExecuteAction<Long>() {
            @Override
            public Long execute(Object jedisObj) {
                try {
                    byte[] channel = SafeEncoder.encode(message.getChannel());
                    byte[] bytes = SerializableUtils.serialize(message.getBody());
                    return isCluster(jedisObj) ? c2jc(jedisObj).publish(channel, bytes) : c2j(jedisObj).publish(channel, bytes);
                } catch (JedisConnectionException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new JedisException(e.getMessage());
                }
                return 0L;
            }
        });
    }
}
