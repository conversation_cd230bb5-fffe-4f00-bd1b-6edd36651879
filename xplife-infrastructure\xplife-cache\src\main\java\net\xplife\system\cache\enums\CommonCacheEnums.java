package net.xplife.system.cache.enums;
import java.util.LinkedHashMap;
import net.xplife.system.tools.common.enums.ICacheEnums;
public enum CommonCacheEnums implements ICacheEnums {
    COMMON_FLAG_BY("comm:flg:by:", "公共缓存标识");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (CommonCacheEnums commonCacheEnums : CommonCacheEnums.values()) {
            map.put(commonCacheEnums.getKey(), commonCacheEnums.getDesc());
        }
    }

    private CommonCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
