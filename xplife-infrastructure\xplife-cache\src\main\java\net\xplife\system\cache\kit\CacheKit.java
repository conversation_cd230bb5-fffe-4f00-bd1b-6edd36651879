package net.xplife.system.cache.kit;
import net.xplife.system.cache.core.CacheInterface;
import net.xplife.system.cache.core.RedisClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CacheKit {
    private static Logger         logger = LoggerFactory.getLogger(CacheKit.class);
    private static CacheInterface cacheInterface;

    public static CacheInterface cache() {
        try {
            if (cacheInterface == null) {
                cacheInterface = RedisClient.getInstance();
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        return cacheInterface;
    }
}
