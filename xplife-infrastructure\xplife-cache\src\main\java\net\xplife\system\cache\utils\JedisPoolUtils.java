package net.xplife.system.cache.utils;
import net.xplife.system.cache.common.CacheConfig;
import net.xplife.system.tools.util.core.ToolsKit;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.exceptions.JedisException;
/**
 *
 */
public class JedisPoolUtils {
    private static JedisPool pool     = null;
    private static String    host;
    private static int       port;
    private static int       timeout  = 2000;
    private static String    password;
    private static int       database = 0;

    /**
     * 建立连接池 真实环境，一般把配置参数缺抽取出来。
     */
    private static void createJedisPool() {
        // 建立连接池配置参数
        JedisPoolConfig config = new JedisPoolConfig();
//        config.setMaxIdle(250);
//        config.setMinIdle(100);
//        config.setMaxTotal(500);
//        config.setMaxWaitMillis(5000);
//        config.setTestWhileIdle(false);
//        config.setTestOnBorrow(true);
//        config.setTestOnReturn(false);
//        config.setNumTestsPerEvictionRun(10);
//        config.setMinEvictableIdleTimeMillis(1000);
//        config.setSoftMinEvictableIdleTimeMillis(10);
//        config.setTimeBetweenEvictionRunsMillis(10);
//        config.setLifo(false);
        config.setMaxIdle(8);
        config.setMinIdle(0);
        config.setMaxTotal(16);
        config.setMaxWaitMillis(5000);
        config.setTestWhileIdle(false);
        config.setTestOnBorrow(false);
        config.setTestOnReturn(false);
        // 创建连接池
        try {
            database = CacheConfig.getInstance().getDatabase();
            host = CacheConfig.getInstance().getHost();
            password = CacheConfig.getInstance().getPassword();
            port = CacheConfig.getInstance().getPort();
            if (ToolsKit.isEmpty(password)) {
                pool = new JedisPool(config, host, port, timeout);
            } else {
                pool = new JedisPool(config, host, port, timeout, password, database);
            }
            System.out.println("Connent  " + host + ":" + port + " Redis is Success...");
        } catch (Exception e) {
            e.printStackTrace();
            throw new JedisException(e.getMessage(), e);
        }
    }

    public static boolean isSuccess() {
        return ToolsKit.isNotEmpty(pool);
    }

    /**
     * 在多线程环境同步初始化
     */
    private static synchronized void poolInit() throws JedisException {
        if (pool == null) {
            createJedisPool();
        }
    }

    /**
     * 获取一个jedis 对象
     * 
     * @return
     */
    public static Jedis getJedis() {
        if (pool == null) {
            poolInit();
        }
        return pool.getResource();
    }

    /**
     * 归还一个连接
     * 
     * @param jedis
     */
    public static void returnResource(Jedis jedis) {
        try {
            pool.returnResource(jedis);
        } catch (Exception e) {
            throw new JedisException(e.getMessage());
        }
    }

    /**
     * 归还一个损坏的连接
     * 
     * @param jedis
     */
    public static void returnBrokenResource(Jedis jedis) {
        try {
            pool.returnBrokenResource(jedis);
        } catch (Exception e) {
            throw new JedisException(e.getMessage());
        }
    }

    public static void close() {
        if (pool != null) {
            pool.close();
        }
    }
}