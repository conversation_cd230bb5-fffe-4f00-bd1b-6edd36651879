package net.xplife.system.http.core;
import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;

import net.xplife.system.http.common.Consts;
import net.xplife.system.http.common.ContentType;
import net.xplife.system.http.common.HttpMethod;
import net.xplife.system.http.core.entity.RequestEntity;
import net.xplife.system.http.core.request.GetRequest;
import net.xplife.system.http.core.request.MultipartRequest;
import net.xplife.system.http.core.request.OptionsRequest;
import net.xplife.system.http.core.request.PostRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import net.xplife.system.http.core.response.HttpResponse;
import net.xplife.system.http.exception.HttpClientException;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
/**
 * 基于OkHttp封装请求客户端 Created by laotang on 2017/8/14.
 */
public class HttpClient {
    private static final Logger                            logger            = LoggerFactory.getLogger(HttpClient.class);
    private static Lock                                    _httpClientLock   = new ReentrantLock();
    private static ConcurrentHashMap<String, OkHttpClient> OKHTTP_CLIENT_MAP = new ConcurrentHashMap<String, OkHttpClient>();
    /**
     * 请求实体类，封装了所以请求参数
     */
    private RequestEntity requestEntity;
    /* OkHttp */
    private static OkHttpClient.Builder                    builder;
    // private CookieStore cookieStore = null;
    private OkHttpClient                                   okHttpClient;

    /**
     * 初始化Builder对象，所以Client对象共用一个
     */
    private void initHttpClientBuilder() throws Exception {
        builder = new OkHttpClient.Builder()
                // 设置cookie自动管理
                // .cookieJar(new DefaultCookieJar(cookieStore))
                // 设置默认主机验证规则
                .hostnameVerifier(new HostnameVerifier() {
                    public boolean verify(String hostname, SSLSession session) {
                        return true;
                    }
                })
                // 连接超时
                .connectTimeout(Consts.CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)
                // 读超时
                .readTimeout(Consts.READ_TIMEOUT, TimeUnit.MILLISECONDS)
                // 写超时
                .writeTimeout(Consts.WRITE_TIMEOUT, TimeUnit.MILLISECONDS);
        // 开启Gzip压缩
        // .addInterceptor(new GzipRequestInterceptor());
    }

    /**
     * 构建http请求客户端
     * 
     * @param method
     *            请求模式
     * @param headers
     *            请求头信息
     * @param url
     *            请求URL(如果是GET，可以自带参数)
     * @param params
     *            请求提交的参数
     * @param files
     *            请求提交的文件
     * @param security
     *            请求是否开启验证
     * @param contentType
     *            内容类型，表单方式，JSON方式等
     * @param body
     *            请求体，如json, xml, html等
     */
    public HttpClient(HttpMethod method, Map<String, String> headers, String url, Map<String, Object> params, Map<String, File> files, boolean security,
                      ContentType contentType, byte[] body) {
        // TODO ..是否应该要new一个Map?
        this.requestEntity = new RequestEntity(method, headers, url, params, files, security, contentType, body);
    }

    /**
     * 返回{@linkplain OkHttpClient}对象
     *
     * @return {@link OkHttpClient}
     */
    public OkHttpClient getOkHttpClient(String host) {
        okHttpClient = OKHTTP_CLIENT_MAP.get(host);
        if (okHttpClient == null) {
            try {
                _httpClientLock.tryLock(3000, TimeUnit.MILLISECONDS);
                initHttpClientBuilder(); // 初始化Build对象
                okHttpClient = builder.build();
                OKHTTP_CLIENT_MAP.put(host, okHttpClient);
            } catch (Exception e) {
                logger.warn(e.getMessage(), e);
            } finally {
                _httpClientLock.unlock();
            }
        }
        return okHttpClient;
    }

    /**
     * 发送请求
     * 
     * @return {@link HttpResponse}
     */
    public HttpResponse send() {
        HttpMethod method = requestEntity.getMethod();
        Request request = null;
        // 根据请求方式，分别构建对应的请求对象
        if (HttpMethod.GET.name().equalsIgnoreCase(method.name())) {
            request = new GetRequest(requestEntity).buildRequest();
        }
        if (HttpMethod.POST.name().equalsIgnoreCase(method.name())) {
            if (requestEntity.getContentType().equals(ContentType.MULTIPART)) {
                request = new MultipartRequest(requestEntity).buildRequest();
            } else {
                request = new PostRequest(requestEntity).buildRequest();
            }
        }
        if (HttpMethod.OPTIONS.name().equalsIgnoreCase(method.name())) {
            request = new OptionsRequest(requestEntity).buildRequest();
        }
        // 取得请求返回对象
        try {
            Response response = getOkHttpClient(request.url().host()).newCall(request).execute();
            HttpResponse httpResponse = new HttpResponse();
            httpResponse.setRawResponse(response);
            if (response.code() < 200 || response.code() >= 300) {
                httpResponse.setErrorMessage(response.message());
                httpResponse.setErrorCode(response.code());
                httpResponse.setSuccess(false);
            } else {
                httpResponse.setSuccess(true);
                httpResponse.setErrorMessage(null);
            }
            return httpResponse;
        } catch (IOException e) {
            throw new HttpClientException(e);
        }
    }
}
