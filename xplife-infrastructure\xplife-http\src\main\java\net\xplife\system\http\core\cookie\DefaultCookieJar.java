package net.xplife.system.http.core.cookie;
import java.util.List;

import net.xplife.system.http.utils.HttpUtils;
import net.xplife.system.http.exception.HttpClientException;
import okhttp3.Cookie;
import okhttp3.CookieJar;
import okhttp3.HttpUrl;
/**
 * 默认的Cookie处理,自动管理用户的Cookie. Created by laotang on 2017/8/16.
 */
public class DefaultCookieJar implements CookieJar {
    private ICookieStore cookieStore;

    public DefaultCookieJar(ICookieStore cookieStore) {
        if (HttpUtils.isEmpty(cookieStore)) {
            throw new HttpClientException("CookieStore may not be null.");
        }
        this.cookieStore = cookieStore;
    }

    // @Override
    public void saveFromResponse(HttpUrl url, List<Cookie> cookies) {
        // cookieStore.add(url, cookies);
    }

    // @Override
    public List<Cookie> loadForRequest(HttpUrl url) {
        // return cookieStore.get(url);
        return null;
    }
}
