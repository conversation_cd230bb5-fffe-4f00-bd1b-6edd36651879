package net.xplife.system.http.core.entity;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

import net.xplife.system.http.common.ContentType;
import net.xplife.system.http.common.HttpMethod;
/**
 * Created by laotang on 2017/8/14.
 */
public class RequestEntity {
    private HttpMethod          method;
    private Map<String, String> headers = new HashMap<String, String>();
    private String              url;
    private Map<String, Object> params  = new HashMap<String, Object>();
    private Map<String, File>   files;
    private ContentType contentType;
    private boolean             security;
    private byte[]              body;

    public RequestEntity(HttpMethod method, Map<String, String> headers, String url, Map<String, Object> params, Map<String, File> files, boolean security,
            ContentType contentType, byte[] body) {
        this.method = method;
        this.headers.putAll(headers);
        this.url = url;
        this.params.putAll(params);
        this.files = files;
        this.security = security;
        this.contentType = contentType;
        this.body = body;
    }

    public HttpMethod getMethod() {
        return method;
    }

    public void setMethod(HttpMethod method) {
        this.method = method;
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public Map<String, File> getFiles() {
        return files;
    }

    public void setFiles(Map<String, File> files) {
        this.files = files;
    }

    public boolean getSecurity() {
        return security;
    }

    public void setSecurity(boolean security) {
        this.security = security;
    }

    public ContentType getContentType() {
        return contentType;
    }

    public void setContentType(ContentType contentType) {
        this.contentType = contentType;
    }

    public byte[] getBody() {
        return body;
    }

    public void setBody(byte[] body) {
        this.body = body;
    }
}
