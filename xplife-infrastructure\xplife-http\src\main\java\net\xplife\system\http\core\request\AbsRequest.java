// AbsRequest.java
// Created by laotang on 2017/8/14.
// Copyright 2017年 sythealth. All rights reserved.
package net.xplife.system.http.core.request;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import net.xplife.system.http.common.Consts;
import net.xplife.system.http.common.ContentType;
import net.xplife.system.http.utils.HttpUtils;
import net.xplife.system.http.utils.Signature;
import net.xplife.system.http.common.HttpMethod;
import net.xplife.system.http.core.entity.RequestEntity;
import net.xplife.system.http.exception.HttpClientException;
import okhttp3.FormBody;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okio.BufferedSink;
/**
 * 基于OKHTTP封装请求基类
 *
 * <AUTHOR>
 * @Date 2017/8/14
 * @see IRequest
 * @since 2.0
 */
public abstract class AbsRequest implements IRequest {
    /**
     * 请求类型
     */
    private HttpMethod          method;
    /**
     * 请求的URL
     */
    private String              url;
    /**
     * 请求头信息
     */
    private Map<String, String> headers;
    /**
     * 请求参数
     */
    private Map<String, Object> requestParams;
    /**
     * 是否验证参数
     */
    private boolean             isSecurity;
    /**
     * 请求主体(okhttp)
     */
    private RequestBody         requestBody;
    /**
     * 请求内容类型
     */
    private ContentType contentType;
    /**
     * 请求主体信息
     */
    private byte[]              body;

    AbsRequest(RequestEntity requestEntity) {
        this.method = requestEntity.getMethod();
        this.contentType = requestEntity.getContentType();
        this.url = requestEntity.getUrl();
        this.headers = HttpUtils.isNotEmpty(requestEntity.getHeaders()) ? requestEntity.getHeaders() : new HashMap<String, String>();
        this.requestParams = HttpUtils.isNotEmpty(requestEntity.getParams()) ? requestEntity.getParams() : new HashMap<String, Object>();
        this.body = requestEntity.getBody();
        this.isSecurity = requestEntity.getSecurity();
        buildRequestBody();
        buildRequestSign();
    }

    /**
     * 子类必须实现构建Request方法
     * 
     * @return
     */
    protected abstract Request buildRequest();

    /**
     * 取OKHTTP主体
     * 
     * @return
     */
    public RequestBody getRequestBody() {
        return requestBody;
    }

    /**
     * 构建OKHTTP请求主体
     */
    private void buildRequestBody() {
        if (HttpMethod.POST.name().equals(method.name())) {
            String type = contentType.getValue().toLowerCase();
            if (type.startsWith(ContentType.FORM.getValue().toLowerCase())) {
                buildFormRequestBody();
            } else if (type.startsWith(ContentType.JSON.getValue().toLowerCase())) {
                buildJsonRequestBody();
            } else if (type.startsWith(ContentType.XML.getValue().toLowerCase())) {
                buildXmlRequestBody();
            } else if (type.startsWith(ContentType.MULTIPART.getValue().toLowerCase())) {
                buildMultipartRequestBody();
            }
        }
    }

    /**
     * 如果开启验证, 将值保存到header头里
     */
    private void buildRequestSign() {
        if (isSecurity) {
            if (HttpUtils.isNotEmpty(requestParams)) {
                headers.put(Signature.DUANG_SGIN_RESULT_FIELD, Signature.getSignResult(requestParams));
            } else {
                headers.put(Signature.DUANG_SGIN_RESULT_FIELD, Signature.getSignResult(body));
            }
        }
    }

    /**
     * 构建表单请求体
     */
    private void buildFormRequestBody() {
        FormBody.Builder builder = new FormBody.Builder();
        if (HttpUtils.isNotEmpty(requestParams)) {
            for (Iterator<Map.Entry<String, Object>> iterator = requestParams.entrySet().iterator(); iterator.hasNext();) {
                Map.Entry<String, Object> entry = iterator.next();
                String key = entry.getKey();
                Object value = entry.getValue();
                // System.out.println("############buildFormRequestBody: " + key+ " " + value+" "+value.getClass());
                if (HttpUtils.isArray(value)) {
                    value = value.toString().replace(" ", "");
                }
                builder.add(key, value + "");
            }
        }
        requestBody = builder.build();
    }

    /**
     * 构建JSON请求体
     */
    private void buildJsonRequestBody() {
        requestBody = RequestBody.create(HttpUtils.buildMediaType(contentType, Consts.UTF_8_ENCODING), body);
    }

    /**
     * 构建XML请求体
     */
    private void buildXmlRequestBody() {
        requestBody = RequestBody.create(HttpUtils.buildMediaType(contentType, Consts.UTF_8_ENCODING), body);
    }

    /**
     * 构建文件上传请求的URL
     *
     * @return
     */
    private void buildMultipartRequestBody() {
        // MediaType mediaType = MediaType.parse(contentType.getValue());
        // MultipartBody.Builder builder = new MultipartBody.Builder();
        // //设置类型
        // builder.setType(mediaType); //(MultipartBody.FORM);
        // System.out.println(contentType.getValue()+ " @@@@@@@@@@@@mediaType.toString(): " + mediaType.toString());
        // for(Iterator<Map.Entry<String,String>> it = headers.entrySet().iterator(); it.hasNext();) {
        // Map.Entry<String,String> entry = it.next();
        // System.out.println("header: " + entry.getKey()+" "+entry.getValue());
        // }
        // System.out.println(body.length);
        // builder.setType(mediaType);
        requestBody = new RequestBody() {
            @Override
            public MediaType contentType() {
                return MediaType.parse(contentType.getValue());
            }

            @Override
            public void writeTo(BufferedSink sink) throws IOException {
                sink.write(body);
            }
        };
        // builder.addPart(requestBody);
        // requestBody = builder.build();
    }

    /**
     * 构建GET请求的URL
     *
     * @return 请求URL字符串
     */
    protected String buildUrl() {
        if (HttpUtils.isEmpty(url)) {
            throw new HttpClientException("构建请求URL时，URL不能为空");
        }
        StringBuilder urlString = new StringBuilder(url);
        if (url.contains("?")) {
            urlString.append("&");
        } else {
            urlString.append("?");
        }
        // 如果是GET请求
        if (HttpMethod.GET.name().equalsIgnoreCase(method.name())) {
            // body不为空且是ContentType.STREAM，
            // 一般都是HttpKit.duang().url().param(String).get(), 参数为key=value&key1=value1字符串时会用到
            if (HttpUtils.isNotEmpty(body)) {
                String queryString = new String(body, Charset.forName(Consts.UTF_8_ENCODING));
                if (HttpUtils.isNotEmpty(queryString)) {
                    Map<String, Object> paramsMap = HttpUtils.createLinkString2Map(queryString);
                    requestParams.putAll(paramsMap);
                }
            }
            if (HttpUtils.isNotEmpty(requestParams)) {
                for (Iterator<Map.Entry<String, Object>> it = requestParams.entrySet().iterator(); it.hasNext();) {
                    Map.Entry<String, Object> entry = it.next();
                    if (HttpUtils.isNotEmpty(entry.getValue())) {
                        urlString.append(entry.getKey().trim()).append("=").append(HttpUtils.urlEncode(entry.getValue() + "")).append("&");
                    }
                }
            }
        }
        urlString.deleteCharAt(urlString.length() - 1); // 删除最后一上?或&符号
        return urlString.toString();
    }

    /**
     * 添加用户header头信息，如果没有设置，则填充默认header
     * 
     * @param builder
     * @param httpUrl
     */
    protected void putHeaders2Builder(Request.Builder builder, HttpUrl httpUrl) {
        // 用户设置的header头信息
        if (HttpUtils.isNotEmpty(headers)) {
            Iterator<String> keyIter = headers.keySet().iterator();
            while (keyIter.hasNext()) {
                String key = keyIter.next();
                String value = headers.get(key);
                builder.header(key, value);
            }
        }
        // Map<String, String> defaultHeaders = HttpUtils.getDefaultHeaders(httpUrl.host());
        // // 默认的header头信息, 如果header里有相同的key， 则以默认的为准
        // if(HttpUtils.isNotEmpty(defaultHeaders)) {
        // for (Iterator<Map.Entry<String, String>> iterator = defaultHeaders.entrySet().iterator(); iterator.hasNext(); ) {
        // Map.Entry<String, String> entry = iterator.next();
        // builder.header(entry.getKey(), entry.getValue());
        // }
        // }
    }
}
