package net.xplife.system.http.core.request;
import net.xplife.system.http.core.entity.RequestEntity;
import okhttp3.HttpUrl;
import okhttp3.Request;
/**
 * Created by laotang on 2017/8/14.
 */
public class MultipartRequest extends AbsRequest {
    public MultipartRequest(RequestEntity requestEntity) {
        super(requestEntity);
    }

    @Override
    public Request buildRequest() {
        Request.Builder builder = new Request.Builder();
        HttpUrl httpUrl = HttpUrl.parse(buildUrl());
        builder.url(httpUrl);
        this.putHeaders2Builder(builder, httpUrl); // 设置header
        builder.post(this.getRequestBody());
        return builder.build();
    }
}
