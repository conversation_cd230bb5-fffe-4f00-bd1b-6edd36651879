package net.xplife.system.http.core.response;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import net.xplife.system.http.exception.HttpClientException;
import okhttp3.Headers;
import okhttp3.Response;
/**
 * <p>
 * Http响应结果处理类，通过{@linkplain #isSuccess()}可以得知服务器是否返回200。 并且该类提供了将结果转为其它对象的便捷方法
 * </p>
 * 当响应结果状态码不是200时，而强制调用某个数据转换方法则会跑出异常{@linkplain HttpClientException}， 如果项目需要判断Http Status Code来做一些特殊处理，可以捕获这个异常实现自己的逻辑。
 */
public class HttpResponse implements Serializable {
    private static final long   serialVersionUID = -861108072820490102L;
    private transient Response  rawResponse;
    /* 请求是否成功 */
    private boolean             isSuccess;
    /* 请求失败时错误消息 */
    private String              errorMessage;
    private int                 errorCode;
    private Map<String, String> headerMap        = new HashMap<String, String>();

    /**
     * 判断请求是否成功
     *
     * @return 成功返回true, 否则返回false
     */
    public boolean isSuccess() {
        return isSuccess;
    }

    public void setSuccess(boolean success) {
        isSuccess = success;
    }

    /**
     * 请求失败时错误消息
     *
     * @return 失败消息
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * 请求失败时的HTTP Status Code
     *
     * @return Http错误码
     */
    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public void setRawResponse(Response rawResponse) {
        this.rawResponse = rawResponse;
        Headers headers = rawResponse.headers();
        if (null != headers) {
            for (Iterator<String> iterator = headers.names().iterator(); iterator.hasNext();) {
                String key = iterator.next();
                headerMap.put(key, headers.get(key));
            }
        }
    }

    /**
     * 将响应结果
     * 
     * @return
     */
    public Map<String, String> getHeaderMap() {
        return headerMap;
    }

    /**
     * 将响应结果转为字符串
     *
     * @return 响应结果字符串
     * @throws HttpClientException
     *             如果服务器返回非200则抛出此异常
     */
    public String asString() {
        try {
            return rawResponse.body().string();
        } catch (IOException e) {
            throw new HttpClientException(e);
        }
    }

    /**
     * 将结果转为字节流
     * 
     * @return
     */
    public InputStream asStream() {
        try {
            return rawResponse.body().byteStream();
        } catch (Exception e) {
            throw new HttpClientException(e);
        }
    }

    //
    // /**
    // * 将响应结果转为JavaBean对象
    // *
    // * @param targetClass 目标类型
    // * @param <E> 泛型类型
    // * @return JavaBean对象
    // * @throws HttpClientException 如果服务器返回非200则抛出此异常
    // */
    // public <E> E asBean(Class<E> targetClass) {
    // String responseString = asString();
    // return this.custom(new JsonDataHandler<>(targetClass));
    // }
    //
    // /**
    // * 将响应结果转为JavaBean对象
    // * <p>
    // * 用法如下：Map&lt;String,String&gt; data = httpResponse.asBean(new TypeRef&lt;Map&lt;String,String&gt;&gt;);
    // * </p>
    // *
    // * @param typeRef 带有泛型类的封装类
    // * @param <E> 泛型类型
    // * @return JavaBean对象
    // * @throws HttpClientException 如果服务器返回非200则抛出此异常
    // */
    // public <E> E asBean(TypeRef<E> typeRef) {
    //// return this.custom(new JsonDataHandler<>(typeRef));
    // }
    //
    /**
     * 将响应结果转为字节数组
     *
     * @return 字节数组
     * @throws HttpClientException
     *             如果服务器返回非200则抛出此异常
     */
    public byte[] asBytes() {
        this.assertSuccess();
        try {
            return this.rawResponse.body().bytes();// okhttp3自动关闭流
        } catch (IOException e) {
            throw new HttpClientException(e);
        } finally {
            if (null != this.rawResponse) {
                this.rawResponse.close();
            }
        }
    }
    //
    // /**
    // * 将响应结果输出到文件中
    // *
    // * @param saveFile 目标保存文件,非空
    // */
    // public void asFile(File saveFile) {
    //// Assert.notNull(saveFile, "SaveFile may noy be null.");
    //// this.custom(new FileDataHandler(saveFile.getParent(), saveFile.getName()));
    // }
    //
    // /**
    // * 将响应结果输出到输出流,并不会主动关闭输出流{@code out}
    // *
    // * @param out 输出流,非空
    // */
    // public void asStream(OutputStream out) {
    // Assert.notNull(out, "OutputStream is null.");
    // this.assertSuccess();
    // try {
    // IOUtils.copy(this.rawResponse.body().byteStream(), out);
    // } finally {
    // IOUtils.closeQuietly(this.rawResponse);
    // }
    // }
    //
    // /**
    // * 响应结果转换
    // *
    // * @param dataHandler 数据转换接口
    // * @param <T> 期望转换的类型
    // * @return 抓好结果
    // */
    // public <T> T custom(DataHandler<T> dataHandler) {
    // this.assertSuccess();
    // try {
    // return dataHandler.handle(this.rawResponse);
    // } catch (IOException e) {
    // throw new HttpClientException(e);
    // } finally {
    // IOUtils.closeQuietly(this.rawResponse);
    // }
    // }

    private void assertSuccess() {
        if (!this.isSuccess) {
            String errorMsg = System.currentTimeMillis() + " rquest url[" + rawResponse.request().url().toString() + "] is fail, response code is:  ["
                    + this.errorCode + "], errormsg is: " + this.errorMessage;
            throw new HttpClientException(errorMsg);
        }
    }
}
