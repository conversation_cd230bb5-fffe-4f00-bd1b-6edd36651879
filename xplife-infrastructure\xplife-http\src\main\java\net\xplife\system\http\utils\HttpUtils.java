package net.xplife.system.http.utils;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Array;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import net.xplife.system.http.common.Consts;
import net.xplife.system.http.common.ContentType;
import net.xplife.system.http.common.HttpHeaders;
import okhttp3.MediaType;
/**
 * Created by laotang on 2017/8/14.
 */
public class HttpUtils {
    /***
     * 判断传入的对象是否为空
     *
     * @param obj
     *            待检查的对象
     * @return 返回的布尔值,为空或等于0时返回true
     */
    public static boolean isEmpty(Object obj) {
        return checkObjectIsEmpty(obj, true);
    }

    /***
     * 判断传入的对象是否不为空
     *
     * @param obj
     *            待检查的对象
     * @return 返回的布尔值,不为空或不等于0时返回true
     */
    public static boolean isNotEmpty(Object obj) {
        return checkObjectIsEmpty(obj, false);
    }

    @SuppressWarnings("rawtypes")
    private static boolean checkObjectIsEmpty(Object obj, boolean bool) {
        if (null == obj)
            return bool;
        else if (obj == "" || "".equals(obj))
            return bool;
        else if (obj instanceof Integer || obj instanceof Long || obj instanceof Double) {
            try {
                Double.parseDouble(obj + "");
            } catch (Exception e) {
                return bool;
            }
        } else if (obj instanceof String) {
            if (((String) obj).length() <= 0) return bool;
            if ("null".equalsIgnoreCase(obj + "")) return bool;
        } else if (obj instanceof Map) {
            if (((Map) obj).size() == 0) return bool;
        } else if (obj instanceof Collection) {
            if (((Collection) obj).size() == 0) return bool;
        } else if (obj instanceof Object[]) {
            if (((Object[]) obj).length == 0) return bool;
        }
        return !bool;
    }

    /**
     * 对URL中的请求参数UTF-8编码
     *
     * @param value
     *            参数值
     * @return 编码的值
     */
    public static String urlEncode(String value) {
        try {
            return isNotEmpty(value) ? URLEncoder.encode(value, Consts.UTF_8_ENCODING).replace("+", "%20").replace("*", "%2A").replace("%7E", "~") : "";
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return value;
        }
    }

    /**
     * 根据host取出默认的Header头信息
     * 
     * @param host
     * @return
     */
    public static Map<String, String> getDefaultHeaders(String host) {
        Map<String, String> defaultHeaderMap = new HashMap<String, String>();
        defaultHeaderMap.put(HttpHeaders.USER_AGENT, buildUserAgent());
        return defaultHeaderMap;
    }

    /**
     * 构建请求Header头里UserAgeng默认值
     * 
     * @return
     */
    private static String buildUserAgent() {
        StringBuilder userAgentStr = new StringBuilder();
        userAgentStr.append("sadais-gateway/1.0.0(").append(System.getProperty("os.name")).append("/").append(System.getProperty("os.version")).append("/")
                .append(System.getProperty("os.arch")).append("/").append(System.getProperty("java.version")).append(")");
        return userAgentStr.toString();
    }

    /**
     * 构建okhttp MediaType
     * 
     * @param contentType
     *            枚举
     * @return
     */
    public static MediaType buildMediaType(ContentType contentType, String charset) {
        return MediaType.parse(String.format("%s; charset=%s", contentType.getValue(), Charset.forName(charset)));
    }

    public static ContentType getContentType(String body) {
        if (isMapJsonString(body) || isArrayJsonString(body)) return ContentType.JSON;
        if (isXmlString(body)) return ContentType.XML;
        return null;
    }

    private static boolean isMapJsonString(String jsonString) {
        if (isEmpty(jsonString)) return false;
        return jsonString.startsWith("{") && jsonString.endsWith("}");
    }

    private static boolean isArrayJsonString(String jsonString) {
        if (isEmpty(jsonString)) return false;
        return jsonString.startsWith("[") && jsonString.endsWith("]");
    }

    private static boolean isXmlString(String xmlString) {
        if (isEmpty(xmlString)) return false;
        return (xmlString.startsWith("<") && xmlString.endsWith(">")) ? true : false;
    }

    /**
     * 字符串转换为Stream流
     * 
     * @param str
     *            字符串
     * @param encoding
     *            编码格式
     * @return
     */
    public static InputStream string2InputStream(String str, String encoding) {
        try {
            return new ByteArrayInputStream(str.getBytes(encoding));
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    /**
     * @param params
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String paramToQueryString(Map<String, Object> params) throws UnsupportedEncodingException {
        if (isEmpty(params)) {
            return null;
        }
        List<String> list = new ArrayList<String>();
        Iterator<String> it = params.keySet().iterator();
        while (it.hasNext()) {
            String key = it.next();
            Object value = params.get(key);
            if (isNotEmpty(value)) {
                if (value instanceof java.io.File) {
                    continue;
                }
                list.add(key + "=" + urlEncode(value + "") + "&");
            }
        }
        int size = list.size();
        String[] arrayToSort = list.toArray(new String[size]);
        Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        for (String value : arrayToSort) {
            sb.append(value);
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    public static InputStream byte2InputStream(byte[] body) {
        return new ByteArrayInputStream(body);
    }

    public static Map<String, Object> createLinkString2Map(String queryString) {
        Map<String, Object> map = new HashMap<String, Object>();
        queryString = queryString.startsWith("?") ? queryString.substring(1, queryString.length()) : queryString;
        String[] qsArray = queryString.split("&");
        try {
            for (String qsString : qsArray) {
                String[] qsItems = qsString.split("=");
                if (qsItems.length == 2) {
                    if (isNotEmpty(qsItems[1])) {
                        map.put(qsItems[0], URLEncoder.encode(qsItems[1], Consts.UTF_8_ENCODING).replace("+", "%20").replace("*", "%2A").replace("\"", "22%")
                                .replace("%2520", " ").replace("%7E", "~"));
                    }
                }
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return map;
    }

    /**
     * @param value
     * @return
     */
    public static boolean isArray(Object value) {
        return value instanceof List || value instanceof Array || value.getClass().isArray();
    }
}
