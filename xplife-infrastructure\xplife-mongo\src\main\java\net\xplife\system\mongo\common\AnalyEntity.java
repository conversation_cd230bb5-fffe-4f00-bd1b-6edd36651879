package net.xplife.system.mongo.common;
import org.springframework.data.mongodb.core.index.Indexed;
/**
 * 统计分析表基础类
 */
public abstract class AnalyEntity extends IdEntity {
    public static final String VERSION_FIELD       = "version";
    public static final String CHANNEL_FIELD       = "channel";
    public static final String DEVICE_SYSTEM_FIELD = "deviceSystem";
    public static final String YEAR_FIELD          = "year";
    public static final String SEASON_FIELD        = "season";
    public static final String MONTH_FIELD         = "month";
    public static final String WEEK_FIELD          = "week";
    public static final String DAY_FIELD           = "day";
    public static final String HOUR_FIELD          = "hour";
    public static final String PARTITION_FIELD     = "partition";
    private static final long  serialVersionUID    = 1L;
    protected String           requestLogId;                        // 请求日志ID
    protected String           version;                             // 版本
    protected String           channel;                             // 渠道
    protected String           deviceSystem;                        // 系统版本
    protected int              year;                                // 年
    @Indexed(name = "_season_")
    protected int              season;                              // 季
    @Indexed(name = "_month_")
    protected int              month;                               // 月
    @Indexed(name = "_week_")
    protected int              week;                                // 周
    protected int              day;                                 // 日
    protected int              hour;                                // 小时
    @Indexed(name = "_partition_")
    protected int              partition;                           // 分区

    public String getRequestLogId() {
        return requestLogId;
    }

    public void setRequestLogId(String requestLogId) {
        this.requestLogId = requestLogId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getDeviceSystem() {
        return deviceSystem;
    }

    public void setDeviceSystem(String deviceSystem) {
        this.deviceSystem = deviceSystem;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public int getHour() {
        return hour;
    }

    public void setHour(int hour) {
        this.hour = hour;
    }

    public int getPartition() {
        return partition;
    }

    public void setPartition(int partition) {
        this.partition = partition;
    }

    public int getSeason() {
        return season;
    }

    public void setSeason(int season) {
        this.season = season;
    }

    public int getWeek() {
        return week;
    }

    public void setWeek(int week) {
        this.week = week;
    }
}
