package net.xplife.system.mongo.common;
import java.util.Date;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
/**
 * 统一定义id的entity基类. 基类统一定义id的属性名称、数据类型、列名映射及生成策略. 子类可重载getId()函数重定义id的列名映射和生成策略.
 */
public class IdEntity implements java.io.Serializable {
    public static final String ID_FIELD           = "_id";
    public static final String CREATETIME_FIELD   = "createtime";
    public static final String CREATEUSERID_FIELD = "createuserid";
    public static final String UPDATETIME_FIELD   = "updatetime";
    public static final String UPDATEUSERID_FIELD = "updateuserid";
    public static final String STATUS_FIELD       = "status";
    public static final String SOURCE_FIELD       = "source";
    public static final String APP_ID_FIELD       = "appId";
    private static final long  serialVersionUID   = 1L;
    @Id
    protected String           id;                                 // 表主键
    @Indexed(name = "_createtime_")
    protected Date             createtime;                         // 创建时间
    protected String           createuserid;                       // 创建人ID
    protected Date             updatetime;                         // 更新时间
    protected String           updateuserid;                       // 更新人ID
    protected String           status;                             // 数据状态(查数据字典)
    protected String           source;                             // 数据来源
    @Indexed(name = "_appid_")
    protected String           appId;                              // 应用渠道ID

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getCreateuserid() {
        return createuserid;
    }

    public void setCreateuserid(String createuserid) {
        this.createuserid = createuserid;
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    public String getUpdateuserid() {
        return updateuserid;
    }

    public void setUpdateuserid(String updateuserid) {
        this.updateuserid = updateuserid;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((createtime == null) ? 0 : createtime.hashCode());
        result = prime * result + ((createuserid == null) ? 0 : createuserid.hashCode());
        result = prime * result + ((id == null) ? 0 : id.hashCode());
        result = prime * result + ((source == null) ? 0 : source.hashCode());
        result = prime * result + ((status == null) ? 0 : status.hashCode());
        result = prime * result + ((updatetime == null) ? 0 : updatetime.hashCode());
        result = prime * result + ((updateuserid == null) ? 0 : updateuserid.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;
        IdEntity other = (IdEntity) obj;
        if (createtime == null) {
            if (other.createtime != null) return false;
        } else if (!createtime.equals(other.createtime)) return false;
        if (createuserid == null) {
            if (other.createuserid != null) return false;
        } else if (!createuserid.equals(other.createuserid)) return false;
        if (id == null) {
            if (other.id != null) return false;
        } else if (!id.equals(other.id)) return false;
        if (source == null) {
            if (other.source != null) return false;
        } else if (!source.equals(other.source)) return false;
        if (status == null) {
            if (other.status != null) return false;
        } else if (!status.equals(other.status)) return false;
        if (updatetime == null) {
            if (other.updatetime != null) return false;
        } else if (!updatetime.equals(other.updatetime)) return false;
        if (updateuserid == null) {
            if (other.updateuserid != null) return false;
        } else if (!updateuserid.equals(other.updateuserid)) return false;
        return true;
    }

    @Override
    public String toString() {
        return "IdEntity [id=" + id + ", createtime=" + createtime + ", createuserid=" + createuserid + ", updatetime=" + updatetime + ", updateuserid="
                + updateuserid + ", status=" + status + ", source=" + source + "]";
    }
}
