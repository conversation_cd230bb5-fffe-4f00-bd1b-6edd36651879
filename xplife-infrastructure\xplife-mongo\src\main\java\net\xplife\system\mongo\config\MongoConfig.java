package net.xplife.system.mongo.config;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.MongoDbFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoDbFactory;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.MongoCredential;
import com.mongodb.ReadPreference;
import com.mongodb.ServerAddress;

@Configuration
@ConditionalOnExpression("${mongo.enable:false}") // 配置开关是否重载该类覆盖默认的MongoDbFactory
public class MongoConfig {

    @Value("${mongoReplicaSet}")
    private String mongoReplicaSet;
    @Value("${mongoUserName}")
    private String mongoUserName;
    @Value("${mongoPassword}")
    private String mongoPassword;
    @Value("${mongoDatabase}")
    private String mongoDatabase;

    /**
     * 覆盖默认的MongoDbFactory
     * @return
     */
    @Bean
    MongoDbFactory mongoDbFactory() {
        MongoClientOptions mongoClientOptions = MongoClientOptions.builder().connectionsPerHost(500).minConnectionsPerHost(10).heartbeatFrequency(10000)
                .minHeartbeatFrequency(500).heartbeatConnectTimeout(15000).heartbeatSocketTimeout(20000).localThreshold(15)
                .readPreference(ReadPreference.secondaryPreferred()).connectTimeout(15000).maxWaitTime(120000).socketTimeout(0)
                .threadsAllowedToBlockForConnectionMultiplier(50).build();
        List<ServerAddress> serverAddresses = new ArrayList<ServerAddress>();
        String[] replicaSet = mongoReplicaSet.split(",");
        for (String address : replicaSet) {
            String host = address.split(":")[0];
            int port = Integer.parseInt(address.split(":")[1]);
            ServerAddress serverAddress = new ServerAddress(host, port);
            serverAddresses.add(serverAddress);
        }
        System.out.println("serverAddresses：" + serverAddresses.toString());
        // 创建客户端和Factory
        MongoClient mongoClient = null;
        boolean hasUserName = mongoUserName != null && !("").equals(mongoUserName);
        boolean hasPassword = mongoPassword != null && !("").equals(mongoPassword);
        if (hasUserName && hasPassword) {
            MongoCredential credential = MongoCredential.createScramSha1Credential(mongoUserName, mongoDatabase, mongoPassword.toCharArray());
            if (credential == null) {
                throw new RuntimeException("Can not connect to mongoDB. Failed to authenticate!");
            }
            List<MongoCredential> mongoCredentialList = Arrays.asList(credential);
            mongoClient = new MongoClient(serverAddresses, mongoCredentialList, mongoClientOptions);
        } else {
            mongoClient = new MongoClient(serverAddresses, mongoClientOptions);
        }
        return new SimpleMongoDbFactory(mongoClient, mongoDatabase);
    }

    /**
     * 覆盖默认的MongoTemplate
     * @return
     */
    @Bean
    MongoTemplate mongoTemplate() {
        return new MongoTemplate(mongoDbFactory());
    }

}
