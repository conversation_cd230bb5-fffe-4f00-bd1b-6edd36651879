package net.xplife.system.mongo.core;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import net.xplife.system.mongo.common.AnalyEntity;
import net.xplife.system.mongo.common.IdEntity;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.mongo.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;
import com.mongodb.client.result.DeleteResult;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.util.core.ToolsKit;

/**
 * 继承MongoTemplate 扩展类
 * 
 * <AUTHOR>
 * @param <T>
 */
@Repository
@Slf4j
public class MongoDao<T extends Serializable> {

    @Autowired
    private MongoTemplate mongoTemplate;
    protected Class<T> entityClass;

    public MongoDao() {
        initEntityClass();
    }

    private void initEntityClass() {
        this.entityClass = this.getSuperClassGenricType(getClass(), 0);
        this.getCollectionName();
    }

    /**
     * 通过反射, 获得Class定义中声明的父类的泛型参数的类型. 如无法找到, 返回Object.class. 如public UserDao extends MongodbBaseDao<User,String>
     *
     * @param clazz
     *            clazz The class to introspect
     * @param index
     *            the Index of the generic ddeclaration,start from 0.
     * @return the index generic declaration, or Object.class if cannot be determined
     */
    private Class getSuperClassGenricType(final Class clazz, final int index) {
        Type genType = clazz.getGenericSuperclass();
        if (!(genType instanceof ParameterizedType)) {
            return Object.class;
        }
        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();
        if (index >= params.length || index < 0) {
            return Object.class;
        }
        if (!(params[index] instanceof Class)) {
            return Object.class;
        }
        return (Class) params[index];
    }

    /**
     * 获取类映射数据库表名
     */
    private String getCollectionName() {
        String collection = null;
        if (entityClass.isAnnotationPresent(org.springframework.data.mongodb.core.mapping.Document.class)) {
            org.springframework.data.mongodb.core.mapping.Document document = (org.springframework.data.mongodb.core.mapping.Document) entityClass
                    .getAnnotation(org.springframework.data.mongodb.core.mapping.Document.class);
            collection = (StringUtils.hasText(document.collection()) ? document.collection() : entityClass.getSimpleName());
        } else {
            collection = entityClass.getSimpleName();
        }
        return collection;
    }

    // private Query appendQueryIDField(Query query, String collectionName) {
    // return appendQueryIDField(query, collectionName, -1);
    // }

    /**
     * 为了解决分片后出现does not contain _id or shard key for pattern这个异常 如果原有的查询条件不包含有分片键(_id)的，则先查询一次,将作为分片键的值查出来追加到原有的查询条件中
     *
     * @param query
     *            原有的查询条件
     * @param collectionName
     *            查询的集合名称
     * @param limit
     *            查询的记录数
     * @return
     */
    // private Query appendQueryIDField(Query query, String collectionName, int limit) {
    // if (null == query) return query;
    // Document queryObj = query.getQueryObject();
    // if (queryObj.toString().length() <= 3) return query;
    // if (!queryObj.containsKey(IdEntity.ID_FIELD)) {
    // DBObject fieldObj = new BasicDBObject();
    // fieldObj.put(IdEntity.ID_FIELD, true);
    // DBCursor cursor = mongoTemplate.getCollection(collectionName).find(query.getQueryObject(), fieldObj);
    // if (limit > -1) {
    // cursor.limit(limit);
    // }
    // List<ObjectId> objIds = new ArrayList<ObjectId>();
    // if (null != cursor) {
    // for (Iterator<DBObject> it = cursor.iterator(); it.hasNext();) {
    // DBObject dbo = it.next();
    // ObjectId oid = new ObjectId(dbo.get(IdEntity.ID_FIELD).toString());
    // if (!objIds.contains(oid)) {
    // objIds.add(oid);
    // }
    // }
    // }
    // if (null != query.getFieldsObject() && !query.getFieldsObject().containsKey(IdEntity.ID_FIELD)) {
    // query.fields().include(IdEntity.ID_FIELD);
    // }
    // if (objIds.size() == 1) {
    // query.addCriteria(Criteria.where(IdEntity.ID_FIELD).is(objIds.get(0)));
    // } else if (objIds.size() > 1) {
    // query.addCriteria(Criteria.where(IdEntity.ID_FIELD).in(objIds));
    // }
    // }
    // this.queryPrint(query);
    // return query;
    // }
    /**
     * 打印查询语句
     *
     * @param query
     */
    private void queryPrint(Query query) {
        log.debug("collectionName: " + getCollectionName());
        log.debug("querys: " + query.getQueryObject());
        log.debug("fields: " + query.getFieldsObject());
        log.debug("sorts: " + query.getSortObject());
    }

    /**
     * 保存新增或修改的对象.
     */
    public void saveEntity(T entity) {
        mongoTemplate.save(entity, getCollectionName());
    }

    /**
     * 新增记录
     *
     * @param entity
     */
    public void insertEntity(T entity) throws RuntimeException {
        if (ToolsKit.isEmpty(entity)) {
            throw new RuntimeException("entity is null");
        }
        IdEntity idEntity = (IdEntity) entity;
        if (ToolsKit.isEmpty(idEntity.getId())) {
            throw new RuntimeException("entity id is null");
        }
        mongoTemplate.insert(entity, getCollectionName());
    }

    /**
     * 单条更新
     *
     * @param query
     *            查询语句
     * @param update
     *            更新值
     */
    public void updateFirst(Query query, Update update) {
        this.queryPrint(query);
        mongoTemplate.updateFirst(query, update, entityClass, getCollectionName());
    }

    /**
     * 批量更新
     *
     * @param query
     *            查询语句
     * @param update
     *            更新值
     */
    public void updateMulti(Query query, Update update) {
        this.queryPrint(query);
        mongoTemplate.updateMulti(query, update, entityClass, getCollectionName());
    }

    /**
     * 根据条件查询对象
     *
     * @param query
     *            查询语句
     * @return
     */
    public T findOne(Query query) {
        this.queryPrint(query);
        return (T) mongoTemplate.findOne(query, entityClass, getCollectionName());
    }

    /**
     * 根据条件查询对象集合
     *
     * @param query
     *            查询语句
     * @return
     */
    public List<T> findList(Query query) {
        this.queryPrint(query);
        return (List<T>) mongoTemplate.find(query, entityClass, getCollectionName());
    }

    /**
     * 根据条件查询对象集合
     *
     * @param query
     *            查询语句
     * @return
     */
    public List<T> findList(String... status) {
        Query query = new Query();
        if (ToolsKit.isNotEmpty(status)) {
            query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(status[0]));
        } else {
            query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        }
        this.queryPrint(query);
        return (List<T>) this.findList(query);
    }

    /**
     * 根据条件查询对象集合
     *
     * @param query
     *            查询语句
     * @return
     */
    public List<T> findList(String orderField) {
        Query query = new Query();
        query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        query.with(new Sort(Sort.Direction.DESC, orderField));
        this.queryPrint(query);
        return (List<T>) this.findList(query);
    }

    /**
     * 根据条件查询数量
     *
     * @param query
     *            查询语句
     * @return
     */
    public long count(Query query) {
        this.queryPrint(query);
        return mongoTemplate.count(query, entityClass, getCollectionName());
    }

    /**
     * 根据条件查询对象集合
     *
     * @param query
     *            查询语句
     * @return
     */
    public Page<T> findPage(Query query) {
        this.queryPrint(query);
        Page<T> page = new Page<T>();
        List<T> result = (List<T>) mongoTemplate.find(query, entityClass, getCollectionName());
        page.setResult(result);
        if (query.getSkip() == 0) {
            page.setPageNo(1);
        } else {
            page.setPageNo((int) (query.getSkip() / query.getLimit()) + 1);
        }
        page.setPageSize(query.getLimit());
        page.setTotalCount(this.count(query));
        return page;
    }

    /**
     * 根据ID查询对象.
     *
     * @param id
     *            对象实体ID
     * @return 对象实体
     */
    public T getById(String id, String status) {
        Query query = new Query();
        query.addCriteria(Criteria.where(IdEntity.ID_FIELD).is(id));
        if (ToolsKit.isNotEmpty(status)) {
            query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(status));
        }
        return (T) this.findOne(query);
    }

    /**
     * 根据ID删除对象
     *
     * @param id
     *            对象实体ID
     */
    public void delById(String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where(IdEntity.ID_FIELD).is(id));
        query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        Update update = new Update();
        update.set(IdEntity.STATUS_FIELD, ToolsConst.DATA_DELETE_STATUS);
        this.updateFirst(query, update);
    }

    /**
     * 根据多个ID删除记录
     * 
     * @param ids
     */
    public void delByIds(List<String> ids) {
        Query query = new Query();
        query.addCriteria(Criteria.where(IdEntity.ID_FIELD).in(ids));
        query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        Update update = new Update();
        update.set(IdEntity.STATUS_FIELD, ToolsConst.DATA_DELETE_STATUS);
        this.updateMulti(query, update);
    }

    /**
     * 根据条件删除记录--物理删除无法恢复
     * 
     * @param query
     */
    public DeleteResult remove(Query query) {
        return mongoTemplate.remove(query, getCollectionName());
    }

    /**
     * 根据ID删除记录--物理删除无法恢复
     * 
     * @param id
     *            记录ID
     * @return
     */
    public DeleteResult removeById(String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where(IdEntity.ID_FIELD).is(id));
        return mongoTemplate.remove(query, getCollectionName());
    }

    /**
     * 根据条件删除记录--物理删除无法恢复
     * 
     * @param propertyName
     *            属性名
     * @param value
     *            属性值
     * @return
     */
    public DeleteResult removeUniqueBy(String propertyName, Object value) {
        Query query = new Query();
        query.addCriteria(Criteria.where(propertyName).is(value));
        return mongoTemplate.remove(query, getCollectionName());
    }

    /**
     * 根据属性查询对象
     *
     * @param propertyName
     *            属性字段
     * @param value
     *            属性值
     * @return
     */
    public T findUniqueBy(String propertyName, Object value) {
        Query query = new Query();
        query.addCriteria(Criteria.where(propertyName).is(value));
        query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        return (T) this.findOne(query);
    }

    /**
     * 根据属性查询对象
     *
     * @param propertyName
     *            属性字段
     * @param value
     *            属性值
     * @return
     */
    public List<T> findUniqueByList(String propertyName, Object value) {
        Query query = new Query();
        if (ToolsKit.isNotEmpty(value)) {
            query.addCriteria(Criteria.where(propertyName).is(value));
        }
        query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        query.with(new Sort(Sort.Direction.DESC, IdEntity.ID_FIELD));
        return (List<T>) this.findList(query);
    }

    /**
     * 统计汇总查询信息列表
     *
     * @param day--不需要填-1
     *            天
     * @param hour--不需要填-1
     *            小时
     * @param partition
     *            分区字段
     * @return
     */
    public List<T> findList(int day, int hour, int partition) {
        return this.findListByQuery(null, null, day, hour, partition);
    }

    /**
     * 统计汇总查询信息列表
     *
     * @param propertyName
     *            参数名
     * @param value
     *            参数值
     * @param day--不需要填-1
     *            天
     * @param hour--不需要填-1
     *            小时
     * @param partition
     *            分区字段
     * @return
     */
    public List<T> findListByIn(String propertyName, List<Object> value, int day, int hour, int partition) {
        Query query = new Query();
        if (day > 0) {
            query.addCriteria(Criteria.where(AnalyEntity.DAY_FIELD).is(day));
        }
        if (hour >= 0) {
            query.addCriteria(Criteria.where(AnalyEntity.HOUR_FIELD).is(hour));
        }
        if (ToolsKit.isNotEmpty(propertyName) && ToolsKit.isNotEmpty(value)) {
            query.addCriteria(Criteria.where(propertyName).in(value));
        }
        query.addCriteria(Criteria.where(AnalyEntity.PARTITION_FIELD).is(partition));
        query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        return this.findList(query);
    }

    /**
     * 统计汇总查询信息列表
     *
     * @param propertyName
     *            参数名
     * @param value
     *            参数值
     * @param day--不需要填-1
     *            天
     * @param hour--不需要填-1
     *            小时
     * @param partition
     *            分区字段
     * @return
     */
    public List<T> findListByQuery(List<String> propertyName, List<Object> value, int day, int hour, int partition) {
        Query query = new Query();
        if (day > 0) {
            query.addCriteria(Criteria.where(AnalyEntity.DAY_FIELD).is(day));
        }
        if (hour >= 0) {
            query.addCriteria(Criteria.where(AnalyEntity.HOUR_FIELD).is(hour));
        }
        if (ToolsKit.isNotEmpty(propertyName) && ToolsKit.isNotEmpty(value) && propertyName.size() == value.size()) {
            for (int i = 0; i < propertyName.size(); i++) {
                query.addCriteria(Criteria.where(propertyName.get(i)).is(value.get(i)));
            }
        }
        query.addCriteria(Criteria.where(AnalyEntity.PARTITION_FIELD).is(partition));
        query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        return this.findList(query);
    }

    /**
     * 统计汇总查询数量
     *
     * @param day--不需要填-1
     *            天
     * @param hour--不需要填-1
     *            小时
     * @param partition
     *            分区字段
     * @return
     */
    public long count(int day, int hour, int partition) {
        Query query = new Query();
        if (day > 0) {
            query.addCriteria(Criteria.where(AnalyEntity.DAY_FIELD).is(day));
        }
        if (hour >= 0) {
            query.addCriteria(Criteria.where(AnalyEntity.HOUR_FIELD).is(hour));
        }
        query.addCriteria(Criteria.where(AnalyEntity.PARTITION_FIELD).is(partition));
        query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        return this.count(query);
    }

    /**
     * 统计汇总查询信息列表
     *
     * @param startDate
     *            开始分区字段
     * @param endDate
     *            结束分区字段
     * @return
     */
    public List<T> findListByQuery(String propertyName, int startDate, int endDate) {
        Query query = new Query();
        query.addCriteria(new Criteria().andOperator(Criteria.where(propertyName).gte(startDate), Criteria.where(propertyName).lte(endDate)));
        query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        return this.findList(query);
    }

    /**
     * 统计汇总查询信息列表
     *
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @return
     */
    public List<T> findListByQuery(String propertyName, Date startDate, Date endDate) {
        Query query = new Query();
        query.addCriteria(new Criteria().andOperator(Criteria.where(propertyName).gte(startDate), Criteria.where(propertyName).lte(endDate)));
        query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        return this.findList(query);
    }

    /**
     * 统计汇总查询信息列表
     *
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @return
     */
    public List<T> findListByQuery(String appId, String propertyName, Date startDate, Date endDate) {
        Query query = new Query();
        if (ToolsKit.isNotEmpty(appId)) {
            query.addCriteria(Criteria.where(IdEntity.APP_ID_FIELD).is(appId));
        }
        query.addCriteria(new Criteria().andOperator(Criteria.where(propertyName).gte(startDate), Criteria.where(propertyName).lte(endDate)));
        query.addCriteria(Criteria.where(IdEntity.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        return this.findList(query);
    }

    /**
     * 统计属性值
     * 
     * @param criteria
     * @param groupField
     * @param sumField
     * @return
     */
    public List<Result> sum(Criteria criteria, String groupField, String sumField) {
        MatchOperation match = Aggregation.match(criteria);
        GroupOperation group = Aggregation.group(groupField).sum(sumField).as("sum");
        Aggregation aggregation = Aggregation.newAggregation(match, group);
        AggregationResults<Result> results = mongoTemplate.aggregate(aggregation, this.getCollectionName(), Result.class);
        return results.getMappedResults();
    }

    public List<String> distinct(String field, Query query) {
        List<String> distinctList = new ArrayList<>();
        mongoTemplate.getCollection(this.getCollectionName()).distinct(field, query.getQueryObject(), String.class).into(distinctList);
        return distinctList;
    }
}
