package net.xplife.system.sms.client.dy;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import com.alibaba.fastjson.JSON;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import net.xplife.system.sms.core.SmsClientInterface;
import net.xplife.system.sms.client.config.AldyConfig;
import net.xplife.system.sms.core.SmsMessage;
import net.xplife.system.sms.utils.SmsUtil;
import net.xplife.system.tools.util.core.ToolsKit;
public class TaobaoSmsUtils implements SmsClientInterface<IAcsClient> {
    private static TaobaoSmsUtils taobaoSmsUtils;
    private static IAcsClient     acsClient;
    private static Lock           smsKitLock    = new ReentrantLock();
    private static Lock           smsClientLock = new ReentrantLock();

    public static TaobaoSmsUtils getInstance() {
        try {
            smsKitLock.lock();
            if (taobaoSmsUtils == null) {
                taobaoSmsUtils = new TaobaoSmsUtils();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            smsKitLock.unlock();
        }
        return taobaoSmsUtils;
    }

    @Override
    public void initClient() {
        try {
            smsClientLock.lock();
            if (acsClient == null) {
                // 初始化acsClient,暂不支持region化
                IClientProfile profile = DefaultProfile.getProfile(AldyConfig.getInstance().getRegionId(), AldyConfig.getInstance().getAccessKey(),
                        AldyConfig.getInstance().getAccessKeySecret());
                DefaultProfile.addEndpoint(AldyConfig.getInstance().getEndpointName(), AldyConfig.getInstance().getRegionId(),
                        AldyConfig.getInstance().getProduct(), AldyConfig.getInstance().getDomain());
                acsClient = new DefaultAcsClient(profile);
            }
            if (acsClient == null) {
                throw new NullPointerException("create AcsClient fail!");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            smsClientLock.unlock();
        }
    }

    @Override
    public IAcsClient getSmsClient() {
        if (acsClient == null) {
            initClient();
        }
        return acsClient;
    }

    public SmsMessage sendSms(SmsMessage message) {
        if (null == acsClient) {
            throw new NullPointerException("client is null");
        }
        SendSmsRequest request = new SendSmsRequest();
        // 必填:待发送手机号
        request.setPhoneNumbers(SmsUtil.builderPhones(message.getPhones(), AldyConfig.getInstance().getMaxRecNum()));
        // 必填:短信签名-可在短信控制台中找到
        request.setSignName(AldyConfig.getInstance().getSignName());
        // 必填:短信模板-可在短信控制台中找到
        request.setTemplateCode(message.getTplCode());
        // 可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
        request.setTemplateParam(JSON.toJSONString(message.getParams()));
        // 选填-上行短信扩展码(无特殊需求用户请忽略此字段)
        // request.setSmsUpExtendCode("90997");
        // 可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
        request.setOutId("");
        try {
            SendSmsResponse response = acsClient.getAcsResponse(request);
            if (response.getCode() != null && response.getCode().equals("OK")) {
                message.setSuccess(true);
                message.setContent(SmsUtil.builderContent(message));
            } else {
                System.out.println("response.getBody():  " + response.getMessage());
                message.setSuccess(false);
                message.setContent(response.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            message.setSuccess(false);
            message.setContent(e.getMessage());
        }
        return message;
    }

    public SmsMessage showSms(SmsMessage message) {
        if (ToolsKit.isEmpty(message)) return null;
        if (ToolsKit.isNotEmpty(message.getParams())) {
            message.setContent(SmsUtil.builderContent(message));
        }
        return message;
    }
}
