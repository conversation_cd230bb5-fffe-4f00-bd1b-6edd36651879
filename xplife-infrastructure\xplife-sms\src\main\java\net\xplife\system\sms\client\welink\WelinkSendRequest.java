package net.xplife.system.sms.client.welink;
/**
 * 微网联通的短信发送请求对象
 */
public class WelinkSendRequest {
    private String recNum;     // 接收人信息
    private Object smsParam;   // 发送的参数
    private String smsType;    // 发送模板ID
    private String smsContent; // 短信内容

    public WelinkSendRequest() {
    }

    /**
     * 构造函数
     * 
     * @param recNum
     *            接收人
     * @param smsParam
     *            需要替换的参数
     * @param smsType
     *            模板ID
     * @param smsContent
     */
    public WelinkSendRequest(String recNum, Object smsParam, String smsType, String smsContent) {
        this.recNum = recNum;
        this.smsParam = smsParam;
        this.smsType = smsType;
        this.smsContent = smsContent;
    }

    public String getRecNum() {
        return recNum;
    }

    public void setRecNum(String recNum) {
        this.recNum = recNum;
    }

    public Object getSmsParam() {
        return smsParam;
    }

    public void setSmsParam(Object smsParam) {
        this.smsParam = smsParam;
    }

    public String getSmsType() {
        return smsType;
    }

    public void setSmsType(String smsType) {
        this.smsType = smsType;
    }

    public String getSmsContent() {
        return smsContent;
    }

    public void setSmsContent(String smsContent) {
        this.smsContent = smsContent;
    }
}
