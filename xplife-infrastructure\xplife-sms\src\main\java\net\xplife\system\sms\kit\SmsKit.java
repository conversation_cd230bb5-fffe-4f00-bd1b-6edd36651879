package net.xplife.system.sms.kit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import net.xplife.system.sms.client.dy.TaobaoSmsUtils;
import net.xplife.system.sms.client.welink.WelinkSmsUtils;
import net.xplife.system.sms.core.SmsClientInterface;
import net.xplife.system.sms.core.SmsMessage;
import net.xplife.system.sms.utils.SmsChanelEnum;
import net.xplife.system.sms.utils.SmsUtil;
import net.xplife.system.tools.util.core.ToolsKit;
final public class SmsKit {
    private static Map<String, SmsClientInterface<?>> factoryMap = new HashMap<>();
    private static SmsKit                             smsKit;
    private static SmsChanelEnum                      smsChanelEnum;

    public static SmsKit getInstance() {
        if (null == smsKit) {
            smsKit = new SmsKit();
        }
        return smsKit;
    }

    /**
     * 初始化
     * 
     * @param chanelEnum
     */
    public void init(SmsChanelEnum chanelEnum) {
        SmsClientInterface<?> smsClientInterface = null;
        if (chanelEnum.name().equals(SmsChanelEnum.ALIYUN.name())) {
            smsClientInterface = TaobaoSmsUtils.getInstance();
        } else if (chanelEnum.name().equals(SmsChanelEnum.WELINK.name())) {
            smsClientInterface = WelinkSmsUtils.getInstance();
        }
        if (ToolsKit.isNotEmpty(smsClientInterface)) {
            smsClientInterface.initClient();
            factoryMap.put(chanelEnum.name(), smsClientInterface);
        }
    }

    public SmsKit channel(SmsChanelEnum chanelEnum) {
        smsChanelEnum = chanelEnum;
        return this;
    }

    /**
     * 发送短信
     * 
     * @param phoneList
     *            接收人电话号码集合
     * @param type
     *            自定义的短信模板代码
     * @param paramMap
     *            要替换短信模板的参数集合
     * @return
     */
    public SmsMessage send(String phone, String type, Map<String, String> paramMap) {
        List<String> phoneList = new ArrayList<String>();
        phoneList.add(phone);
        return send(phoneList, type, paramMap);
    }

    /**
     * 批量发送短信
     * 
     * @param phoneList
     *            接收人电话号码集合
     * @param type
     *            自定义的短信模板代码
     * @param paramMap
     *            要替换短信模板的参数集合
     * @return
     */
    public SmsMessage send(List<String> phoneList, String type, Map<String, String> paramMap) {
        SmsClientInterface<?> smsClientInterface = factoryMap.get(smsChanelEnum.name());
        if (ToolsKit.isNotEmpty(smsClientInterface)) {
            SmsMessage message = SmsUtil.builderSmsMessage(type);
            message.setParams(paramMap);
            message.setPhones(phoneList);
            return smsClientInterface.sendSms(message);
        }
        return null;
    }

    /**
     * 重载模板JSON内容，当模板发生数据变化时，可手工重载 新版jar会自动重载
     * 
     * @return
     */
    public boolean reloadTemplate() {
        try {
            SmsUtil.reloadTemplate();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
