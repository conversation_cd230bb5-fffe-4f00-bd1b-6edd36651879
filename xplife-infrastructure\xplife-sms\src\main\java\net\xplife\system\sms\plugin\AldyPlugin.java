package net.xplife.system.sms.plugin;
import net.xplife.system.sms.kit.SmsKit;
import net.xplife.system.sms.utils.SmsChanelEnum;
import net.xplife.system.tools.common.core.IPlugin;
import net.xplife.system.sms.client.config.AldyConfig;

public class AldyPlugin implements IPlugin {
    public AldyPlugin(String regionId, String accessKey, String accessKeySecret, String endpointName, String product, String domain, int maxRecNum,
            String signName, String charEncode, String smsTemplateUrl) {
        AldyConfig.getInstance().setRegionId(regionId);
        AldyConfig.getInstance().setAccessKey(accessKey);
        AldyConfig.getInstance().setAccessKeySecret(accessKeySecret);
        AldyConfig.getInstance().setEndpointName(endpointName);
        AldyConfig.getInstance().setProduct(product);
        AldyConfig.getInstance().setDomain(domain);
        AldyConfig.getInstance().setMaxRecNum(maxRecNum);
        AldyConfig.getInstance().setSignName(signName);
        AldyConfig.getInstance().setCharEncode(charEncode);
        AldyConfig.getInstance().setSmsTemplateUrl(smsTemplateUrl);
    }

    @Override
    public void init() throws Exception {
    }

    @Override
    public void start() throws Exception {
        SmsKit.getInstance().init(SmsChanelEnum.ALIYUN);
    }

    @Override
    public void stop() throws Exception {
    }
}
