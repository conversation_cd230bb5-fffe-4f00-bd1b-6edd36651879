package net.xplife.system.storage.aliyun.core;
import java.util.Iterator;
import java.util.Map;
import java.util.Vector;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.request.PutLogsRequest;
import net.xplife.system.storage.config.SlsConfig;
import net.xplife.system.storage.enums.SLSAccessEnum;
import net.xplife.system.storage.utils.IBaseClient;
import net.xplife.system.storage.utils.Tools;
import net.xplife.system.tools.util.core.ToolsKit;
public class SLSUtils implements IBaseClient<Client> {
    private static Lock     slsKitLock    = new ReentrantLock();
    private Lock            slsClientLock = new ReentrantLock();
    private Client          client;
    private static SLSUtils SLSUtils;

    @Override
    public void init() {
        try {
            slsClientLock.lock();
            if (client == null) {
                client = new Client(SlsConfig.getInstance().getSlsEndPoint(), SlsConfig.getInstance().getSlsAccesskey(),
                        SlsConfig.getInstance().getSlsAccesskeySecret());
                System.out.println("Connent SLS is Success...");
            }
            if (client == null) {
                throw new Exception("create SLSClient fail!");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            slsClientLock.unlock();
        }
    }

    @Override
    public Client getClient() {
        if (client == null) {
            init();
        }
        return client;
    }

    @Override
    public boolean isSuccess() {
        return client != null;
    }

    public static SLSUtils getInstance() {
        try {
            slsKitLock.lock();
            if (SLSUtils == null) {
                SLSUtils = new SLSUtils();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            slsKitLock.unlock();
        }
        return SLSUtils;
    }

    /**
     * 提交日志
     * 
     * @param slsEnum
     * @param topic
     * @param logItemMap
     * @throws Exception
     */
    public void putlog(SLSAccessEnum slsEnum, String topic, Map<String, String> logItemMap) throws Exception {
        if (ToolsKit.isNotEmpty(logItemMap)) {
            Vector<LogItem> logGroup = new Vector<LogItem>();
            LogItem logItem = new LogItem((int) (System.currentTimeMillis() / 1000));
            for (Iterator<Map.Entry<String, String>> it = logItemMap.entrySet().iterator(); it.hasNext();) {
                Map.Entry<String, String> entry = it.next();
                if (ToolsKit.isNotEmpty(entry)) {
                    String value = entry.getValue();
                    logItem.PushBack(entry.getKey(), ToolsKit.isEmpty(value) ? "" : value);
                }
            }
            logGroup.add(logItem);
            PutLogsRequest reques = new PutLogsRequest(slsEnum.getProject(), slsEnum.getStore(), topic, Tools.getLocalHostIP(), logGroup,
                    Tools.getLogShardHash());
            getClient().PutLogs(reques);
        }
    }
}
