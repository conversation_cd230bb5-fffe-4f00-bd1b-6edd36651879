package net.xplife.system.storage.aliyun.core;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse;
import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse.Credentials;
import net.xplife.system.storage.config.StsConfig;
import net.xplife.system.storage.dto.STSMessage;
import net.xplife.system.storage.enums.STSParamEnum;
import net.xplife.system.storage.utils.IBaseClient;
import net.xplife.system.tools.util.core.ToolsKit;
public class STSUtils implements IBaseClient<IAcsClient> {
    private static Lock       stsKitLock    = new ReentrantLock();
    private Lock              stsClientLock = new ReentrantLock();
    private static IAcsClient client;
    private static STSUtils   stsUtils;

    public static STSUtils getInstance() {
        try {
            stsKitLock.lock();
            if (stsUtils == null) {
                stsUtils = new STSUtils();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            stsKitLock.unlock();
        }
        return stsUtils;
    }

    @Override
    public void init() {
        try {
            stsClientLock.lock();
            if (client == null) {
                DefaultProfile.addEndpoint("", "", "Sts", StsConfig.getInstance().getStsEndPoint());
                IClientProfile profile = DefaultProfile.getProfile(StsConfig.getInstance().getStsRegion(), StsConfig.getInstance().getStsAccesskey(),
                        StsConfig.getInstance().getStsAccesskeySecret());
                client = new DefaultAcsClient(profile);
                System.out.println("create AcsClient is Success...");
            }
            if (client == null) {
                throw new Exception("create AcsClient fail!");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            stsClientLock.unlock();
        }
    }

    @Override
    public IAcsClient getClient() {
        if (client == null) {
            init();
        }
        return client;
    }

    @Override
    public boolean isSuccess() {
        return client != null;
    }

    // 创建一个 AssumeRoleRequest 并设置请求参数
    private AssumeRoleRequest createAssumeRoleRequest() {
        AssumeRoleRequest request = new AssumeRoleRequest();
        request.setMethod(MethodType.POST);
        request.setProtocol(ProtocolType.HTTPS);
        request.setRoleArn(StsConfig.getInstance().getStsRoleArn());
        request.setRoleSessionName(StsConfig.getInstance().getStsRoleSessionName());
        request.setDurationSeconds(StsConfig.getInstance().getStsDurationSeconds());
        return request;
    }

    // 发起请求，并得到response
    public STSMessage getSTSResponse() {
        return getSTSResponse(getEndPoints());
    }

    // 发起请求，并得到response
    private STSMessage getSTSResponse(Map<String, Map<String, String>> paramMap) {
        if (paramMap.isEmpty()) throw new NullPointerException("sts endpoint map is null");
        try {
            AssumeRoleResponse response = getClient().getAcsResponse(createAssumeRoleRequest());
            if (ToolsKit.isEmpty(response) || ToolsKit.isEmpty(response.getCredentials())) return null;
            STSMessage sts = new STSMessage();
            Credentials credentials = response.getCredentials();
            sts.setAccessKeyId(credentials.getAccessKeyId());
            sts.setAccessKeySecret(credentials.getAccessKeySecret());
            sts.setExpiration(credentials.getExpiration());
            long timeout = System.currentTimeMillis() + StsConfig.getInstance().getStsDurationSeconds() * 1000;
            sts.setTimeOut(timeout);
            sts.setRequestId(response.getRequestId());
            sts.setSecurityToken(credentials.getSecurityToken());
            sts.setParamMap(paramMap);
            return sts;
        } catch (ServerException e) {
            e.printStackTrace();
        } catch (ClientException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取配置参数
     * 
     * @return
     */
    private static Map<String, Map<String, String>> getEndPoints() {
        String[] arrays = StsConfig.getInstance().getStsEndPoints().split(",");
        Map<String, Map<String, String>> paramsMap = new HashMap<String, Map<String, String>>();
        if (null != arrays && arrays.length > 0) {
            for (String str : arrays) {
                String[] values = str.split("_");
                String key = values[0].toUpperCase();
                String endpoint = values[1];
                Map<String, String> subMap = new HashMap<String, String>();
                subMap.put(STSParamEnum.ENDPOINT.getName(), endpoint);
                for (STSParamEnum enums : STSParamEnum.values()) {
                    if (enums.name().indexOf(key) > -1) {
                        if (enums.name().equals(STSParamEnum.OSS_BUCKET.name())) {
                            subMap.put(enums.getName(), values[2]);
                        } else {
                            subMap.put(enums.getName(), enums.getValue());
                        }
                    }
                }
                paramsMap.put(key, subMap);
            }
        }
        return paramsMap;
    }
}
