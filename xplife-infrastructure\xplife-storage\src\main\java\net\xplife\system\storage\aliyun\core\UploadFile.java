package net.xplife.system.storage.aliyun.core;
import java.io.File;
import net.xplife.system.storage.utils.Tools;
/**
 * UploadFile.
 */
public class UploadFile {
    private String parameterName;    // 表单名称
    private String nginxPath;        // 映射路径
    private String thumbUrl;         // 缩略图映射路径
    private String serverDirectory;  // 服务器保存路径，相对于/uploadfiles文件下
    private String fileName;         // 上传保存后的文件名
    private String originalFileName; // 未上传前的文件名
    private String contentType;      // 文件类型
    private long   fileSize;         // 文件大小
    private String extName;          // 扩展名

    public UploadFile(String parameterName, String nginxPath, String thumbUrl, String serverDirectory, String filesystemName, String originalFileName,
            String contentType, long fileSize) {
        this.parameterName = parameterName;
        this.nginxPath = nginxPath;
        this.thumbUrl = thumbUrl;
        this.serverDirectory = serverDirectory;
        this.fileName = filesystemName;
        this.originalFileName = originalFileName;
        this.contentType = contentType;
        this.fileSize = fileSize;
        this.extName = fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    public String getParameterName() {
        return parameterName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileName() {
        return fileName;
    }

    public String getOriginalFileName() {
        return originalFileName;
    }

    public String getContentType() {
        return contentType;
    }

    public String getServerDirectory() {
        return serverDirectory;
    }

    public File getFile() {
        if (serverDirectory == null || fileName == null) {
            return null;
        } else {
            String path = Tools.getWebRootPath() + serverDirectory + "/" + fileName;
            return new File(path);
        }
    }

    public long getFileSize() {
        return fileSize;
    }

    public String getExtName() {
        return extName;
    }

    public String getServerFilePath() {
        return serverDirectory + "/" + fileName;
    }

    public String getNginxPath() {
        return nginxPath;
    }

    public void setNginxPath(String nginxPath) {
        this.nginxPath = nginxPath;
    }

    public String getThumbUrl() {
        return thumbUrl;
    }

    public void setThumbUrl(String thumbUrl) {
        this.thumbUrl = thumbUrl;
    }

    public String setServerDirectory(String serverDirectory) {
        return this.serverDirectory = serverDirectory;
    }
}
