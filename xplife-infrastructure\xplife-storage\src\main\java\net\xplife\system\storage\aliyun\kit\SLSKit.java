package net.xplife.system.storage.aliyun.kit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import net.xplife.system.storage.aliyun.core.SLSUtils;
/**
 * 阿里日志服务工具类
 */
public class SLSKit {
    private static Logger   logger = LoggerFactory.getLogger(SLSKit.class);
    private static SLSUtils slsUtils;

    public static SLSUtils getInstance() {
        try {
            if (slsUtils == null) {
                slsUtils = SLSUtils.getInstance();
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        return slsUtils;
    }
}
