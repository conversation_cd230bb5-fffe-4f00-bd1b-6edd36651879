package net.xplife.system.storage.aliyun.kit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import net.xplife.system.storage.aliyun.core.STSUtils;
/**
 * 阿里获取 token工具类
 */
public class STSKit {
    private static Logger   logger = LoggerFactory.getLogger(SLSKit.class);
    private static STSUtils stsUtils;

    public static STSUtils getInstance() {
        try {
            if (stsUtils == null) {
                stsUtils = STSUtils.getInstance();
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        return stsUtils;
    }
}
