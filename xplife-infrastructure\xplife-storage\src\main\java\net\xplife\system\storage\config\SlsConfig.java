package net.xplife.system.storage.config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
public class SlsConfig {
    private static final Logger logger = LoggerFactory.getLogger(SlsConfig.class);
    private String              slsAccesskey;
    private String              slsAccesskeySecret;
    private String              slsEndPoint;
    private static SlsConfig    slsConfig;

    public static SlsConfig getInstance() {
        try {
            if (null == slsConfig) {
                slsConfig = new SlsConfig();
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        return slsConfig;
    }

    private SlsConfig() {
    }

    public String getSlsAccesskey() {
        return slsAccesskey;
    }

    public void setSlsAccesskey(String slsAccesskey) {
        this.slsAccesskey = slsAccesskey;
    }

    public String getSlsAccesskeySecret() {
        return slsAccesskeySecret;
    }

    public void setSlsAccesskeySecret(String slsAccesskeySecret) {
        this.slsAccesskeySecret = slsAccesskeySecret;
    }

    public String getSlsEndPoint() {
        return slsEndPoint;
    }

    public void setSlsEndPoint(String slsEndPoint) {
        this.slsEndPoint = slsEndPoint;
    }
}
