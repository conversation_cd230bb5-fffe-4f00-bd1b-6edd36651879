package net.xplife.system.storage.config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
public class StsConfig {
    private static final Logger logger = LoggerFactory.getLogger(StsConfig.class);
    private String              stsAccesskey;
    private String              stsAccesskeySecret;
    private String              stsRegion;
    private String              stsEndPoint;
    private String              stsRoleArn;
    private String              stsRoleSessionName;
    private long                stsDurationSeconds;
    private String              charEncode;
    private String              stsEndPoints;
    private static StsConfig    stsConfig;

    public static StsConfig getInstance() {
        try {
            if (null == stsConfig) {
                stsConfig = new StsConfig();
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        return stsConfig;
    }

    private StsConfig() {
    }

    public String getStsAccesskey() {
        return stsAccesskey;
    }

    public void setStsAccesskey(String stsAccesskey) {
        this.stsAccesskey = stsAccesskey;
    }

    public String getStsAccesskeySecret() {
        return stsAccesskeySecret;
    }

    public void setStsAccesskeySecret(String stsAccesskeySecret) {
        this.stsAccesskeySecret = stsAccesskeySecret;
    }

    public String getStsRegion() {
        return stsRegion;
    }

    public void setStsRegion(String stsRegion) {
        this.stsRegion = stsRegion;
    }

    public String getStsEndPoint() {
        return stsEndPoint;
    }

    public void setStsEndPoint(String stsEndPoint) {
        this.stsEndPoint = stsEndPoint;
    }

    public String getStsRoleArn() {
        return stsRoleArn;
    }

    public void setStsRoleArn(String stsRoleArn) {
        this.stsRoleArn = stsRoleArn;
    }

    public String getStsRoleSessionName() {
        return stsRoleSessionName;
    }

    public void setStsRoleSessionName(String stsRoleSessionName) {
        this.stsRoleSessionName = stsRoleSessionName;
    }

    public long getStsDurationSeconds() {
        return stsDurationSeconds;
    }

    public void setStsDurationSeconds(long stsDurationSeconds) {
        this.stsDurationSeconds = stsDurationSeconds;
    }

    public String getCharEncode() {
        return charEncode;
    }

    public void setCharEncode(String charEncode) {
        this.charEncode = charEncode;
    }

    public String getStsEndPoints() {
        return stsEndPoints;
    }

    public void setStsEndPoints(String stsEndPoints) {
        this.stsEndPoints = stsEndPoints;
    }
}
