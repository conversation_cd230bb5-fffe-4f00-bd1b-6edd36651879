package net.xplife.system.storage.dto;
import java.util.Map;
public class STSMessage {
    private String                           securityToken;  // 安全token
    private String                           accessKeySecret;// accessKeySecret
    private String                           accessKeyId;    // accessKey
    private String                           expiration;     // 超时时长
    private String                           requestId;      // 请求ID
    private long                             timeOut;        // 超时时间
    private Map<String, Map<String, String>> paramMap;

    public String getSecurityToken() {
        return securityToken;
    }

    public void setSecurityToken(String securityToken) {
        this.securityToken = securityToken;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getExpiration() {
        return expiration;
    }

    public void setExpiration(String expiration) {
        this.expiration = expiration;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public STSMessage() {
        super();
    }

    public Map<String, Map<String, String>> getParamMap() {
        return paramMap;
    }

    public void setParamMap(Map<String, Map<String, String>> paramMap) {
        this.paramMap = paramMap;
    }

    public long getTimeOut() {
        return timeOut;
    }

    public void setTimeOut(long timeOut) {
        this.timeOut = timeOut;
    }
}
