package net.xplife.system.storage.enums;
/**
 * SLS访问日志枚举 项目，日志库，说明
 */
public enum SLSAccessEnum {
    QINGPLUS("syt-accesslog", "qingplus", "qingplus", "轻加接口");
    private final String project;     // 项目名称
    private final String store;       // 日志库名称(微服务名称)
    private final String productCode; // 对应的项目名称
    private final String desc;        // 说明

    public String getProject() {
        return project;
    }

    public String getStore() {
        return store;
    }

    public String getDesc() {
        return desc;
    }

    public String getProductCode() {
        return productCode;
    }

    private SLSAccessEnum(String project, String store, String productCode, String desc) {
        this.project = project;
        this.store = store;
        this.productCode = productCode;
        this.desc = desc;
    }
}
