package net.xplife.system.storage.hw.core;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import com.obs.services.ObsClient;
import com.obs.services.model.ListObjectsRequest;
import com.obs.services.model.ObjectListing;
import com.obs.services.model.ObjectMetadata;
import com.obs.services.model.ObsBucket;
import com.obs.services.model.ObsObject;
import com.obs.services.model.PutObjectRequest;
import com.obs.services.model.PutObjectResult;
import net.xplife.system.storage.config.OssConfig;
import net.xplife.system.storage.enums.OSSContentType;
import net.xplife.system.storage.utils.IBaseClient;
import net.xplife.system.tools.util.core.ToolsKit;
public class OBSUtils implements IBaseClient<ObsClient> {
    private static Lock     ossKitLock    = new ReentrantLock();
    private Lock            ossClientLock = new ReentrantLock();
    private ObsClient       client;
    private static OBSUtils OBSUtils;

    @Override
    public void init() {
        try {
            ossClientLock.lock();
            if (client == null) {
                client = new ObsClient(OssConfig.getInstance().getOssAccesskey(), OssConfig.getInstance().getOssAccesskeySecret(),
                        OssConfig.getInstance().getOssEndPoint());
                System.out.println("Connent OBS is Success...");
            }
            if (client == null) {
                throw new Exception("create OBSClient fail!");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            ossClientLock.unlock();
        }
    }

    @Override
    public ObsClient getClient() {
        if (client == null) {
            init();
        }
        return client;
    }

    @Override
    public boolean isSuccess() {
        return client != null;
    }

    public static OBSUtils getInstance() {
        try {
            ossKitLock.lock();
            if (OBSUtils == null) {
                OBSUtils = new OBSUtils();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            ossKitLock.unlock();
        }
        return OBSUtils;
    }

    public ObsBucket createBucket(String bucketName) {
        // 新建一个Bucket
        try {
            return getClient().createBucket(bucketName);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public void deleteBucket(String bucketName) {
        try {
            getClient().deleteBucket(bucketName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void putObject(String bucketName, String key, String filePath, String callbackUrl) throws Exception {
        try {
            URL url = new URL(callbackUrl);
            callbackUrl = url.toString();
        } catch (Exception e) {
            throw new IllegalAccessException("请输入正确格式的URL地址");
        }
        File file = new File(filePath);
        key = key.startsWith("/") ? key.substring(1, key.length()) : key;
        // 创建上传Object的Metadata
        ObjectMetadata meta = new ObjectMetadata();
        // 必须设置ContentLength
        meta.setContentLength(file.length());
        // 设置ContentType， 默认为image/jpeg 如果不设置的话，则FF及chrome打开则会变成直接下载，其它的使用默认的
        String flag = key.substring(key.lastIndexOf(".") + 1, key.length()).toLowerCase();
        meta.setContentType(OSSContentType.get(flag));
        // 备份文件
        // copyObject(bucketName, key);
        // 获取指定文件的输入流
        InputStream content = new FileInputStream(file);
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, content);
        PutObjectResult putObjectResult = getClient().putObject(putObjectRequest);
    }

    /**
     * 上传文件到指定的bucket
     * 
     * @param bucketName
     *            Bucket是OSS上的命名空间，相当于数据的容器，可以存储若干数据实体（Object）
     * @param key
     *            上传后的文件名
     * @param filePath
     *            文件路径
     * @throws FileNotFoundException
     */
    public void putObject(String bucketName, String key, String filePath) throws Exception {
        // 获取指定文件的输入流
        File file = new File(filePath);
        InputStream content = new FileInputStream(file);
        putObject(bucketName, key, file.length(), content);
    }

    /**
     * 上传文件到指定的bucket
     * 
     * @param bucketName
     *            Bucket是OSS上的命名空间，相当于数据的容器，可以存储若干数据实体（Object）
     * @param key
     *            上传后的文件名
     * @param fileLength
     *            文件长度
     * @param is
     *            文件InputStream
     * @throws FileNotFoundException
     */
    public void putObject(String bucketName, String key, long fileLength, InputStream is) {
        try {
            key = key.startsWith("/") ? key.substring(1, key.length()) : key;
            // 创建上传Object的Metadata
            ObjectMetadata meta = new ObjectMetadata();
            // 必须设置ContentLength
            meta.setContentLength(fileLength);
            // 设置ContentType， 默认为image/jpeg 如果不设置的话，则FF及chrome打开则会变成直接下载，其它的使用默认的
            String flag = key.substring(key.lastIndexOf(".") + 1, key.length()).toLowerCase();
            meta.setContentType(OSSContentType.get(flag));
            // 备份文件
            // copyObject(bucketName, key);
            // 上传Object.
            getClient().putObject(bucketName, key, is, meta);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("上传Object时出错: " + e.getMessage());
        }
    }

    /**
     * 上传文件到指定的bucket
     * 
     * @param bucketName
     *            Bucket是OSS上的命名空间，相当于数据的容器，可以存储若干数据实体（Object）
     * @param key
     *            上传后的文件名
     * @param fileLength
     *            文件长度
     * @param is
     *            文件InputStream
     * @param contentTypeMap
     *            key为文件扩展名，value为contentType 文件ContentType
     * @throws FileNotFoundException
     */
    public void putObject(String bucketName, String key, long fileLength, InputStream is, Map<String, String> contentTypeMap) {
        OSSContentType.add(contentTypeMap);
        putObject(bucketName, key, fileLength, is);
    }

    /**
     * 删除Object
     * 
     * @param bucketName
     * @param key
     */
    public void deleteObject(String bucketName, String key) {
        try {
            // 备份文件
            // copyObject(bucketName, key);
            getClient().deleteObject(bucketName, key);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 备份文件，只支持同区域的BucketName
     * 
     * @param srcBucketName
     *            文件备份前的BucketName
     * @param srcKey
     *            文件备份前的文件路径
     */
    public void copyObject(String srcBucketName, String srcKey) {
        boolean found = getClient().doesObjectExist(srcBucketName, srcKey);
        if (found) {
            try {
                String destBucketName = srcBucketName + "-backup";
                StringBuffer destKey = new StringBuffer();
                String[] srcKeyArray = srcKey.split("/");
                for (int i = 0; i < srcKeyArray.length; i++) {
                    if (i == 2) {
                        destKey.append(ToolsKit.Date.format(new Date(), "yyyyMMddHHmmss")).append("/");
                    }
                    destKey.append(srcKeyArray[i]).append("/");
                }
                if (destKey.length() > 1) {
                    destKey.deleteCharAt(destKey.length() - 1);
                }
                getClient().copyObject(srcBucketName, srcKey, destBucketName, destKey.toString());
                System.out.println("##########copy file " + destKey + " to " + destBucketName + " is done!");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void getObject(String bucketName, String prefix) {
        // 构造ListObjectsRequest请求
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName);
        // "/" 为文件夹的分隔符
        // listObjectsRequest.setDelimiter("/");
        // 列出prefix目录下的所有文件和文件夹
        listObjectsRequest.setPrefix(prefix); // .setPrefix("fun/");
        ObjectListing listing = getClient().listObjects(listObjectsRequest);
        // 遍历所有Object
        System.out.println("Objects:");
        for (ObsObject obsObject : listing.getObjects()) {
            System.out.println("##########: " + obsObject.getObjectKey());
        }
        // 遍历所有CommonPrefix
        System.out.println("\nCommonPrefixs:");
        for (String commonPrefix : listing.getCommonPrefixes()) {
            System.out.println(commonPrefix);
        }
    }

    /**
     * 判断文件是否存在
     * 
     * @param srcBucketName
     *            bucketName
     * @param srcKey
     *            文件路径
     * @return
     */
    public boolean objectExist(String srcBucketName, String srcKey) {
        return getClient().doesObjectExist(srcBucketName, srcKey);
    }

    /**
     * 网络流式上传文件
     * 
     * @param sourceUrl
     *            源文件地址
     * @param bucketName
     *            bucketName
     * @param targetUrl
     *            目标地址
     */
    public boolean putObjectByStream(String sourceUrl, String bucketName, String targetUrl) {
        boolean isSuccess = false;
        try {
            // 上传网络流。
            InputStream inputStream = new URL(sourceUrl).openStream();
            getClient().putObject(bucketName, targetUrl, inputStream);
            isSuccess = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return isSuccess;
    }
}
