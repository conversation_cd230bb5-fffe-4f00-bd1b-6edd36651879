package net.xplife.system.storage.hw.kit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import net.xplife.system.storage.hw.core.OBSUtils;
public class OBSKit {
    private static Logger   logger = LoggerFactory.getLogger(OBSKit.class);
    private static OBSUtils obsUtils;

    public static OBSUtils getInstance() {
        try {
            if (obsUtils == null) {
                obsUtils = OBSUtils.getInstance();
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        return obsUtils;
    }
}
