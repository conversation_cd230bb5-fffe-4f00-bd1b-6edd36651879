/*
package net.xplife.system.storage.jd.net.xplife.system.web.core;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.SDKGlobalConfiguration;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.Bucket;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import net.xplife.system.storage.net.xplife.system.web.config.OssConfig;
import net.xplife.system.storage.net.xplife.system.web.enums.OSSContentType;
import net.xplife.system.storage.net.xplife.system.web.utils.IBaseClient;
import net.xplife.system.tools.util.net.xplife.system.web.core.ToolsKit;
public class OSSUtils implements IBaseClient<AmazonS3> {
    private static Lock     ossKitLock    = new ReentrantLock();
    private Lock            ossClientLock = new ReentrantLock();
    private AmazonS3        s3;
    private static OSSUtils OSSUtils;

    @Override
    public void init() {
        try {
            ossClientLock.lock();
            if (s3 == null) {
                String accessKey = OssConfig.getInstance().getOssAccesskey();
                String secretKey = OssConfig.getInstance().getOssAccesskeySecret();
                String endpoint = OssConfig.getInstance().getOssEndPoint();
                System.setProperty(SDKGlobalConfiguration.ENABLE_S3_SIGV4_SYSTEM_PROPERTY, "true");
                ClientConfiguration net.xplife.system.web.config = new ClientConfiguration();
                AwsClientBuilder.EndpointConfiguration endpointConfig = new AwsClientBuilder.EndpointConfiguration(endpoint, "cn-east-2");
                AWSCredentials awsCredentials = new BasicAWSCredentials(accessKey, secretKey);
                AWSCredentialsProvider awsCredentialsProvider = new AWSStaticCredentialsProvider(awsCredentials);
                s3 = AmazonS3Client.builder().withEndpointConfiguration(endpointConfig).withClientConfiguration(net.xplife.system.web.config).withCredentials(awsCredentialsProvider)
                        .disableChunkedEncoding().withPathStyleAccessEnabled(true).build();
                System.out.println("Connent OSS is Success...");
            }
            if (s3 == null) {
                throw new NullPointerException("create OSSClient fail!");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            ossClientLock.unlock();
        }
    }

    @Override
    public AmazonS3 getClient() {
        if (s3 == null) {
            init();
        }
        return s3;
    }

    @Override
    public boolean isSuccess() {
        return s3 != null;
    }

    public static OSSUtils getInstance() {
        try {
            ossKitLock.lock();
            if (OSSUtils == null) {
                OSSUtils = new OSSUtils();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            ossKitLock.unlock();
        }
        return OSSUtils;
    }

    */
/**
     * 列举Bucket
     * 
     * @return
     *//*

    public List<S3ObjectSummary> getS3ObjectSummarys(String bucketName) {
        try {
            ListObjectsV2Result result = getClient().listObjectsV2(bucketName);
            return result.getObjectSummaries();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    */
/**
     * 获取所有的管理空间
     * 
     * @return
     *//*

    public List<Bucket> getBucketList() {
        try {
            return getClient().listBuckets();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    */
/**
     * 创建一个管理空间
     * 
     * @param bucketName
     *            管理空间名称
     * @return
     *//*

    public Bucket createBucket(String bucketName) {
        // 新建一个Bucket
        try {
            return getClient().createBucket(bucketName);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    */
/**
     * 根据名称删除一个管理空间
     * 
     * @param bucketName
     *            管理空间名称
     *//*

    public void deleteBucket(String bucketName) {
        try {
            getClient().deleteBucket(bucketName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    */
/**
     * 上传文件到指定的bucket
     * 
     * @param bucketName
     *            Bucket是OSS上的命名空间，相当于数据的容器，可以存储若干数据实体（Object）
     * @param key
     *            上传后的文件名
     * @param filePath
     *            文件路径
     * @throws FileNotFoundException
     *//*

    public void putObject(String bucketName, String key, String filePath) throws Exception {
        // 获取指定文件的输入流
        File file = new File(filePath);
        InputStream content = new FileInputStream(file);
        putObject(bucketName, key, file.length(), content);
    }

    */
/**
     * 上传文件到指定的bucket
     * 
     * @param bucketName
     *            Bucket是OSS上的命名空间，相当于数据的容器，可以存储若干数据实体（Object）
     * @param key
     *            上传后的文件名
     * @param fileLength
     *            文件长度
     * @param is
     *            文件InputStream
     * @throws FileNotFoundException
     *//*

    public void putObject(String bucketName, String key, long fileLength, InputStream is) {
        try {
            key = key.startsWith("/") ? key.substring(1, key.length()) : key;
            // 创建上传Object的Metadata
            ObjectMetadata meta = new ObjectMetadata();
            // 必须设置ContentLength
            meta.setContentLength(fileLength);
            // 设置ContentType， 默认为image/jpeg 如果不设置的话，则FF及chrome打开则会变成直接下载，其它的使用默认的
            String flag = key.substring(key.lastIndexOf(".") + 1, key.length()).toLowerCase();
            meta.setContentType(OSSContentType.get(flag));
            // 备份文件
            // copyObject(bucketName, key);
            // 上传Object.
            getClient().putObject(bucketName, key, is, meta);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("上传Object时出错: " + e.getMessage());
        }
    }

    */
/**
     * 上传文件到指定的bucket
     * 
     * @param bucketName
     *            Bucket是OSS上的命名空间，相当于数据的容器，可以存储若干数据实体（Object）
     * @param key
     *            上传后的文件名
     * @param fileLength
     *            文件长度
     * @param is
     *            文件InputStream
     * @param contentTypeMap
     *            key为文件扩展名，value为contentType 文件ContentType
     * @throws FileNotFoundException
     *//*

    public void putObject(String bucketName, String key, long fileLength, InputStream is, Map<String, String> contentTypeMap) {
        OSSContentType.add(contentTypeMap);
        putObject(bucketName, key, fileLength, is);
    }

    */
/**
     * 删除Object
     * 
     * @param bucketName
     * @param key
     *//*

    public void deleteObject(String bucketName, String key) {
        try {
            // 备份文件
            // copyObject(bucketName, key);
            getClient().deleteObject(bucketName, key);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    */
/**
     * 备份文件，只支持同区域的BucketName
     * 
     * @param srcBucketName
     *            文件备份前的BucketName
     * @param srcKey
     *            文件备份前的文件路径
     *//*

    public void copyObject(String srcBucketName, String srcKey) {
        boolean found = getClient().doesObjectExist(srcBucketName, srcKey);
        if (found) {
            try {
                String destBucketName = srcBucketName + "-backup";
                StringBuffer destKey = new StringBuffer();
                String[] srcKeyArray = srcKey.split("/");
                for (int i = 0; i < srcKeyArray.length; i++) {
                    if (i == 2) {
                        destKey.append(ToolsKit.Date.format(new Date(), "yyyyMMddHHmmss")).append("/");
                    }
                    destKey.append(srcKeyArray[i]).append("/");
                }
                if (destKey.length() > 1) {
                    destKey.deleteCharAt(destKey.length() - 1);
                }
                getClient().copyObject(srcBucketName, srcKey, destBucketName, destKey.toString());
                System.out.println("##########copy file " + destKey + " to " + destBucketName + " is done!");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    */
/**
     * 判断文件是否存在
     * 
     * @param srcBucketName
     *            bucketName
     * @param srcKey
     *            文件路径
     * @return
     *//*

    public boolean objectExist(String srcBucketName, String srcKey) {
        return getClient().doesObjectExist(srcBucketName, srcKey);
    }

    */
/**
     * 网络流式上传文件
     * 
     * @param sourceUrl
     *            源文件地址
     * @param bucketName
     *            bucketName
     * @param targetUrl
     *            目标地址
     *//*

    public boolean putObjectByStream(String sourceUrl, String bucketName, String targetUrl) {
        boolean isSuccess = false;
        try {
            // 上传网络流。
            URLConnection URLConnection = new URL(sourceUrl).openConnection();
            int contentLength = URLConnection.getContentLength();
            String contentType = URLConnection.getContentType();
            InputStream inputStream = URLConnection.getInputStream();
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(contentType);
            objectMetadata.setContentLength(contentLength);
            getClient().putObject(new PutObjectRequest(bucketName, targetUrl, inputStream, objectMetadata));
            isSuccess = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return isSuccess;
    }
}
*/
