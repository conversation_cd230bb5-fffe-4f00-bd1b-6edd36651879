/*
package net.xplife.system.storage.jd.kit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import net.xplife.system.storage.jd.net.xplife.system.web.core.OSSUtils;
public class OSSKit {
    private static Logger   logger = LoggerFactory.getLogger(OSSKit.class);
    private static OSSUtils ossUtils;

    public static OSSUtils getInstance() {
        try {
            if (ossUtils == null) {
                ossUtils = OSSUtils.getInstance();
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        return ossUtils;
    }
}
*/
