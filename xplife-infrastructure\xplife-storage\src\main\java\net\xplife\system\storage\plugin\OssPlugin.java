package net.xplife.system.storage.plugin;
import net.xplife.system.storage.aliyun.kit.OSSKit;
import net.xplife.system.tools.common.core.IPlugin;
import net.xplife.system.storage.config.OssConfig;
public class OssPlugin implements IPlugin {
    public OssPlugin(String ossAccesskey, String ossAccesskeySecret, String ossEndPoint) {
        OssConfig.getInstance().setOssAccesskey(ossAccesskey);
        OssConfig.getInstance().setOssAccesskeySecret(ossAccesskeySecret);
        OssConfig.getInstance().setOssEndPoint(ossEndPoint);
    }

    @Override
    public void init() throws Exception {
    }

    @Override
    public void start() throws Exception {
        String endPoint = OssConfig.getInstance().getOssEndPoint();
        if (endPoint.indexOf("aliyun") > -1) {
            OSSKit.getInstance().init();
        } else if (endPoint.indexOf("jcloud") > -1 || endPoint.indexOf("jdcloud") > -1) {
            // net.xplife.system.storage.jd.kit.OSSKit.getInstance().init();
        }
    }

    @Override
    public void stop() throws Exception {
    }
}
