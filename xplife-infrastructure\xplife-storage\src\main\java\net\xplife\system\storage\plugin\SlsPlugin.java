package net.xplife.system.storage.plugin;
import net.xplife.system.storage.aliyun.kit.SLSKit;
import net.xplife.system.tools.common.core.IPlugin;
import net.xplife.system.storage.config.SlsConfig;
public class SlsPlugin implements IPlugin {
    public SlsPlugin(String slsAccesskey, String slsAccesskeySecret, String slsEndPoint) {
        SlsConfig.getInstance().setSlsAccesskey(slsAccesskey);
        SlsConfig.getInstance().setSlsAccesskeySecret(slsAccesskeySecret);
        SlsConfig.getInstance().setSlsEndPoint(slsEndPoint);
    }

    @Override
    public void init() throws Exception {
    }

    @Override
    public void start() throws Exception {
        SLSKit.getInstance().init();
    }

    @Override
    public void stop() throws Exception {
    }
}
