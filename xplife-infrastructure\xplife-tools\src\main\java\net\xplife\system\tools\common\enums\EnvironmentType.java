package net.xplife.system.tools.common.enums;
/**
 * 服务器三个环境变量枚举
 * <AUTHOR> 2018年6月19日
 *
 */
public enum EnvironmentType {
    API(1, "api"), 
    OBT(2, "obt-api"), 
    LOCAL(3, "local-api");
    public int    getValue;
    public String domainPrefix;

    public int getGetValue() {
        return getValue;
    }

    public String getDomainPrefix() {
        return domainPrefix;
    }

    EnvironmentType(int getValue, String domainPrefix) {
        this.getValue = getValue;
        this.domainPrefix = domainPrefix;
    }
}
