package net.xplife.system.tools.util.core;
import cn.hutool.core.collection.CollectionUtil;
import java.util.List;
/**
 * Created by brook on 2017/7/7.
 *
 * <AUTHOR>
 */
class CollectionUtils extends CollectionUtil {
    /**
     * 将另一个元素加入到列表中，如果元素不为空
     *
     * @param list
     *            集合元素类型
     * @param list
     *            列表
     * @param element
     *            element 添加的元素
     * @return 此列表
     */
    public static <T> List<T> addIfNotEmpty(List<T> list, T element) {
        if (ToolsKit.isNotEmpty(element)) {
            list.add(element);
        }
        return list;
    }
}
