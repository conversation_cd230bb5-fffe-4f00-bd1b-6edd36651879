package net.xplife.system.tools.util.core;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;
import cn.hutool.core.date.DateUtil;
/**
 * Created by brook on 2017/7/10.
 *
 * <AUTHOR>
 */
class DateUtils extends DateUtil {
    /**
     * ObjectId 转时间
     *
     * @param objectId
     *            mongodb object id
     * @return date
     */
    public static Date dateFromObjectId(String objectId) {
        return new Date(Long.valueOf(objectId.substring(0, 8), 16) * 1000);
    }

    /**
     * 基于时间生成 objectId
     *
     * @param date
     *            时间
     * @return objectId
     */
    public static String objectIdFromDate(Date date) {
        return objectIdFromDate(date.getTime());
    }

    /**
     * 获取yyyy-MM-dd HH:mm:ss格式日期
     * 
     * @param date
     * @return
     */
    public static Date getDateToMin(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取yyyy-MM-dd HH:mm:ss格式日期
     * 
     * @param date
     * @return
     */
    public static Date getDateToMax(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, -1);
        return calendar.getTime();
    }

    /**
     * 基于时间生成 objectId
     *
     * @param time
     *            时间毫秒
     * @return objectId
     */
    public static String objectIdFromDate(long time) {
        return Long.toHexString(time / 1000) + "0000000000000000";
    }

    private static Date getFirstDayOfWeek(Date date, int value) {
        Calendar cal = Calendar.getInstance(java.util.Locale.CHINA);
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, -1); // 解决周日会出现 并到下一周的情况
        cal.set(Calendar.DAY_OF_WEEK, value);
        return cal.getTime();
    }

    /**
     * 获取指定日期是当年第几周,以周一为一周的开始日期，周日为结束日期
     */
    public static int getWeekCountByDate(Date date) {
        Date monday = (getFirstDayOfWeek(date, Calendar.MONDAY)); // 当前日期所在周里周一的日期,由周一开始，周日结束
        Calendar mondayCal = Calendar.getInstance(java.util.Locale.CHINA);
        mondayCal.setTime(monday);
        int dayOfYear = mondayCal.get(Calendar.DAY_OF_YEAR);
        int count = dayOfYear / 7;
        if (dayOfYear % 7 > 0) {
            count++;
        }
        return count;
        // return cal.get(java.util.Calendar.WEEK_OF_YEAR);
        // //2015-12-28日时，计算结果为1，不正确
    }

    public static Date[] getWeekRange(java.util.Date date) {
        int firstDayOfWeek = Calendar.MONDAY;// 以周一算第一天
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.setFirstDayOfWeek(firstDayOfWeek);
        Calendar first = (Calendar) calendar.clone();
        first.add(Calendar.DAY_OF_WEEK, first.getFirstDayOfWeek() - first.get(Calendar.DAY_OF_WEEK));
        // and add six days to the end date
        Calendar last = (Calendar) first.clone();
        last.add(Calendar.DAY_OF_YEAR, 6);
        return new Date[] { beginOfDay(first).getTime(), endOfDay(last).getTime()};
    }

    /**
     * 判断年月是否小于当前年月
     *
     * @param date
     * @return
     */
    public static boolean ltCurrentYearMonth(Date date) {
        return yearMonthCompare(new Date(), date) > 0;
    }

    /**
     * @param current
     * @param compare
     * @return > 大于0 ==等于0 <小于0
     */
    public static int yearMonthCompare(Date current, Date compare) {
        Calendar currentCal = Calendar.getInstance();
        currentCal.setTime(current);
        Calendar compareCal = Calendar.getInstance();
        compareCal.setTime(compare);
        return ((currentCal.get(Calendar.YEAR) * 12 + currentCal.get(Calendar.MONTH)) - (compareCal.get(Calendar.YEAR) * 12 + compareCal.get(Calendar.MONTH)));
    }

    /**
     * 获取时间段内的日期列表
     *
     * @param startDate
     *            开始时间
     * @param endDate
     *            结算时间
     * @return 日期列表
     */
    public static List<Date> getDateList(Date startDate, Date endDate) {
        List<Date> dateList = new ArrayList<Date>();
        dateList.add(startDate);
        Calendar begin = Calendar.getInstance();
        begin.setTime(startDate);
        Calendar end = Calendar.getInstance();
        end.setTime(endDate);
        while (endDate.after(begin.getTime())) {
            begin.add(Calendar.DAY_OF_MONTH, 1);
            dateList.add(begin.getTime());
        }
        return dateList;
    }

    /**
     * 获取时间内的整个月日期列表
     *
     * @param date
     *            开始时间
     * @return 日期列表
     */
    public static List<String> getDateList(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int maxDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        List<String> dateList = new ArrayList<String>();
        for (int i = 1; i <= maxDay; i++) {
            calendar.set(Calendar.DAY_OF_MONTH, i);
            dateList.add(ToolsKit.Date.format(calendar.getTime(), cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN));
        }
        return dateList;
    }

    /**
     * 获取月度星期
     * 
     * @param date
     * @return
     */
    public static int getWeekOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY); // 美国是以周日为每周的第一天 现把周一设成第一天
        calendar.setTime(date);
        return calendar.get(Calendar.WEEK_OF_MONTH);
    }

    /**
     * 1 第一季度 2 第二季度 3 第三季度 4 第四季度
     * 
     * @param date
     * @return
     */
    public static int getSeason(Date date) {
        int season = 0;
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int month = c.get(Calendar.MONTH);
        switch (month) {
        case Calendar.JANUARY:
        case Calendar.FEBRUARY:
        case Calendar.MARCH:
            season = 1;
            break;
        case Calendar.APRIL:
        case Calendar.MAY:
        case Calendar.JUNE:
            season = 2;
            break;
        case Calendar.JULY:
        case Calendar.AUGUST:
        case Calendar.SEPTEMBER:
            season = 3;
            break;
        case Calendar.OCTOBER:
        case Calendar.NOVEMBER:
        case Calendar.DECEMBER:
            season = 4;
            break;
        default:
            break;
        }
        return season;
    }
    /**
     * 提供常用的日期格式化对象
     */
    public static class DatePattern extends cn.hutool.core.date.DatePattern {
    }
    /**
     * 日期格式化工具类
     */
    public static class FastDateFormat extends cn.hutool.core.date.format.FastDateFormat {
        /**
         * 构造
         *
         * @param pattern
         *            使用{@link java.text.SimpleDateFormat} 相同的日期格式
         * @param timeZone
         *            非空时区 {@link TimeZone}
         * @param locale
         *            {@link Locale} 日期地理位置
         * @throws NullPointerException
         *             if pattern, timeZone, or locale is null.
         */
        protected FastDateFormat(java.lang.String pattern, TimeZone timeZone, Locale locale) {
            super(pattern, timeZone, locale);
        }

        /**
         * 构造
         *
         * @param pattern
         *            使用{@link java.text.SimpleDateFormat} 相同的日期格式
         * @param timeZone
         *            非空时区 {@link TimeZone}
         * @param locale
         *            {@link Locale} 日期地理位置
         * @param centuryStart
         *            The start of the 100 year period to use as the "default century" for 2 digit year parsing. If centuryStart is null, defaults to now - 80
         *            years
         * @throws NullPointerException
         *             if pattern, timeZone, or locale is null.
         */
        protected FastDateFormat(java.lang.String pattern, TimeZone timeZone, Locale locale, java.util.Date centuryStart) {
            super(pattern, timeZone, locale, centuryStart);
        }
    }
}
