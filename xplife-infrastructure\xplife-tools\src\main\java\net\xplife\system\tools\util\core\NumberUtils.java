package net.xplife.system.tools.util.core;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import cn.hutool.core.util.NumberUtil;
/**
 * Created by brook on 2017/7/7.
 *
 * <AUTHOR>
 */
class NumberUtils extends NumberUtil {
    /**
     * 获取随机数--最大值比结束随机数少1
     *
     * @param startNum
     *            开始随机数
     * @param endNum
     *            结束随机数
     * @return 随机数
     */
    public static int getRandomNum(int startNum, int endNum) {
        return (int) (Math.random() * (endNum - startNum) + startNum);
    }

    /**
     * 处理对象字段精度问题
     *
     * @param bean
     *            处理对象
     * @param gteDecimals
     *            处理最小小数长度
     * @param scale
     *            保留小数位数
     * @param roundingMode
     *            保留小数的模式 {@link RoundingMode}
     */
    public static void fixObjectFieldDoubleDecimals(Object bean, int gteDecimals, final int scale, final RoundingMode roundingMode) {
        Map<String, Object> update = new HashMap<>();
        Map<String, Object> fieldMap = ToolsKit.Bean.beanToMap(bean);
        for (Map.Entry<String, Object> entry : fieldMap.entrySet()) {
            if (entry.getValue() != null && entry.getValue() instanceof Double) {
                String valueStr = String.valueOf(entry.getValue());
                if (valueStr.matches("\\d+\\.\\d{" + gteDecimals + ",}")) {
                    double value = ToolsKit.Number.round((double) entry.getValue(), scale, roundingMode).doubleValue();
                    update.put(entry.getKey(), value);
                }
            }
        }
        if (ToolsKit.isNotEmpty(update)) {
            ToolsKit.Bean.fillBeanWithMap(update, bean, false);
        }
    }

    /**
     * 转成保留N位小数
     *
     * @param value
     *            需要转换的值
     * @return 保留两位小数的值
     */
    public static double parseDecimal(double value, double position) {
        return ((int) (value * position)) / position;
    }

    /**
     * 获取一定长度的随机字符串
     *
     * @param length
     *            指定字符串长度
     * @return 一定长度的字符串
     */
    public static String getRandomStringByLength(int length) {
        String base = "0123456789abcdefghijklmnopqrstuvwxyz";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }
}
