package net.xplife.system.tools.util.core;
import cn.hutool.core.util.RandomUtil;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
/**
 * Created by brook on 2017/7/7.
 *
 * <AUTHOR>
 */
class RandomUtils extends RandomUtil {
    /**
     * 随机获得列表中的一定量元素
     *
     * @param <T>
     *            元素类型
     * @param list
     *            列表
     * @param count
     *            随机取出的个数
     * @return 随机元素
     */
    public static <T> List<T> randomUniqueEles(List<T> list, int count) {
        final List<T> result = new ArrayList<T>(count);
        int limit = list.size();
        if (limit <= count) {
            result.addAll(list);
            return result;
        }
        Set<Integer> indexSet = new HashSet<>(count);
        while (count > 0) {
            int index = randomInt(limit);
            if (!indexSet.contains(index)) {
                indexSet.add(index);
                count--;
            }
        }
        for (Integer index : indexSet) {
            result.add(list.get(index));
        }
        return result;
    }
}
