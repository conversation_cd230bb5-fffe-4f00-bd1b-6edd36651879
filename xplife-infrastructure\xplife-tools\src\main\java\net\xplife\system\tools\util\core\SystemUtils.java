package net.xplife.system.tools.util.core;
import cn.hutool.system.SystemUtil;
/**
 * Created by brook on 2017/11/2.
 *
 * <AUTHOR>
 */
class SystemUtils extends SystemUtil {
    /**
     * 获取主机ip和当前进程pid
     *
     * @return ip=***********&pid=123
     */
    public static String getLocalhostAndPid() {
        return "ip=" + ToolsKit.Net.getLocalhostStr() + "&pid=" + ToolsKit.System.getCurrentPID();
    }
}
