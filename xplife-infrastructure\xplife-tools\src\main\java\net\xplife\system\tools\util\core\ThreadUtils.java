package net.xplife.system.tools.util.core;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import net.xplife.system.tools.util.core.concurrent.CompletableFuture;
import cn.hutool.core.thread.ThreadUtil;
/**
 * Created by brook on 2017/11/24.
 *
 * <AUTHOR>
 */
class ThreadUtils extends ThreadUtil {
    /**
     * 默认超时秒数
     */
    public static final int  DEFAULT_TIMEOUT_SECONDS = 3;
    /**
     * 默认使用线程数
     */
    public static final byte DEFAULT_USE_MAX_THREAD  = 32;

    /**
     * 批量处理异步结果
     *
     * @param futures
     * @param <T>
     * @return
     * @throws InterruptedException
     * @throws ExecutionException
     * @throws TimeoutException
     */
    public static <T> List<T> allOf(Collection<Future<T>> futures) throws InterruptedException, ExecutionException, TimeoutException {
        List<T> result = new ArrayList<>(futures.size());
        for (Future<T> future : futures) {
            T t = future.get(DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (t != null) {
                result.add(t);
            }
        }
        return result;
    }

    /**
     * 批量处理异步结果
     *
     * @param collection
     *            集合
     * @param completableFuture
     *            future
     * @param maxThread
     *            最大线程
     * @param timeout
     *            超时时间(每个线程的超时)
     * @param unit
     *            超时时间单位
     * @param <K>
     * @param <V>
     * @return
     * @throws InterruptedException
     * @throws ExecutionException
     * @throws TimeoutException
     */
    public static <K, V> List<V> allOf(Collection<K> collection, CompletableFuture<K, Callable<V>> completableFuture, byte maxThread, long timeout,
            TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        List<V> result = new ArrayList<>();
        for (List<K> page : ToolsKit.Page.listToPage(collection, maxThread)) {
            List<Future<V>> futureList = new ArrayList<>();
            for (K key : page) {
                futureList.add(execAsync(completableFuture.getCallable(key)));
            }
            for (Future<V> future : futureList) {
                V v = future.get(timeout, unit);
                if (v != null) {
                    result.add(v);
                }
            }
        }
        return result;
    }

    /**
     * 批量处理异步结果
     *
     * @param collection
     * @param completableFuture
     * @param timeout
     * @param unit
     * @param <K>
     * @param <V>
     * @return
     * @throws InterruptedException
     * @throws ExecutionException
     * @throws TimeoutException
     */
    public static <K, V> List<V> allOf(Collection<K> collection, CompletableFuture<K, Callable<V>> completableFuture, long timeout, TimeUnit unit)
            throws InterruptedException, ExecutionException, TimeoutException {
        return allOf(collection, completableFuture, DEFAULT_USE_MAX_THREAD, timeout, unit);
    }

    /**
     * 批量处理异步结果
     *
     * @param collection
     * @param completableFuture
     * @param <K>
     * @param <V>
     * @return
     * @throws InterruptedException
     * @throws ExecutionException
     * @throws TimeoutException
     */
    public static <K, V> List<V> allOf(Collection<K> collection, CompletableFuture<K, Callable<V>> completableFuture)
            throws InterruptedException, ExecutionException, TimeoutException {
        return allOf(collection, completableFuture, DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS);
    }
}
