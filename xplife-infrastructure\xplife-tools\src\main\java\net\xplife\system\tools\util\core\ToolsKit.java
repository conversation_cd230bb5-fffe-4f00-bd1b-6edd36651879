package net.xplife.system.tools.util.core;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import net.xplife.system.tools.common.core.ToolsConst;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ClassLoaderUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.HashUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.NetUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.TypeUtil;
import cn.hutool.core.util.ZipUtil;
/**
 * Created by brook on 2017/6/30. duang 工具类公共入口
 *
 * <AUTHOR>
 */
public final class ToolsKit {
    private static final java.lang.String CREATETIME_FIELD     = "createtime";
    private static final java.lang.String CREATEUSERID_FIELD   = "createuserid";
    private static final java.lang.String UPDATETIME_FIELD     = "updatetime";
    private static final java.lang.String UPDATEUSERID_FIELD   = "updateuserid";
    private static final java.lang.String STATUS_FIELD         = "status";
    private static final java.lang.String SOURCE_FIELD         = "source";
    private static final java.lang.String STATUS_FIELD_SUCCESS = "审核通过";

    private ToolsKit() {
    }
    /**
     * 字符串工具类
     */
    public static class String extends StringUtils {
    }
    /**
     * 数组工具类入口
     */
    public static class Array extends ArrayUtils {
    }
    /**
     * 集合相关工具类，包括数组
     */
    public static class Collection extends CollectionUtils {
    }
    /**
     * 转义和反转义工具类Escape / Unescape<br>
     * escape采用ISO Latin字符集对指定的字符串进行编码。<br>
     * 所有的空格符、标点符号、特殊字符以及其他非ASCII字符都将被转化成%xx格式的字符编码(xx等于该字符在字符集表里面的编码的16进制数字)。
     */
    public static class Escape extends EscapeUtils {
    }
    /**
     * 数字工具类<br>
     * 对于精确值计算应该使用 {@link BigDecimal}<br>
     * JDK7中<strong>BigDecimal(double val)</strong>构造方法的结果有一定的不可预知性，例如：
     * <p>
     * 
     * <pre>
     * new BigDecimal(0.1)
     * </pre>
     * <p>
     * 表示的不是<strong>0.1</strong>而是<strong>0.1000000000000000055511151231257827021181583404541015625</strong>
     * <p>
     * 这是因为0.1无法准确的表示为double。因此应该使用<strong>new BigDecimal(String)</strong>。
     * </p>
     * 相关介绍：
     * <ul>
     * <li>http://www.oschina.net/code/snippet_563112_25237</li>
     * <li>https://github.com/venusdrogon/feilong-core/wiki/one-jdk7-bug-thinking</li>
     * </ul>
     */
    public static class Number extends NumberUtils {
    }
    /**
     * 随机工具类
     *
     * <AUTHOR>
     */
    public static class Random extends RandomUtils {
    }
    /**
     * Hash算法大全<br>
     * 推荐使用FNV1算法
     */
    public static class Hash extends HashUtil {
    }
    /**
     * IO工具类<br>
     * IO工具类只是辅助流的读写，并不负责关闭流。原因是流可能被多次读写，读写关闭后容易造成问题。
     */
    public static class IO extends IOUtils {
    }
    /**
     * 文件工具类
     */
    public static class File extends FileUtil {
    }
    /**
     * 文件类型判断工具类
     */
    // public static class FileType extends FileTypeUtil {
    //
    // }
    /**
     * 正则相关工具类
     */
    public static class Regex extends ReUtil {
    }
    /**
     * 线程池工具
     */
    public static class Thread extends ThreadUtils {
    }
    /**
     * 类工具类
     */
    public static class Class extends ClassUtil {
    }
    /**
     * 压缩工具类
     */
    public static class Zip extends ZipUtil {
    }
    /**
     * 对象一些通用的函数
     */
    public static class Object extends ObjectUtils {
    }
    /**
     * 分页工具类
     */
    public static class Page extends PageUtils {
    }
    /**
     * 网络相关工具
     */
    public static class Net extends NetUtil {
    }
    /**
     * 统一资源定位符相关工具类
     */
    public static class URL extends URLUtils {
    }
    /**
     * 字符集工具类
     */
    public static class Charset extends CharsetUtil {
    }
    /**
     * Bean工具类
     */
    public static class Bean extends BeanUtil {
    }
    /**
     * 常用公式工具类
     */
    public static class Formula extends FormulaUtils {
    }
    /**
     * 日期工具类入口
     */
    public static class Date extends DateUtils {
    }
    /**
     * script脚本工具类
     */
    public static class Script extends ScriptUtils {
    }
    /**
     * System脚本工具类
     */
    public static class System extends SystemUtils {
    }
    /**
     * 反射工具类
     */
    public static class Reflect extends ReflectUtil {
    }
    /**
     * {@link java.lang.ClassLoader}工具类
     */
    public static class ClassLoader extends ClassLoaderUtil {
    }
    /**
     * 十六进制（简写为hex或下标16）在数学中是一种逢16进1的进位制，一般用数字0到9和字母A到F表示（其中:A~F即10~15）。<br>
     * 例如十进制数57，在二进制写作111001，在16进制写作39。<br>
     * 像java,c这样的语言为了区分十六进制和十进制数值,会在十六进制数的前面加上 0x,比如0x20是十进制的32,而不是十进制的20<br>
     * <p>
     * 参考：https://my.oschina.net/xinxingegeya/blog/287476
     *
     * <AUTHOR>
     */
    public static class Hex extends HexUtil {
    }
    /**
     * 身份证相关工具类<br>
     * see https://www.oschina.net/code/snippet_1611_2881
     *
     * <AUTHOR>
     * @since 3.0.4
     */
    public static class Idcard extends IdcardUtil {
    }
    /**
     * 图片处理工具类：<br>
     * 功能：缩放图像、切割图像、旋转、图像类型转换、彩色转黑白、文字水印、图片水印等 <br>
     * 参考：http://blog.csdn.net/zhangzhikaixinya/article/details/8459400
     *
     * <AUTHOR>
     */
    public static class Image extends ImageUtils {
    }
    /**
     * 系统运行时工具类，用于执行系统命令的工具
     *
     * <AUTHOR>
     * @since 3.1.1
     */
    public static class Runtime extends RuntimeUtil {
    }
    /**
     * 枚举工具类
     *
     * <AUTHOR>
     * @since 3.3.0
     */
    public static class Enum extends EnumUtil {
    }
    /**
     * 针对 {@link java.lang.reflect.Type} 的工具类封装<br>
     * 最主要功能包括：
     * <p>
     * 
     * <pre>
     * 1. 获取方法的参数和返回值类型（包括Type和Class）
     * 2. 获取泛型参数类型（包括对象的泛型参数或集合元素的泛型类型）
     * </pre>
     *
     * <AUTHOR>
     * @since 3.0.8
     */
    public static class Type extends TypeUtil {
    }

    /**
     * 是否包含空字符串
     *
     * @param elements
     *            字符串列表
     * @return 是否包含空字符串
     */
    public static boolean hasEmpty(java.lang.Object... elements) {
        if (elements == null || elements.length == 0) {
            return true;
        }
        for (java.lang.Object element : elements) {
            if (isEmpty(element)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 安全相关工具类
     */
    // public static class Secure extends SecureUtil {
    //
    // }
    /***
     * 判断传入的对象是否为空
     *
     * @param obj
     *            待检查的对象
     * @return 返回的布尔值, 为空或等于0时返回true
     */
    public static boolean isEmpty(java.lang.Object obj) {
        return checkObjectIsEmpty(obj, true);
    }

    /***
     * 判断传入的对象是否不为空
     *
     * @param obj
     *            待检查的对象
     * @return 返回的布尔值, 不为空或不等于0时返回true
     */
    public static boolean isNotEmpty(java.lang.Object obj) {
        return checkObjectIsEmpty(obj, false);
    }

    @SuppressWarnings("rawtypes")
    private static boolean checkObjectIsEmpty(java.lang.Object obj, boolean bool) {
        if (null == obj) {
            return bool;
        } else if (obj == "" || "".equals(obj)) {
            return bool;
        } else if (obj instanceof java.lang.Integer || obj instanceof java.lang.Long || obj instanceof java.lang.Double) {
            try {
                java.lang.Double.parseDouble(obj + "");
            } catch (Exception e) {
                return bool;
            }
        } else if (obj instanceof java.lang.String) {
            if (((java.lang.String) obj).length() <= 0) {
                return bool;
            }
            if ("null".equalsIgnoreCase(obj + "")) {
                return bool;
            }
        } else if (obj instanceof java.util.Map) {
            if (((Map) obj).size() == 0) {
                return bool;
            }
        } else if (obj instanceof java.util.Collection) {
            if (((java.util.Collection) obj).size() == 0) {
                return bool;
            }
        } else if (obj instanceof java.lang.Object[]) {
            if (((java.lang.Object[]) obj).length == 0) {
                return bool;
            }
        }
        return !bool;
    }

    /***
     * 新增更新记录时，添加基本数据到对象中
     *
     * @param obj
     *            需要反射的对象
     * @param userId
     *            创建人ID
     */
    public static void setIdEntityData(java.lang.Object obj, java.lang.String userId) {
        if (isNotEmpty(obj)) {
            Map<java.lang.String, java.lang.Object> fields = new HashMap<>(8);
            java.util.Date now = new java.util.Date();
            java.lang.Object createUserId;
            try {
                createUserId = obj.getClass().getMethod("getCreateuserid").invoke(obj);
            } catch (Exception e) {
                e.printStackTrace();
                throw new IllegalArgumentException(e);
            }
            if (isEmpty(createUserId)) {
                fields.put(CREATEUSERID_FIELD, userId);
                fields.put(CREATETIME_FIELD, now);
                fields.put(STATUS_FIELD, STATUS_FIELD_SUCCESS);
                fields.put(SOURCE_FIELD, "phone");
            }
            fields.put(UPDATETIME_FIELD, now);
            fields.put(UPDATEUSERID_FIELD, userId);
            ToolsKit.Bean.fillBeanWithMap(fields, obj, false);
        }
    }

    /**
     * 取默认的Mobile
     *
     * @return
     */
    public static java.lang.String getDefaultMobile() {
        java.lang.String uuid = getUUID();
        return "sa_" + uuid;
    }

    public static java.lang.String getUUID() {
        return UUID.randomUUID().toString().replace("-", "").toLowerCase();
    }

    /**
     * 转成保留N位小数
     *
     * @param value
     *            需要转换的值
     * @return 保留两位小数的值
     */
    public static double parseDecimal(double value, double position) {
        return ((int) (value * position)) / position;
    }

    /**
     * 根据user agent判断设备类型 0.ANDROID 1.IOS
     *
     * @param userAgent
     * @return
     */
    public static int getDeviceTypeByUserAgent(java.lang.String userAgent) {
        if (isNotEmpty(userAgent)) {
            if (userAgent.contains("iOS") || userAgent.contains("iPhone") || userAgent.contains("iPad")) {
                return 1;
            } else if (userAgent.contains("Android")) {
                return 0;
            }
        }
        return -1;
    }

    /**
     * 中文转数字男女
     * 
     * @param sex
     * @return
     */
    public static int getSex(java.lang.String sex) {
        if (java.lang.String.valueOf(ToolsConst.STATUS_0).equals(sex) || java.lang.String.valueOf(ToolsConst.STATUS_1).equals(sex)) {
            return Integer.parseInt(sex);
        }
        if (ToolsConst.DEFAULT_M_SEX.equals(sex)) {
            return ToolsConst.STATUS_1;
        } else {
            return ToolsConst.STATUS_0;
        }
    }

    /**
     * 获取中文男女
     * 
     * @param sex
     * @return
     */
    public static java.lang.String getSexStr(java.lang.String sex) {
        if (ToolsConst.DEFAULT_W_SEX.equals(sex) || ToolsConst.DEFAULT_M_SEX.equals(sex)) {
            return sex;
        }
        if (java.lang.String.valueOf(ToolsConst.STATUS_1).equals(sex)) {
            return ToolsConst.DEFAULT_M_SEX;
        } else {
            return ToolsConst.DEFAULT_W_SEX;
        }
    }
}
