package net.xplife.system.web.common;
public class WebConst {
    /**
     * 默认的编码格式字符串
     */
    public static final String ENCODING_FIELD          = "UTF-8";
    /** RFC 1945 (HTTP/1.0) Section 10.5, RFC 2616 (HTTP/1.1) Section 14.17 */
    public static final String CONTENT_TYPE            = "Content-Type";
    /**
     * 请求ID key
     */
    public final static String SADAIS_REQUEST_ID_FIELD = "sadais_request_id";
    /**
     * 请求ID前缀
     */
    public final static String SADAIS_FIELD            = "sadais_";
    public final static String FORWARDED_PROTO         = "X-Forwarded-Proto";
    public static final String FORWARDED_FOR           = "X-Forwarded-For";
    public static final String REAL_IP                 = "X-Real-IP";
    /**
     * 客户端返回的json格式数据
     */
    public static final String JSON_POST_DATA          = "json_post_data";
    /**
     * 提交请求头信息
     */
    public static final String HEAD_INFO_DATA          = "head_info_data";
    /**
     * jwt密钥
     */
    public static final String JWT_SECERT              = "jgqfr8thqf4m4g4g77nt1o8hux5v7ul7dfqasx39nhkk96j1bmb5k9o6qet96eqcs36s6m";
    /**
     * api jwttoken有效时间-30分钟(修改成14天)
     */
//    public static final long   JWT_API_TTL             = 30 * 60 * 1000;
    public static final long   JWT_API_TTL             = 60 * 60 * 1000 * 24 * 14;
    /**
     * platform jwttoken有效时间-8小时
     */
    public static final long   JWT_PLATFORM_TTL        = 8 * 60 * 60 * 1000;
    /**
     * net.xplife.system.web.jwt subject对象
     */
    public static final String JWT_SUBJECT             = "JWT_SUBJECT";
    /**
     * net.xplife.system.web.jwt 用户信息对象
     */
    public static final String JWT_USER_INFO           = "JWT_USER_INFO";
    /**
     * 淘宝根据IP获取城市信息地址
     */
    public static final String TAOBAO_GETIP_URL        = "http://ip.taobao.com/service/getIpInfo2.php";
    /**
     * net.xplife.system.web.jwt token参数key
     */
    public static final String JWT_TOKEN_PARAM_KEY     = "jwttoken";
    /**
     * feign flag参数key
     */
    public static final String FEIGN_FLAG_KEY          = "feignflag";
    /**
     * 事件日志服务名称
     */
    public static final String SADAIS_SERVICE_EVENT    = "yoyin-event";
    /**
     * api日志采集uri
     */
    public static final String EVENT_LOG_API_URI       = "/event/v1/log/saveapilog";
    /**
     * 消息日志类型--请求
     */
    public static final String QUEUE_MSG_TYPE_REQUEST  = "REQUEST";
    /**
     * 消息日志类型--响应
     */
    public static final String QUEUE_MSG_TYPE_RESPONSE = "RESPONSE";
    /**
     * 列表默认初始化数量
     */
    public static final int    DEFAULT_LIST_INT_COUNT  = 500;
    /**
     * 请求来源--手机
     */
    public static final String REQUEST_SOURCE_PHONE    = "phone";
    /**
     * 请求来源--内部请求
     */
    public static final String REQUEST_SOURCE_FEIGN    = "feign";
    /***
     * appName，sadais-agent的请求头第一个参数值，A4版本
     */
    public static final String APP_NAME_A4 = "YOYIN4APRINTER";
    /***
     * appName，sadais-agent的请求头第一个参数值，普通版本
     */
    public static final String APP_NAME = "YOYINPRINTER";
}
