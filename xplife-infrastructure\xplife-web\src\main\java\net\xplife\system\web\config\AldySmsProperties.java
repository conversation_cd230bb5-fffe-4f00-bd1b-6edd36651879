package net.xplife.system.web.config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
@Component
public class AldySmsProperties {
    @Value("${regionId:}")
    private String regionId;       // 节点：cn-hangzhou
    @Value("${accessKey:}")
    private String accessKey;      // 密匙
    @Value("${accessKeySecret:}")
    private String accessKeySecret;// 密钥
    @Value("${endpointName:}")
    private String endpointName;   // 节点:cn-hangzhou
    @Value("${product:}")
    private String product;        // 产品：Dysmsapi
    @Value("${domain:}")
    private String domain;         // 域：dysmsapi.aliyuncs.com
    @Value("${maxRecNum:0}")
    private int    maxRecNum;      // 单次发送短信上线
    @Value("${signName:}")
    private String signName;       // 签名
    @Value("${charEncode:}")
    private String charEncode;     // 编码
    @Value("${smsTemplateUrl:}")
    private String smsTemplateUrl; // 短信模板地址

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public String getEndpointName() {
        return endpointName;
    }

    public void setEndpointName(String endpointName) {
        this.endpointName = endpointName;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public int getMaxRecNum() {
        return maxRecNum;
    }

    public void setMaxRecNum(int maxRecNum) {
        this.maxRecNum = maxRecNum;
    }

    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName;
    }

    public String getCharEncode() {
        return charEncode;
    }

    public void setCharEncode(String charEncode) {
        this.charEncode = charEncode;
    }

    public String getSmsTemplateUrl() {
        return smsTemplateUrl;
    }

    public void setSmsTemplateUrl(String smsTemplateUrl) {
        this.smsTemplateUrl = smsTemplateUrl;
    }
}