package net.xplife.system.web.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class CommonProperties {

    @Value("${spring.application.name}")
    private String  applicationName;    // 服务名称
    @Value("${server.port}")
    private int     port;               // 服务端口
    @Value("${codeIdNum:0}")
    private int     codeIdNum;          // 生成codeid缓存随机数
    @Value("${event.log.enable:false}")
    private boolean eventLogEnable;     // 是否开启收集日志
    @Value("${analysis.log.enable:false}")
    private boolean analysisLogEnable;  // 是否开启收集统计
    @Value("${user.token.enable:false}")
    private boolean userTokenEnable;    // 是否验证token令牌
    @Value("${apiDomain:}")
    private String  apiDomain;          // api接口域名
    @Value("${fileDomain:}")
    private String  fileDomain;         // 静态文件域名
    @Value("${shareDomain:}")
    private String  shareDomain;        // 分享域名
    @Value("${h5Env:}")
    private String  h5Env;              // h5环境前缀
    @Value("${userDefaultPic:}")
    private String  userDefaultPic;     // 默认头像地址
    @Value("${kafka.product.enable:false}")
    private boolean kafkaProductEnable; // 是否开启kafka生产者
    @Value("${kafka.consumer.enable:false}")
    private boolean kafkaConsumerEnable;// 是否开启kafka消费者
    @Value("${defaultName:}")
    private String  defaultName;        // 默认名称
    @Value("${androidQQAppId:}")
    private String  androidQQAppId;     // 安卓QQ授权ID
    @Value("${androidQQAppKey:}")
    private String  androidQQAppKey;    // 安卓QQ授权密匙
    @Value("${iosQQAppId:}")
    private String  iosQQAppId;         // IOS QQ授权ID
    @Value("${iosQQAppKey:}")
    private String  iosQQAppKey;        // IOS QQ授权密匙
    @Value("${wxAppId:}")
    private String  wxAppId;            // 微信授权ID
    @Value("${wxAppSecret:}")
    private String  wxAppSecret;        // 微信授权密匙
    @Value("${h5WxAppId:}")
    private String  h5WxAppId;          // 微信H5授权ID
    @Value("${h5WxAppSecret:}")
    private String  h5WxAppSecret;      // 微信H5授权密匙
    @Value("${h5QqAppId:}")
    private String  h5QqAppId;          // QQ H5授权ID
    @Value("${h5QqAppSecret:}")
    private String  h5QqAppSecret;      // QQ H5授权密匙
    @Value("${cryptionKey:vvwpc7sl0oe7o48alvsbyf2iowowha7qejhoqh99g2bzl21v76fyt22f9ux862k14nv8ze}")
    private String  cryptionKey;        // 加密密匙
    @Value("${maWxAppId:}")
    private String  maWxAppId;          // 微信小程序授权ID
    @Value("${maWxAppSecret:}")
    private String  maWxAppSecret;      // 微信小程序授权密匙'

    @Value("${ymOneKeyAlyAppId:}")
    private String ymOneKeyAlyAppId;    // 友盟一键登录的阿里云appId
    @Value("${ymOneKeyAlyAppSercet:}")
    private String ymOneKeyAlyAppSercet;// 友盟一键登录的阿里云appSercet

    @Value("${ymOneKeyAndroidAppId:60a3737cc9aacd3bd4da3950}")
    private String ymOneKeyAndroidAppId;// 友盟的应用中，安卓的appId，短信一键登录
    @Value("${ymOneKeyIosAppId:61415025314602341a130915}")
    private String ymOneKeyIosAppId;    // 友盟的应用中，ios的appId，短信一键登录

    @Value("${ymOneKeyAndroidAppIdA4:6110c919063bed4d8c107421}")
    private String ymOneKeyAndroidAppIdA4;// 友盟的应用中，a4版本安卓的appId，短信一键登录
    @Value("${ymOneKeyIosAppIdA4:613ac26b517ed7102047a591}")
    private String ymOneKeyIosAppIdA4;    // 友盟的应用中，a4版本ios的appId，短信一键登录

    @Value("${jyeooOrcSearchUrl:http://api.jyeoo.com/v1/counter/ocrquessearch}")
    private String jyeooOrcSearchUrl;    // 友盟的应用中，a4版本ios的appId，短信一键登录


    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getH5QqAppId() {
        return h5QqAppId;
    }

    public void setH5QqAppId(String h5QqAppId) {
        this.h5QqAppId = h5QqAppId;
    }

    public String getH5QqAppSecret() {
        return h5QqAppSecret;
    }

    public void setH5QqAppSecret(String h5QqAppSecret) {
        this.h5QqAppSecret = h5QqAppSecret;
    }

    public String getH5WxAppId() {
        return h5WxAppId;
    }

    public void setH5WxAppId(String h5WxAppId) {
        this.h5WxAppId = h5WxAppId;
    }

    public String getH5WxAppSecret() {
        return h5WxAppSecret;
    }

    public void setH5WxAppSecret(String h5WxAppSecret) {
        this.h5WxAppSecret = h5WxAppSecret;
    }

    public String getAndroidQQAppId() {
        return androidQQAppId;
    }

    public void setAndroidQQAppId(String androidQQAppId) {
        this.androidQQAppId = androidQQAppId;
    }

    public String getAndroidQQAppKey() {
        return androidQQAppKey;
    }

    public void setAndroidQQAppKey(String androidQQAppKey) {
        this.androidQQAppKey = androidQQAppKey;
    }

    public String getIosQQAppId() {
        return iosQQAppId;
    }

    public void setIosQQAppId(String iosQQAppId) {
        this.iosQQAppId = iosQQAppId;
    }

    public String getIosQQAppKey() {
        return iosQQAppKey;
    }

    public void setIosQQAppKey(String iosQQAppKey) {
        this.iosQQAppKey = iosQQAppKey;
    }

    public String getDefaultName() {
        return defaultName;
    }

    public void setDefaultName(String defaultName) {
        this.defaultName = defaultName;
    }

    public int getCodeIdNum() {
        return codeIdNum;
    }

    public void setCodeIdNum(int codeIdNum) {
        this.codeIdNum = codeIdNum;
    }

    public String getUserDefaultPic() {
        return userDefaultPic;
    }

    public void setUserDefaultPic(String userDefaultPic) {
        this.userDefaultPic = userDefaultPic;
    }

    public boolean getKafkaConsumerEnable() {
        return kafkaConsumerEnable;
    }

    public void setKafkaConsumerEnable(boolean kafkaConsumerEnable) {
        this.kafkaConsumerEnable = kafkaConsumerEnable;
    }

    public boolean getKafkaProductEnable() {
        return kafkaProductEnable;
    }

    public void setKafkaProductEnable(boolean kafkaProductEnable) {
        this.kafkaProductEnable = kafkaProductEnable;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public boolean getEventLogEnable() {
        return eventLogEnable;
    }

    public void setEventLogEnable(boolean eventLogEnable) {
        this.eventLogEnable = eventLogEnable;
    }

    public boolean getAnalysisLogEnable() {
        return analysisLogEnable;
    }

    public void setAnalysisLogEnable(boolean analysisLogEnable) {
        this.analysisLogEnable = analysisLogEnable;
    }

    public boolean getUserTokenEnable() {
        return userTokenEnable;
    }

    public void setUserTokenEnable(boolean userTokenEnable) {
        this.userTokenEnable = userTokenEnable;
    }

    public String getApiDomain() {
        return apiDomain;
    }

    public void setApiDomain(String apiDomain) {
        this.apiDomain = apiDomain;
    }

    public String getFileDomain() {
        return fileDomain;
    }

    public void setFileDomain(String fileDomain) {
        this.fileDomain = fileDomain;
    }

    public String getWxAppId() {
        return wxAppId;
    }

    public void setWxAppId(String wxAppId) {
        this.wxAppId = wxAppId;
    }

    public String getWxAppSecret() {
        return wxAppSecret;
    }

    public void setWxAppSecret(String wxAppSecret) {
        this.wxAppSecret = wxAppSecret;
    }

    public String getShareDomain() {
        return shareDomain;
    }

    public void setShareDomain(String shareDomain) {
        this.shareDomain = shareDomain;
    }

    public String getH5Env() {
        return h5Env;
    }

    public void setH5Env(String h5Env) {
        this.h5Env = h5Env;
    }

    public String getCryptionKey() {
        return cryptionKey;
    }

    public void setCryptionKey(String cryptionKey) {
        this.cryptionKey = cryptionKey;
    }

    public String getMaWxAppId() {
        return maWxAppId;
    }

    public void setMaWxAppId(String maWxAppId) {
        this.maWxAppId = maWxAppId;
    }

    public String getMaWxAppSecret() {
        return maWxAppSecret;
    }

    public void setMaWxAppSecret(String maWxAppSecret) {
        this.maWxAppSecret = maWxAppSecret;
    }

    public String getYmOneKeyAlyAppId() {
        return ymOneKeyAlyAppId;
    }

    public void setYmOneKeyAlyAppId(String ymOneKeyAlyAppId) {
        this.ymOneKeyAlyAppId = ymOneKeyAlyAppId;
    }

    public String getYmOneKeyAlyAppSercet() {
        return ymOneKeyAlyAppSercet;
    }

    public void setYmOneKeyAlyAppSercet(String ymOneKeyAlyAppSercet) {
        this.ymOneKeyAlyAppSercet = ymOneKeyAlyAppSercet;
    }

    public String getYmOneKeyAndroidAppId() {
        return ymOneKeyAndroidAppId;
    }

    public void setYmOneKeyAndroidAppId(String ymOneKeyAndroidAppId) {
        this.ymOneKeyAndroidAppId = ymOneKeyAndroidAppId;
    }

    public String getYmOneKeyIosAppId() {
        return ymOneKeyIosAppId;
    }

    public void setYmOneKeyIosAppId(String ymOneKeyIosAppId) {
        this.ymOneKeyIosAppId = ymOneKeyIosAppId;
    }

    public String getYmOneKeyAndroidAppIdA4() {
        return ymOneKeyAndroidAppIdA4;
    }

    public void setYmOneKeyAndroidAppIdA4(String ymOneKeyAndroidAppIdA4) {
        this.ymOneKeyAndroidAppIdA4 = ymOneKeyAndroidAppIdA4;
    }

    public String getYmOneKeyIosAppIdA4() {
        return ymOneKeyIosAppIdA4;
    }

    public void setYmOneKeyIosAppIdA4(String ymOneKeyIosAppIdA4) {
        this.ymOneKeyIosAppIdA4 = ymOneKeyIosAppIdA4;
    }

    public String getJyeooOrcSearchUrl() {
        return jyeooOrcSearchUrl;
    }

    public void setJyeooOrcSearchUrl(String jyeooOrcSearchUrl) {
        this.jyeooOrcSearchUrl = jyeooOrcSearchUrl;
    }
}