package net.xplife.system.web.config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
@Component
public class OpenSearchProperties {
    @Value("${osAppId:}")
    private String osAppId;
    @Value("${osAccesskey:}")
    private String osAccesskey;
    @Value("${osAccesskeySecret:}")
    private String osAccesskeySecret;
    @Value("${osHost:}")
    private String osHost;

    public String getOsAppId() {
        return osAppId;
    }

    public void setOsAppId(String osAppId) {
        this.osAppId = osAppId;
    }

    public String getOsAccesskey() {
        return osAccesskey;
    }

    public void setOsAccesskey(String osAccesskey) {
        this.osAccesskey = osAccesskey;
    }

    public String getOsAccesskeySecret() {
        return osAccesskeySecret;
    }

    public void setOsAccesskeySecret(String osAccesskeySecret) {
        this.osAccesskeySecret = osAccesskeySecret;
    }

    public String getOsHost() {
        return osHost;
    }

    public void setOsHost(String osHost) {
        this.osHost = osHost;
    }
}