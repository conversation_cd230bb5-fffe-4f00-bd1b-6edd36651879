package net.xplife.system.web.config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
@Component
public class OssProperties {
    @Value("${ossAccesskey:}")
    private String ossAccesskey;
    @Value("${ossAccesskeySecret:}")
    private String ossAccesskeySecret;
    @Value("${ossEndPoint:}")
    private String ossEndPoint;
    @Value("${ossBucketName:}")
    private String ossBucketName;

    public String getOssAccesskey() {
        return ossAccesskey;
    }

    public void setOssAccesskey(String ossAccesskey) {
        this.ossAccesskey = ossAccesskey;
    }

    public String getOssAccesskeySecret() {
        return ossAccesskeySecret;
    }

    public void setOssAccesskeySecret(String ossAccesskeySecret) {
        this.ossAccesskeySecret = ossAccesskeySecret;
    }

    public String getOssEndPoint() {
        return ossEndPoint;
    }

    public void setOssEndPoint(String ossEndPoint) {
        this.ossEndPoint = ossEndPoint;
    }

    public String getOssBucketName() {
        return ossBucketName;
    }

    public void setOssBucketName(String ossBucketName) {
        this.ossBucketName = ossBucketName;
    }
}