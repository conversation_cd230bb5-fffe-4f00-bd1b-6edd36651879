package net.xplife.system.web.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class RedisProperties {

    @Value("${redisHost:}")
    private String redisHost;
    @Value("${redisPort:0}")
    private int redisPort;
    @Value("${redisPassword:}")
    private String redisPassword;
    @Value("${redisDatabase:}")
    private String redisDatabase;

    public String getRedisHost() {
        return redisHost;
    }

    public void setRedisHost(String redisHost) {
        this.redisHost = redisHost;
    }

    public int getRedisPort() {
        return redisPort;
    }

    public void setRedisPort(int redisPort) {
        this.redisPort = redisPort;
    }

    public String getRedisPassword() {
        return redisPassword;
    }

    public void setRedisPassword(String redisPassword) {
        this.redisPassword = redisPassword;
    }

    public String getRedisDatabase() {
        return redisDatabase;
    }

    public void setRedisDatabase(String redisDatabase) {
        this.redisDatabase = redisDatabase;
    }

}