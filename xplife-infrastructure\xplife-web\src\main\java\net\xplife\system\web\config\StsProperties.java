package net.xplife.system.web.config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
@Component
public class StsProperties {
    @Value("${stsAccesskey:}")
    private String stsAccesskey;
    @Value("${stsAccesskeySecret:}")
    private String stsAccesskeySecret;
    @Value("${stsRegion:}")
    private String stsRegion;
    @Value("${stsEndPoint:}")
    private String stsEndPoint;
    @Value("${stsRoleArn:}")
    private String stsRoleArn;
    @Value("${stsRoleSessionName:}")
    private String stsRoleSessionName;
    @Value("${stsDurationSeconds:0}")
    private long   stsDurationSeconds;
    @Value("${charEncode:}")
    private String charEncode;
    @Value("${stsEndPoints:}")
    private String stsEndPoints;

    public String getStsAccesskey() {
        return stsAccesskey;
    }

    public void setStsAccesskey(String stsAccesskey) {
        this.stsAccesskey = stsAccesskey;
    }

    public String getStsAccesskeySecret() {
        return stsAccesskeySecret;
    }

    public void setStsAccesskeySecret(String stsAccesskeySecret) {
        this.stsAccesskeySecret = stsAccesskeySecret;
    }

    public String getStsRegion() {
        return stsRegion;
    }

    public void setStsRegion(String stsRegion) {
        this.stsRegion = stsRegion;
    }

    public String getStsEndPoint() {
        return stsEndPoint;
    }

    public void setStsEndPoint(String stsEndPoint) {
        this.stsEndPoint = stsEndPoint;
    }

    public String getStsRoleArn() {
        return stsRoleArn;
    }

    public void setStsRoleArn(String stsRoleArn) {
        this.stsRoleArn = stsRoleArn;
    }

    public String getStsRoleSessionName() {
        return stsRoleSessionName;
    }

    public void setStsRoleSessionName(String stsRoleSessionName) {
        this.stsRoleSessionName = stsRoleSessionName;
    }

    public long getStsDurationSeconds() {
        return stsDurationSeconds;
    }

    public void setStsDurationSeconds(long stsDurationSeconds) {
        this.stsDurationSeconds = stsDurationSeconds;
    }

    public String getCharEncode() {
        return charEncode;
    }

    public void setCharEncode(String charEncode) {
        this.charEncode = charEncode;
    }

    public String getStsEndPoints() {
        return stsEndPoints;
    }

    public void setStsEndPoints(String stsEndPoints) {
        this.stsEndPoints = stsEndPoints;
    }
}