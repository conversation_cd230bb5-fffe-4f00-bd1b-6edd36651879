package net.xplife.system.web.core;
import java.util.Enumeration;
import java.util.LinkedHashMap;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.common.WebConst;
import net.xplife.system.web.enums.FeignHeadParamEnums;
import feign.RequestInterceptor;
import feign.RequestTemplate;
/**
 * 创建Feign请求拦截器，设置请求参数和head
 * 
 * <AUTHOR> 2018年7月11日
 */
public class FeignRequestInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate requestTemplate) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        requestTemplate.header(WebConst.FEIGN_FLAG_KEY, WebConst.FEIGN_FLAG_KEY);
        if (ToolsKit.isNotEmpty(attributes)) {
            HttpServletRequest request = attributes.getRequest();
            Enumeration<String> headerNames = request.getHeaderNames();
            LinkedHashMap<String, String> map = FeignHeadParamEnums.getMap();
            if (headerNames != null) {
                while (headerNames.hasMoreElements()) {
                    String name = headerNames.nextElement();
                    if (map.containsKey(name)) {
                        String values = request.getHeader(name);
                        requestTemplate.header(name, values);
                    }
                }
            }
        }
    }
}
