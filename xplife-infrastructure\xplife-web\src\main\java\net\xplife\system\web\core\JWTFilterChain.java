package net.xplife.system.web.core;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.web.common.WebKit;

/***
 * jwt过滤器
 */
public class JWTFilterChain implements Filter {
    /**
     * 初始化
     */
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    /**
     * 执行部分
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws ServiceException {
        try {
            System.out.println("============> JWTFilterChain");
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            WebKit.checkedJWT(httpRequest);
            chain.doFilter(request, response);
        } catch (ServiceException e) {
            e.printStackTrace();
            WebKit.printResult(response, e.getCode(), e.getMessage());
            return;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.NET_EXCEPTION.getCode()).setMessage(ExceptionEnums.NET_EXCEPTION.getMessage());
        }
    }

    /**
     * 销毁
     */
    @Override
    public void destroy() {
    }
}