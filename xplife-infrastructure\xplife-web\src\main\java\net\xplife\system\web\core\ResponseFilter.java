package net.xplife.system.web.core;
import java.io.IOException;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.xplife.system.web.common.WebConst;
import net.xplife.system.web.common.WebKit;
import net.xplife.system.web.dto.ReturnDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.common.JsonKit;

/**
 * 统一返回拦截器
 * 
 * <AUTHOR> 2018年7月11日
 */
public class ResponseFilter implements Filter {
    private final Logger log         = LoggerFactory.getLogger(ResponseFilter.class);
    private final String CAPTCHA_URI = "/smscenter/v1/sms/getcaptcha";               // 图片验证码地址
    private final String WX_OATH_URI = "/community/v1/wx/getuserinfo";               // 微信授权地址

    /**
     * 初始化
     */
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    /**
     * 执行部分
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        WebKit.printArrivalTime(httpRequest);
        if (WX_OATH_URI.equals(httpRequest.getRequestURI())) {
            chain.doFilter(request, response);
        } else {
            ResponseWrapper wrapperResponse = new ResponseWrapper((HttpServletResponse) response);// 转换成代理类

            // 这里只拦截返回，直接让请求过去，如果在请求前有处理，可以在这里处理
            chain.doFilter(request, wrapperResponse);
            byte[] contentByte = wrapperResponse.getContent();// 获取返回值
            // 判断是否有值
            if (contentByte.length > 0) {
                if (CAPTCHA_URI.equals(httpRequest.getRequestURI())) {// 处理短信图片验证码返回
                    response.setCharacterEncoding(WebConst.ENCODING_FIELD);// 设置utf-8编码
                    response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
                    response.setContentLength(contentByte.length);
                    // 把返回值输出到客户端
                    ServletOutputStream out = response.getOutputStream();
                    out.write(contentByte);
                    out.flush();
                } else {
                    String text = new String(contentByte, WebConst.ENCODING_FIELD);
                    String content = null;
                    if (ToolsKit.isNotEmpty(text) && (JsonKit.isMapJsonString(text) || JsonKit.isArrayJsonString(text))) {
                        ReturnDto returnDto = JsonKit.jsonParseObject(text, ReturnDto.class);
                        if (ToolsKit.isNotEmpty(WebKit.getValue(WebConst.FEIGN_FLAG_KEY, httpRequest))) {
                            if (returnDto.getData() != null) {
                                content = JsonKit.toJsonString(returnDto.getData());
                            } else {
                                content = "{}";
                            }
                        } else if (ToolsKit.isEmpty(returnDto.getHead()) && ToolsKit.isEmpty(returnDto.getData())) {
                            content = text;
                        } else {
                            content = JsonKit.toJsonString(returnDto);
                        }
                    } else {
                        content = text;
                    }
                    response.setCharacterEncoding(WebConst.ENCODING_FIELD);// 设置utf-8编码
                    response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
                    response.setContentLength(content.getBytes(WebConst.ENCODING_FIELD).length);
                    try {// 如果客户端主动断开跟服务器的连接，此时响应请求，回报 org.eclipse.jetty.io.EofException 异常
                         // 把返回值输出到客户端
                        ServletOutputStream out = response.getOutputStream();
                        out.write(content.getBytes(WebConst.ENCODING_FIELD));
                        out.flush();
                    } catch (Exception e) {
                    }
                    WebKit.sendJournal(httpRequest, content, WebConst.QUEUE_MSG_TYPE_RESPONSE);
                    WebKit.printArrivalTime(httpRequest);
                    log.debug("请求返回信息：" + content);
                }
            }
        }
    }

    /**
     * 销毁
     */
    @Override
    public void destroy() {
    }
}