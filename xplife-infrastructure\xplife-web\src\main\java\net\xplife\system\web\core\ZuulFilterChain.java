package net.xplife.system.web.core;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import net.xplife.system.web.common.WebConst;
import net.xplife.system.web.common.WebKit;
import net.xplife.system.web.jwt.SubjectModel;
import org.apache.commons.lang3.StringUtils;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.common.JsonKit;

/**
 * 统一网关过滤器
 * 
 * <AUTHOR> 2018年7月11日
 */
public class Zuul<PERSON>ilter<PERSON>hain implements Filter {
    /**
     * 初始化
     */
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    /**
     * 执行部分
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws ServiceException {
        try {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            WebKit.printArrivalTime(httpRequest);
            if (ToolsKit.isNotEmpty(WebKit.getJwtToken(httpRequest))) {
                String jsonString = WebKit.getSubjectModel(httpRequest);
                if (ToolsKit.isNotEmpty(jsonString)) {
                    SubjectModel subjectModel = JsonKit.jsonParseObject(jsonString, SubjectModel.class);
                    request.setAttribute(WebConst.JWT_SUBJECT, subjectModel);
                    request.setAttribute(WebConst.JWT_USER_INFO, WebKit.getUserInfoDto(subjectModel.getUserInfo(), httpRequest));
                }
            }
            String headJsonString = WebKit.getHeadInfoDto(httpRequest);
            httpRequest.setAttribute(WebConst.HEAD_INFO_DATA, headJsonString);// head头信息
            ServletRequest requestWrapper = new RequestWrapper(httpRequest);
            WebKit.fillZuulStreamParam((HttpServletRequest) requestWrapper);
            WebKit.printRequestInfo((HttpServletRequest) requestWrapper);
            chain.doFilter(requestWrapper, response);
            WebKit.sendJournal((HttpServletRequest) requestWrapper, StringUtils.EMPTY, WebConst.QUEUE_MSG_TYPE_REQUEST);
        } catch (ServiceException e) {
            e.printStackTrace();
            WebKit.printResult(response, e.getCode(), e.getMessage());
            return;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.NET_EXCEPTION.getCode()).setMessage(ExceptionEnums.NET_EXCEPTION.getMessage());
        }
    }

    /**
     * 销毁
     */
    @Override
    public void destroy() {
    }
}