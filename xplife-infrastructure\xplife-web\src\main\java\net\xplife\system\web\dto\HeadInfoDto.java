package net.xplife.system.web.dto;
/**
 * 提交请求head头里信息
 */
public class HeadInfoDto implements java.io.Serializable {
    private static final long serialVersionUID = 1L;
    private String            header;               // 自定义请求头
    private String            uaFlag;               // ua
    private String            uniqueFlag;           // 唯一标识 安卓传imei，获取不到时传androidid IOS传idfa
    private String            deviceSystem;         // 系统设备 ANDROID IOS H5 MA WEB
    private String            ip;                   // IP
    private String            channel;              // 渠道
    private String            version;              // 版本
    private String            appName;              // 应用英文名称
    private String            station;              // H5时站内、站外
    private String            language;             // 语言
    private Boolean           overseas;             // 是否是海外版本，默认不是
    private String            phoneType;            // 手机型号

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getStation() {
        return station;
    }

    public void setStation(String station) {
        this.station = station;
    }

    public String getHeader() {
        return header;
    }

    public void setHeader(String header) {
        this.header = header;
    }

    public String getUaFlag() {
        return uaFlag;
    }

    public void setUaFlag(String uaFlag) {
        this.uaFlag = uaFlag;
    }

    public String getUniqueFlag() {
        return uniqueFlag;
    }

    public void setUniqueFlag(String uniqueFlag) {
        this.uniqueFlag = uniqueFlag;
    }

    public String getDeviceSystem() {
        return deviceSystem;
    }

    public void setDeviceSystem(String deviceSystem) {
        this.deviceSystem = deviceSystem;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Boolean getOverseas() {
        return overseas;
    }

    public void setOverseas(Boolean overseas) {
        this.overseas = overseas;
    }

    public String getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(String phoneType) {
        this.phoneType = phoneType;
    }
}
