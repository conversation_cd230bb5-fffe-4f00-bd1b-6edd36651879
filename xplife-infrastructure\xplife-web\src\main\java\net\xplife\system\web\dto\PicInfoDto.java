package net.xplife.system.web.dto;
import java.io.Serializable;
public class PicInfoDto implements Serializable {
    /**
     * 图片信息
     */
    private static final long serialVersionUID = 1L;
    private long              fileSize;             // 文件大小
    private String            format;               // 格式
    private int               imageHeight;          // 高
    private int               imageWidth;           // 宽

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public int getImageHeight() {
        return imageHeight;
    }

    public void setImageHeight(int imageHeight) {
        this.imageHeight = imageHeight;
    }

    public int getImageWidth() {
        return imageWidth;
    }

    public void setImageWidth(int imageWidth) {
        this.imageWidth = imageWidth;
    }
}
