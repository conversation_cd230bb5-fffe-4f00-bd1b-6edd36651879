package net.xplife.system.web.dto;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import net.xplife.system.tools.common.core.ToolsConst;
/**
 * 基础查询的DTO 如果有补充请继承该DTO，然后再添加自身特有的查询条件属性
 * 
 * <AUTHOR> 2018年11月15日
 */
public class QueryDto implements Serializable {
    /**
     * 
     */
    private static final long   serialVersionUID = 1L;
    /**
     * 分页参数，分页长度
     */
    private int                 pageSize;
    /**
     * 分页参数，分布起始页
     */
    private int                 pageNo;
    /**
     * 输入的查询参数
     */
    private Map<String, Object> searchValue;
    /**
     * 查询时间(开始)
     */
    private String              startDate;
    /**
     * 查询时间(结束)
     */
    private String              endDate;

    public QueryDto() {
        searchValue = new HashMap<String, Object>();
    }

    public int getPageSize() {
        if (pageSize <= 0) {
            pageSize = ToolsConst.PHONEPAGESIZE;
        }
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        if (pageNo > 0) {
            return pageNo;
        } else {
            return 0;
        }
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public Map<String, Object> getSearchValue() {
        return searchValue;
    }

    public void setSearchValue(Map<String, Object> searchValue) {
        this.searchValue = searchValue;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
}
