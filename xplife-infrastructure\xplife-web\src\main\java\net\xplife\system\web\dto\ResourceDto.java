package net.xplife.system.web.dto;
import java.io.Serializable;
import java.util.List;
public class ResourceDto implements Serializable {
    /**
     * 资源dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            pId;                  // 父节点ID
    private String            resName;              // 资源名称
    private String            resCode;              // 资源编码
    private String            path;                 // 访问路径
    private int               level;                // 层级
    private int               sort;                 // 排序
    private int               type;                 // 资源类型 0菜单 1按钮
    private String            projectId;            // 项目ID
    private String            projectName;          // 项目名称
    private String            projectCode;          // 项目编号
    private String            icon;                 // 菜单图标
    private List<ResourceDto> resourceDtoList;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public List<ResourceDto> getResourceDtoList() {
        return resourceDtoList;
    }

    public void setResourceDtoList(List<ResourceDto> resourceDtoList) {
        this.resourceDtoList = resourceDtoList;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getpId() {
        return pId;
    }

    public void setpId(String pId) {
        this.pId = pId;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }
}
