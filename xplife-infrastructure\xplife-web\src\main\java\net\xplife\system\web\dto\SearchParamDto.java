package net.xplife.system.web.dto;
import java.io.Serializable;
public class SearchParamDto implements Serializable {
    /**
     * 查询参数dto
     */
    private static final long serialVersionUID = 1L;
    private String            inputType;            // 输入类型
    private String            paramKey;             // 参数key
    private Object            paramData;            // 参数数据
    private String            label;                // 标签名称
    private int               sort;                 // 排序

    public SearchParamDto() {
        super();
    }

    public SearchParamDto(String inputType, String paramKey, Object paramData, String label, int sort) {
        super();
        this.inputType = inputType;
        this.paramKey = paramKey;
        this.paramData = paramData;
        this.label = label;
        this.sort = sort;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getInputType() {
        return inputType;
    }

    public void setInputType(String inputType) {
        this.inputType = inputType;
    }

    public String getParamKey() {
        return paramKey;
    }

    public void setParamKey(String paramKey) {
        this.paramKey = paramKey;
    }

    public Object getParamData() {
        return paramData;
    }

    public void setParamData(Object paramData) {
        this.paramData = paramData;
    }
}
