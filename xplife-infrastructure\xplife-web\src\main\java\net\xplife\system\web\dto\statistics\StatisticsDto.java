package net.xplife.system.web.dto.statistics;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class StatisticsDto<T> implements Serializable {
    /**
     * 统计数据dto
     */
    private static final long          serialVersionUID = -874087072428605327L;
    private String                     title;                                                   // 标题
    private boolean                    cached;                                                  // 是否缓存，主要用于缓存标识是否重载
    private List<StatisticsItemDto<T>> items            = new ArrayList<StatisticsItemDto<T>>();// 展示统计数据
    private List<String>               other            = new ArrayList<String>();              // 预留
    private List<String>               categories       = new ArrayList<String>();              // X轴维度

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public boolean isCached() {
        return cached;
    }

    public void setCached(boolean cached) {
        this.cached = cached;
    }

    public List<StatisticsItemDto<T>> getItems() {
        return items;
    }

    public void setItems(List<StatisticsItemDto<T>> items) {
        this.items = items;
    }

    public List<String> getOther() {
        return other;
    }

    public void setOther(List<String> other) {
        this.other = other;
    }

    public List<String> getCategories() {
        return categories;
    }

    public void setCategories(List<String> categories) {
        this.categories = categories;
    }
}
