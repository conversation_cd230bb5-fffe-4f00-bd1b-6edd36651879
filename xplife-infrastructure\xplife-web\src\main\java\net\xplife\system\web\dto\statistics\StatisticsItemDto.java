package net.xplife.system.web.dto.statistics;
import java.io.Serializable;
import java.util.List;
public class StatisticsItemDto<T> implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    private String            name;                 // 分类名称
    private List<T>           data;                 // 数据
    private String            unit;                 // 单位
    private String            type;                 // 类别
    private T                 total;                // 总数

    public T getTotal() {
        return total;
    }

    public void setTotal(T total) {
        this.total = total;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
