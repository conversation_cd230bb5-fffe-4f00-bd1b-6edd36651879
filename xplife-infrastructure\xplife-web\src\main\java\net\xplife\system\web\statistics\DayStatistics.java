package net.xplife.system.web.statistics;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import net.xplife.system.tools.util.core.ToolsKit;
/**
 * 日统计类
 * 
 * <AUTHOR> 2018年11月27日
 * @param <T>
 */
public abstract class DayStatistics<T> {
    private static final boolean MT_MODE = true;

    /**
     * 按时统计
     * 
     * @param current
     * @param params
     * @return
     */
    public abstract T countHours(Date current, Object params);

    /**
     * 按日获取记录
     *
     * @param date
     * @param params
     * @return
     */
    public List<T> getDayByType(Date date, Object params) {
        Date now = new Date();
        List<T> day = new ArrayList<T>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        HashMap<Integer, Future<T>> valueMap = new HashMap<>(24, 1);
        for (int i = 0; i <= 23; ++i) {
            calendar.set(Calendar.HOUR_OF_DAY, i);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            final Date current = calendar.getTime();
            if (current.before(now)) {
                if (MT_MODE) {
                    valueMap.put(i, ToolsKit.Thread.execAsync(new Callable<T>() {
                        @Override
                        public T call() throws Exception {
                            return countHours(current, params);
                        }
                    }));
                } else {
                    day.add(countHours(current, params));
                }
            } else {
                break;
            }
        }
        if (MT_MODE) {
            MonthStatisticsStorage.mapAddToList(day, valueMap, 0, 23);
        }
        return day;
    }
}
