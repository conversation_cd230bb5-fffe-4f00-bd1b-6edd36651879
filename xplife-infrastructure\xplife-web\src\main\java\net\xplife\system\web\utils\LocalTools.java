package net.xplife.system.web.utils;

/**
 * 本地化工具类
 */
public class LocalTools {

    // ------------ TOAST ------------

    public static final String toast_log_method_blank = "toast_log_method_blank";       // 登录方式不能为空
    public static final String toast_acc_notexist = "toast_acc_notexist";               // 账号不存在
    public static final String toast_acc_notinquire = "toast_acc_notinquire";           // 查询不到账号信息
    public static final String toast_log_signfail = "toast_log_signfail";               // 登录签名失败
    public static final String toast_reg_repeat = "toast_reg_repeat";                   // 短时间内重复注册
    public static final String toast_log_incorrect = "toast_log_incorrect"; // 登录信息不正确
    public static final String toast_mobile_notblank = "toast_mobile_notblank"; // 电话号码不能为空
    public static final String toast_msgcode_notblank = "toast_msgcode_notblank"; // 短信验证码不能为空
    public static final String toast_vercode_incorrect = "toast_vercode_incorrect"; // 验证码不正确
    public static final String toast_acc_invalid = "toast_acc_invalid"; // 无效的账号
    public static final String toast_op_freq_try = "toast_op_freq_try"; // 您操作太频繁，请明天再试
    public static final String toast_acc_lock_try = "toast_acc_lock_try"; // 您的账号已被锁定，请明天再试
    public static final String toast_reg_abnormal_try = "toast_reg_abnormal_try"; // 您的注册行为异常，请明天再试
    public static final String toast_userid_notblank = "toast_userid_notblank"; // 用户ID不能为空！
    public static final String toast_deviceid_notblank = "toast_deviceid_notblank"; // 设备ID不能为空！
    public static final String toast_codeid_notblank = "toast_codeid_notblank"; // codeID不能为空
    public static final String toast_user_codeid_notblank = "toast_user_codeid_notblank"; // 用户codeId不能为空
    public static final String toast_codeid_create_error = "toast_codeid_create_error"; // 创建codeId错误
    public static final String toast_user_notexist = "toast_user_notexist"; // 用户不存在！
    public static final String toast_bindmethod_notblank = "toast_bindmethod_notblank"; // 绑定方式不能为空！
    public static final String toast_acc_bound = "toast_acc_bound"; // 账号已经被绑定了！
    public static final String oast_acc_bindfail = "oast_acc_bindfail"; // 绑定账号失败！
    public static final String toast_resetpwd_error = "toast_resetpwd_error"; // 重置密码时出错
    public static final String toast_mobile_bound = "toast_mobile_bound"; // 手机号已经被绑定过！
    public static final String toast_mobile_not_unbound = "toast_mobile_not_unbound"; // 手机号不能解绑
    public static final String toast_bindmobile_resetpwd_error = "toast_bindmobile_resetpwd_error"; // 绑定手机设置密码时出错
    public static final String toast_acc_obtainfail = "toast_acc_obtainfail"; // 获取用户账号失败
    public static final String toast_acc_preserve_onebind = "toast_acc_preserve_onebind"; // 至少需要保留一个绑定账户
    public static final String toast_unsupport_type = "toast_unsupport_type"; // 不支持的类型
    public static final String toast_unbind_fail = "toast_unbind_fail"; // 解绑失败
    public static final String toast_auth_fail = "toast_auth_fail"; // 授权失败
    public static final String toast_acc_old_notblank = "toast_acc_old_notblank"; // 旧账号不能为空
    public static final String toast_inquire_auth_incorrect = "toast_inquire_auth_incorrect"; // 查询第三方授权信息出错
    public static final String toast_inquire_info_incorrect = "toast_inquire_info_incorrect"; // 查询用户信息出错
    public static final String toast_obtain_unionid_fail = "toast_obtain_unionid_fail"; // unionId获取失败
    public static final String toast_unsupport_source = "toast_unsupport_source"; // 不支持的来源
    public static final String toast_unbind_sameacc_fail = "toast_unbind_sameacc_fail"; // 不是同个账号，解绑失败
    public static final String toast_sinaweibo_auth_fail = "toast_sinaweibo_auth_fail"; // 新浪微博授权失败
    public static final String toast_auth_qq_fail = "toast_auth_qq_fail"; // QQ授权失败
    public static final String toast_auth_wechat_fail = "toast_auth_wechat_fail"; // 微信授权失败
    public static final String toast_id_notblank = "toast_id_notblank"; // ID不能为空
    public static final String toast_param_illegal = "toast_param_illegal"; // 参数不合法
    public static final String toast_reg_fail_try = "toast_reg_fail_try"; // 由于网络的原因，注册失败，请重试!
    public static final String toast_infotype_notblank = "toast_infotype_notblank"; // 消息类型不能为空
    public static final String toast_msginfo_notblank = "toast_msginfo_notblank"; // 消息信息不能为空
    public static final String toast_sendid_notblank = "toast_sendid_notblank"; // 发送人ID不能为空
    public static final String toast_msgcontent_notblank = "toast_msgcontent_notblank"; // 消息内容不能为空
    public static final String toast_coltype_notblank = "toast_coltype_notblank"; // 栏目类型不能为空
    public static final String toast_labelid_notblank = "toast_labelid_notblank"; // 标签ID不能为空
    public static final String toast_msginfo_notinquire = "toast_msginfo_notinquire"; // 查询不到系统消息信息

    // ------------ 消息中心 -------------
    public static final String msg_communitymessage = "msg_communitymessage"; // 社区消息
    public static final String msg_likedyourmaterial = "msg_likedyourmaterial"; // 喜欢了你素材
    public static final String msg_friendmessage = "msg_friendmessage"; // 好友消息
    public static final String msg_addyouasafriend = "msg_addyouasafriend"; // 加你为好友
    public static final String msg_agreetobeafriend = "msg_agreetobeafriend"; // 同意你为好友
    public static final String msg_systemmessage = "msg_systemmessage"; // 系统消息
    public static final String msg_sharedevicemessage = "msg_sharedevicemessage"; // 互动打印消息
    public static final String msg_newnote = "msg_newnote"; // 您有一条纸条消息
    public static final String msg_check = "msg_check"; // 前去查看

    // ------------ 推送消息 -------------
    public static final String push_newmessage = "push_newmessage"; // 您有一条新的消息
    public static final String push_newfriendrequest = "push_newfriendrequest"; // 您有一条好友申请

    // -------------------------

    public static final String toast_mobile_invalid = "toast_mobile_invalid"; // 手机号码不合法
    public static final String toast_email_invalid = "toast_email_invalid"; // 邮箱地址不合法
    public static final String toast_accid_notempty = "toast_accid_notempty"; // 账号标识不能为空
    public static final String toast_3token_notempty = "toast_3token_notempty"; // 第三方token不能为空
    public static final String toast_stime_resubmit = "toast_stime_resubmit"; // 短时间内重复提交

    public static final String toast_login_success = "toast_login_success"; // 登录成功
    public static final String toast_vercode_notexist = "toast_vercode_notexist"; // 验证码不存在或已过期
    public static final String toast_opmore_aftertry = "toast_opmore_aftertry"; // 您操作太频繁，待会再试
    public static final String toast_vermore_aftertry = "toast_vermore_aftertry"; // 您的验证码验证太频繁了，请明天再试
    public static final String toast_vercode_getfail = "toast_vercode_getfail"; // 获取验证码失败
    public static final String toast_newpass_notempty = "toast_newpass_notempty"; // 新密码不能为空
    public static final String toast_queryacc_error = "toast_queryacc_error"; // 查询用户账号信息出错
    public static final String toast_oldpass_invalid = "toast_oldpass_invalid"; // 原密码不正确
    public static final String toast_editpass_error = "toast_editpass_error"; // 修改密码时出错
    public static final String toast_email_notempty = "toast_email_notempty"; // 邮箱地址不能为空
    public static final String toast_vercode_notempty = "toast_vercode_notempty"; // 邮箱验证码不能为空
    public static final String toast_email_notunbind = "toast_email_notunbind"; // 邮箱地址不能解绑
    public static final String toast_email_bound = "toast_email_bound"; // 电子邮箱已经被绑定过

    public static final String toast_acc_bound1 = "toast_acc_bound1"; // 账号已经被Tom绑定了

    public static final String toast_email_editerror = "toast_email_editerror"; // 修改用户电子邮箱时出错
    public static final String toast_opbusy_5sretry = "toast_opbusy_5sretry"; // 您操作太频繁，请5秒后再试
    public static final String toast_ipperday_uplimit = "toast_ipperday_uplimit"; // IP单日上限
    public static final String toast_verperday_uplimit = "toast_verperday_uplimit"; // 单日验证码上限
    public static final String toast_vercode_poptimes = "toast_vercode_poptimes"; // 验证码弹窗次数

    public static final String toast_accid_bound1 = "toast_accid_bound1"; // 用户的id已被Tom绑定

    public static final String app_default_websites = "app_default_websites"; // 默认网站

    public static final String toast_account_unregister_no_mobile="toast_account_unregister_no_mobile"; //注销失败，当前手机号码不存在
    public static final String toast_account_cannot_register_seven_day="toast_account_cannot_register_seven_day";//该号码七日内不可再注册
    public static final String toast_account_unregister_success="toast_account_unregister_success";//当前账号已注销，7天内无法再次注册

}
