package net.xplife.system.web.utils;
import java.util.Locale;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.enums.LanguageEnums;

public class WebTools {
    /**
     * 设定安全密码的Salt
     */
    public static byte[] buildEntryptSalt() {
        return Digests.generateSalt(8);
    }

    /**
     * 设定安全的密码，生成随机的salt并经过1024次 sha-1 hash
     */
    public static String buildEntryptPassword(String password, byte[] salt) {
        byte[] hashPassword = Digests.sha1(password.getBytes(), salt, 1024);
        return Encodes.encodeHex(hashPassword);
    }

    /**
     * 获取语言
     * 
     * @param language
     * @return
     */
    public static Locale getLocaleByLanguage(String language) {
        if (ToolsKit.isNotEmpty(language)) {
            if (language.equals(LanguageEnums.ZH_CN.getKey())) {
                return Locale.SIMPLIFIED_CHINESE;
            } else if (language.equals(LanguageEnums.EN_US.getKey())) {
                return Locale.US;
            } else if (language.equals(LanguageEnums.JA_JP.getKey())) {
                return Locale.JAPAN;
            } else if (language.equals(LanguageEnums.KO_KR.getKey())) {
                return Locale.KOREA;
            } else if (language.equals(LanguageEnums.ZH_TW.getKey())) {
                return Locale.TRADITIONAL_CHINESE;
            } else if (language.equals(LanguageEnums.FR_FR.getKey())) {
                return Locale.FRANCE;
            } else if (language.equals(LanguageEnums.DE_DE.getKey())) {
                return Locale.GERMANY;
            } else if (language.equals(LanguageEnums.ES_ES.getKey())) {
                return new Locale("es","ES");
            } else if (language.equals(LanguageEnums.IT_IT.getKey())) {
                return Locale.ITALY;
            }
        }
        return Locale.SIMPLIFIED_CHINESE;
    }
}
