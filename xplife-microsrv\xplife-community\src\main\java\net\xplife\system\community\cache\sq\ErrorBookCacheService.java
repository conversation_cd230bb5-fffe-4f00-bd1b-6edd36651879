package net.xplife.system.community.cache.sq;
import org.springframework.stereotype.Service;
import net.xplife.system.cache.kit.CacheKit;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.community.entity.sq.ErrorBook;
import net.xplife.system.community.enums.sq.ErrorBookCacheEnums;
/**
 * 错题本缓存服务类
 * 
 * <AUTHOR> 2018年7月25日
 */
@Service
public class ErrorBookCacheService {
    /**
     * 保存错题本数据
     * 
     * @param errorBook
     */
    public void saveErrorBook(ErrorBook errorBook) {
        String key = ErrorBookCacheEnums.ERROR_BOOK_BY_ID.getKey() + errorBook.getId();
        CacheKit.cache().set(key, errorBook, ToolsConst.MONTH_SECOND);
    }

    /**
     * 获取错题本数据
     * 
     * @param id
     *            记录ID
     * @return
     */
    public ErrorBook getErrorBook(String id) {
        String key = ErrorBookCacheEnums.ERROR_BOOK_BY_ID.getKey() + id;
        return CacheKit.cache().get(key, ErrorBook.class);
    }

    /**
     * 删除错题本数据
     * 
     * @param id
     *            记录ID
     */
    public void delErrorBook(String id) {
        String key = ErrorBookCacheEnums.ERROR_BOOK_BY_ID.getKey() + id;
        CacheKit.cache().del(key);
    }
}
