package net.xplife.system.community.cache.sq;
import org.springframework.stereotype.Service;
import net.xplife.system.cache.kit.CacheKit;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.community.entity.sq.ReadRecord;
import net.xplife.system.community.enums.sq.ReadRecordCacheEnums;
/**
 * 已读记录缓存服务类
 * 
 * <AUTHOR> 2018年7月25日
 */
@Service
public class ReadRecordCacheService {
    /**
     * 保存已读记录数据
     * 
     * @param readRecord
     */
    public void saveReadRecord(ReadRecord readRecord) {
        String key = ReadRecordCacheEnums.READ_RECORD_BY_COURSE_ID.getKey() + readRecord.getDay() + ":" + readRecord.getUcId();
        CacheKit.cache().set(key, readRecord, ToolsConst.MONTH_SECOND);
    }

    /**
     * 获取已读记录数据
     * 
     * @param day
     *            天数
     * @param ucId
     *            用户课程ID
     * @return
     */
    public ReadRecord getReadRecord(int day, String ucId) {
        String key = ReadRecordCacheEnums.READ_RECORD_BY_COURSE_ID.getKey() + day + ":" + ucId;
        return CacheKit.cache().get(key, ReadRecord.class);
    }

    /**
     * 删除已读记录数据
     * 
     * @param day
     *            天数
     * @param ucId
     *            用户课程ID
     */
    public void delReadRecord(int day, String ucId) {
        String key = ReadRecordCacheEnums.READ_RECORD_BY_COURSE_ID.getKey() + day + ":" + ucId;
        CacheKit.cache().del(key);
    }
}
