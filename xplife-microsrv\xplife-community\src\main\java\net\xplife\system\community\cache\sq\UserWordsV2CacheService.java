package net.xplife.system.community.cache.sq;

import net.xplife.system.cache.kit.CacheKit;
import net.xplife.system.community.entity.sq.UserWords;
import net.xplife.system.community.entity.sq.UserWordsV2;
import net.xplife.system.community.enums.sq.UserWordsCacheEnums;
import net.xplife.system.community.enums.sq.UserWordsV2CacheEnums;
import net.xplife.system.tools.common.core.ToolsConst;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户生词本缓存服务类
 * 
 * <AUTHOR> 2018年7月25日
 */
@Service
public class UserWordsV2CacheService {
    /**
     * 保存用户生词本数据
     * 
     * @param userWords
     */
    public void saveUserWords(UserWordsV2 userWords) {
        String key = UserWordsV2CacheEnums.USER_WORDS_BY_ID.getKey() + userWords.getId();
        String key2 = UserWordsV2CacheEnums.USER_WORDS_BY_FLAG.getKey() + userWords.getUserId() + ":" + userWords.getFlag();
        CacheKit.cache().set(key, userWords, ToolsConst.MONTH_SECOND);
        CacheKit.cache().set(key2, userWords, ToolsConst.MONTH_SECOND);
    }

    /**
     * 获取用户生词本数据
     * 
     * @param id
     *            记录ID
     * @return
     */
    public UserWordsV2 getUserWords(String id) {
        String key = UserWordsV2CacheEnums.USER_WORDS_BY_ID.getKey() + id;
        return CacheKit.cache().get(key, UserWordsV2.class);
    }

    /**
     * 获取用户生词本数据
     * 
     * @param flag
     *            标识
     * @return
     */
    public UserWordsV2 getUserWordsByFlag(String userId, String flag) {
        String key = UserWordsV2CacheEnums.USER_WORDS_BY_FLAG.getKey() + userId + ":" + flag;
        return CacheKit.cache().get(key, UserWordsV2.class);
    }

    /**
     * 删除用户生词本数据
     * 
     * @param id
     *            记录ID
     */
    public void delUserWords(String id) {
        String key = UserWordsV2CacheEnums.USER_WORDS_BY_ID.getKey() + id;
        CacheKit.cache().del(key);
    }

    /**
     * 删除用户生词本数据
     * 
     * @param flag
     *            记录ID
     */
    public void delUserWordsByFlag(String userId, String flag) {
        String key = UserWordsV2CacheEnums.USER_WORDS_BY_FLAG.getKey() + userId + ":" + flag;
        CacheKit.cache().del(key);
    }

    /**
     * 获取用户生词本列表
     * 
     * @param userId
     *            用户ID
     * @param page
     *            当前页码
     * @param pageSize
     *            每页大小
     * @return
     */
    public List<String> getUserWordsIdsList(String userId, int page, int pageSize) {
        String key = UserWordsV2CacheEnums.USER_WORDS_ID_LIST.getKey() + userId;
        return CacheKit.cache().zrevrange(key, page, pageSize);
    }

    /**
     * 保存用户生词本列表
     * 
     * @param userId
     *            用户ID
     * @param id
     *            记录ID
     * @param time
     */
    public void addUserWordsIdToList(String userId, String id, long time) {
        String key = UserWordsV2CacheEnums.USER_WORDS_ID_LIST.getKey() + userId;
        CacheKit.cache().zadd(key, time, id, ToolsConst.MONTH_SECOND);
    }

    /**
     * 从用户生词本列表ID集合中删除ID
     * 
     * @param userId
     *            用户ID
     * @param id
     *            记录ID
     */
    public void removeUserWordsIdToList(String userId, String id) {
        String key = UserWordsV2CacheEnums.USER_WORDS_ID_LIST.getKey() + userId;
        CacheKit.cache().zrem(key, id);
    }

    /**
     * 判断用户生词本列表是否存在
     * 
     * @param userId
     *            用户ID
     * @return
     */
    public boolean existsList(String userId) {
        String key = UserWordsV2CacheEnums.USER_WORDS_ID_LIST.getKey() + userId;
        return CacheKit.cache().exists(key);
    }

    /**
     * 获取ID所在集合列表中的位置
     * 
     * @param userId
     *            用户ID
     * @param id
     *            记录ID
     * @return
     */
    public Long zrevrank(String userId, String id) {
        String key = UserWordsV2CacheEnums.USER_WORDS_ID_LIST.getKey() + userId;
        return CacheKit.cache().zrevrank(key, id);
    }
}
