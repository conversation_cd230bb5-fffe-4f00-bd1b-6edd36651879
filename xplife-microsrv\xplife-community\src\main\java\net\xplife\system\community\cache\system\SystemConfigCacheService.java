package net.xplife.system.community.cache.system;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Service;
import net.xplife.system.cache.kit.CacheKit;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.community.enums.system.SystemConfigCacheEnums;
/**
 * 系统配置缓存服务类
 * 
 * <AUTHOR> 2018年7月25日
 */
@Service
public class SystemConfigCacheService {
    /**
     * 保存系统配置信息
     * 
     * @param type
     *            类型
     * @param map
     *            配置信息
     */
    public void saveSystemConfig(int type, Map<String, String> map) {
        String key = SystemConfigCacheEnums.SYSTEM_CONFIG_PRE.getKey() + type;
        CacheKit.cache().set(key, map, ToolsConst.DAY_SECOND * 15);
    }

    /**
     * 获取系统配置信息
     * 
     * @param type
     *            类型
     * @return
     */
    @SuppressWarnings("unchecked")
    public Map<String, String> getSystemConfig(int type) {
        String key = SystemConfigCacheEnums.SYSTEM_CONFIG_PRE.getKey() + type;
        return CacheKit.cache().get(key, HashMap.class);
    }

    /**
     * 删除系统配置信息
     * 
     * @param type
     *            类型
     * @return
     */
    public void delSystemConfig(int type) {
        String key = SystemConfigCacheEnums.SYSTEM_CONFIG_PRE.getKey() + type;
        CacheKit.cache().del(key);
    }
}
