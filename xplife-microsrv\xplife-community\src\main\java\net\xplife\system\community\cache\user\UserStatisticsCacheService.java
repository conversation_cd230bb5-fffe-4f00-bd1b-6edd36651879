package net.xplife.system.community.cache.user;
import org.springframework.stereotype.Service;
import net.xplife.system.cache.kit.CacheKit;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.community.entity.user.UserStatistics;
import net.xplife.system.community.enums.user.UserStatisticsCacheEnums;
/**
 * 用户统计信息缓存服务类
 * 
 * <AUTHOR> 2018年7月25日
 */
@Service
public class UserStatisticsCacheService {
    /**
     * 保存用户统计信息数据
     * 
     * @param userStatistics
     */
    public void saveUserStatistics(UserStatistics userStatistics) {
        String key = UserStatisticsCacheEnums.USER_STATISTICS_BY_ID.getKey() + userStatistics.getId();
        CacheKit.cache().set(key, userStatistics, ToolsConst.MONTH_SECOND);
    }

    /**
     * 获取用户统计信息数据
     * 
     * @param id
     *            记录ID
     * @return
     */
    public UserStatistics getUserStatistics(String id) {
        String key = UserStatisticsCacheEnums.USER_STATISTICS_BY_ID.getKey() + id;
        return CacheKit.cache().get(key, UserStatistics.class);
    }

    /**
     * 删除用户统计信息数据
     * 
     * @param id
     *            记录ID
     */
    public void delUserStatistics(String id) {
        String key = UserStatisticsCacheEnums.USER_STATISTICS_BY_ID.getKey() + id;
        CacheKit.cache().del(key);
    }
}
