package net.xplife.system.community.controller.chinese;

import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.core.BaseController;
import net.xplife.system.web.dto.ReturnDto;
import net.xplife.system.community.service.chinese.BaiduChineseBuService;
import net.xplife.system.community.service.chinese.FavoritesBuService;
import net.xplife.system.community.utils.Constant;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 学汉语控制器类
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/xhy")
public class ChineseController extends BaseController {

    @Autowired
    private BaiduChineseBuService chineseBuService;
    @Autowired
    private FavoritesBuService favoritesBuService;

    /**
     * 获取智能联想输入检索结果
     * @return
     */
    @RequestMapping(value = "/getAIWords")
    public ReturnDto getAIWords() {
        try {
            String word = this.getValue("word");
            String type = this.getValue("type");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getAIWords(word, type));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取笔画列表
     * @return
     */
    @RequestMapping(value = "/getStrokesList")
    public ReturnDto getStrokesList() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getStrokesList());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取搜索Tag标贴列表
     * @return
     */
    @RequestMapping(value = "/getQueryTags")
    public ReturnDto getQueryTags() {
        try {
            String type = this.getValue("type");
            Integer iType = null;
            if (type != null && type.trim().length() > 0) {
                iType = Integer.parseInt(type.trim());
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getQueryTags(iType));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取教材年级结构
     * @return
     */
    @RequestMapping(value = "/getGradeStructs")
    public ReturnDto getGradeStructs() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getGradeStructs());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取指定教材和年级的课文字词列表
     * @return
     */
    @RequestMapping(value = "/getBookLessonsList")
    public ReturnDto getBookLessonsList() {
        try {
            String bookVer = this.getValue("bookver");
            String grade = this.getValue("grade");
            String type = this.getValue("type");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getBookLessonsList(bookVer, grade, type));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取字详情
     * @return
     */
    @RequestMapping(value = "/getWordDetail")
    public ReturnDto getWordDetail() {
        try {
            String word = this.getValue("word");
            String userId = this.getValue("userId");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getWordDetail(word, userId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取字相关词组
     * @return
     */
    @RequestMapping(value = "/getWordRelatedTerms")
    public ReturnDto getWordRelatedTerms() {
        try {
            String word = this.getValue("word");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getWordRelatedTerms(word));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/getrecommended")
    public ReturnDto getRandomTwoRecommended() {
        try {
//            String size = this.getValue("size");
//            if (ToolsKit.isEmpty(size)){
//                size = "2";
//            }
            String userId = this.getValue("userid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getRandomTwoRecommended(userId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取字相关成语
     * @return
     */
    @RequestMapping(value = "/getWordRelatedIdioms")
    public ReturnDto getWordRelatedIdioms() {
        try {
            String word = this.getValue("word");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getWordRelatedIdioms(word));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取词语详情
     * @return
     */
    @RequestMapping(value = "/getTermDetail")
    public ReturnDto getTermDetail() {
        try {
            String term = this.getValue("term");
            String userId = this.getValue("userId");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getTermDetail(term, userId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取词语造句
     * @return
     */
    @RequestMapping(value = "/getTermSentences")
    public ReturnDto getTermSentences() {
        try {
            String term = this.getValue("term");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getTermSentences(term));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取诗词分类
     * @return
     */
    @RequestMapping(value = "/getPoemClass")
    public ReturnDto getPoemClass() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getPoemClass());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取诗词列表
     * @return
     */
    @RequestMapping(value = "/getPoemList")
    public ReturnDto getPoemList() {
        try {
            String dynasty = this.getValue("dynasty");
            String grade = this.getValue("grade");
            String author = this.getValue("author");
            String pageNo = this.getValue("pageno");
            Integer page = null;
            if (pageNo != null && pageNo.trim().length() > 0) {
                page = Integer.parseInt(pageNo.trim());
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getPoemList(dynasty, grade, author, page));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取诗词详情
     * @return
     */
    @RequestMapping(value = "/getPoemDetail")
    public ReturnDto getPoemDetail() {
        try {
            String pid = this.getValue("pid");
            String userId = this.getValue("userId");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getPoemDetail(pid, userId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取作文分类
     * @return
     */
    @RequestMapping(value = "/getCompositionClass")
    public ReturnDto getCompositionClass() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getCompositionClass());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取作文查询结果
     * @return
     */
    @RequestMapping(value = "/getCompositionQuery")
    public ReturnDto getCompositionQuery() {
        try {
            String word = this.getValue("word");
            String pageNo = this.getValue("pageno");
            Integer page = null;
            if (pageNo != null && pageNo.trim().length() > 0) {
                page = Integer.parseInt(pageNo.trim());
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getCompositionQuery(word, page));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取作文列表
     * @return
     */
    @RequestMapping(value = "/getCompositionList")
    public ReturnDto getCompositionList() {
        try {
            String grade = this.getValue("grade");
            String category = this.getValue("category");
            String tag = this.getValue("tag");
            String count = this.getValue("count");
            String pageNo = this.getValue("pageno");
            if (StringUtils.isBlank(count)) {
                count = "全部";
            }

            if (ToolsKit.isNotEmpty(grade)){
                grade = grade.replaceAll("全小学", "小学").replace("全初中","初中").replaceAll("全高中", "高中");
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getCompositionList(grade, category, tag, count, pageNo));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取作文详情
     * @return
     */
    @RequestMapping(value = "/getCompositionDetail")
    public ReturnDto getCompositionDetail() {
        try {
            String cid = this.getValue("cid");
            String userId = this.getValue("userId");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.getCompositionDetail(cid, userId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 累加作文打印统计次数
     * @return
     */
    @RequestMapping(value = "/incrCompositionPrintStat")
    public ReturnDto incrCompositionPrintStat() {
        try {
            String cid = this.getValue("cid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, chineseBuService.incrCompositionPrintStat(cid));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 添加收藏夹
     * @return
     */
    @RequestMapping(value = "/addFavorites")
    public ReturnDto addFavorites() {
        try {
            String type = this.getValue("type");
            String key = this.getValue("key");
            String pinyin = this.getValue("pinyin");
            String title = this.getValue("title");
            String content = this.getValue("content");
            String tags = this.getValue("tags");
            String userId = this.getValue("userId");
            int iType = 0;
            if (type != null && type.trim().length() > 0) {
                iType = Integer.parseInt(type.trim());
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, favoritesBuService.addFavorites(iType, key, pinyin, title, content, tags, userId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除收藏夹
     * @return
     */
    @RequestMapping(value = "/delFavorites")
    public ReturnDto delFavorites() {
        try {
            String id = this.getValue("id");
            favoritesBuService.delFavorites(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, id);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取用户收藏夹列表
     * @return
     */
    @RequestMapping(value = "/getUserFavoritesList")
    public ReturnDto getUserFavoritesList() {
        try {
            String userId = this.getValue("userId");
            String sort = this.getValue("sort");
            String type = this.getValue("type");
            Boolean sortByLetter = null;
            if (sort != null && sort.trim().equals("1")) {
                sortByLetter = true;
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, favoritesBuService.getUserFavoritesList(userId, sortByLetter, type));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取用户收藏夹ID
     * @return
     */
    @RequestMapping(value = "/getFavoritesId")
    public ReturnDto getFavoritesId() {
        try {
            String userId = this.getValue("userId");
            String key = this.getValue("key");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, favoritesBuService.getUserFavorites(userId, key));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

}
